import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  transformerDirectives,
  transformerVariantGroup,
  presetWind3
} from 'unocss'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  //  排除
  content: {
    pipeline: {
      exclude: ['node_modules']
    }
  },
  /** 自定义快捷方式 */
  shortcuts: [
    ['flex-center', 'flex items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
    ['flex-start', 'flex items-center justify-start'],
    ['flex-end', 'flex items-center justify-end']
  ],
  rules: [],
  //  presetIcons(),
  presets: [
    presetWind3(),
    presetAttributify(),
    presetTypography(),
    presetRemToPx({
      baseFontSize: 4 // 将默认的rem转成px 基准字体大小  官方的默认预设16（1单位 = 0.25rem）所以这里为4 为1：1
    }),
    presetIcons({
      // scale: 1.2,
      warn: true
    })
  ],
  variants: [
    {
      match: s => {
        if (s.startsWith('i-')) {
          return {
            matcher: s,
            selector: s => {
              return s.startsWith('.') ? `${s.slice(1)},${s}` : s
            }
          }
        }
      }
    }
  ],
  transformers: [transformerDirectives({ enforce: 'pre' }), transformerVariantGroup()]
})
