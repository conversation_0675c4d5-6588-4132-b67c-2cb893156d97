import request from '@/axios'
import type { FilterParams, SettdocEntity } from './types'

/**
 * @description: 分页获取列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getSettdocList = (
  data: FilterParams
): Promise<IResponse<BasePage<SettdocEntity[]>>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/getList',
    data
  })
}

/**
 * @description: 新增
 * @param {SettdocEntity} data
 * @return {*}
 */
export const saveSettdoc = (
  financeSettdocManageModels: SettdocEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/save',
    data: {
      financeSettdocManageModels
    }
  })
}

/**
 * @description: 更新
 * @param {SettdocEntity} data
 * @return {*}
 */
export const updateSettdoc = (data: SettdocEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/update',
    data
  })
}

/**
 * @description: 删除
 * @param {string} ids
 * @return {*}
 */
export const deleteSettdoc = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/delById',
    params: {
      ids
    }
  })
}

/**
 * @description: 生成结算单
 * @param {string} ids
 * @return {*}
 */
export const generateSettdoc = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/generate',
    params: {
      ids
    }
  })
}

/**
 * @description: 结算单确认
 * @param {string} ids
 * @return {*}
 */
export const confirmSettdoc = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/confirm',
    params: {
      ids
    }
  })
}
