export interface SettdocEntity {
  id?: string
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  status?: string
  delStatus?: number
  relatedNumber?: string
  customer?: string
  customerId?: string
  transportService?: string
  transportServiceId?: string
  documentNumber?: string
  costType?: string
  costTypeId?: string
  costUnit?: string
  costUnitId?: string
  unitCost?: string
  billCharge?: string
  amount?: string
  pendingPaymentAmount?: string
  currency?: string
  costDesc?: string
  remarks?: string
  paymentStatus?: string
  confirmTime?: string
  settlementTime?: string
  disbursementTime?: string
  paymentTime?: string
  customerEntity?: string
  customerIdEntity?: string
  type?: string
  warehouse?: string
  warehouseId?: string
  freezeAmount?: string
  adAmount?: string
}

export interface FilterParams extends SettdocEntity {
  pageSize: number
  pageNum: number
}
