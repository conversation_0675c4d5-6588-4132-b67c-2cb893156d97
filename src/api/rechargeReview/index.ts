import request from '@/axios'
import type { FilterParams, RechargeReviewParams } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const financeAccountRecharge = (
  data: FilterParams
): Promise<IResponse<BasePage<RechargeReviewParams[]>>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/getList',
    data
  })
}

/**
 * @description: 获取充值记录详情
 * @return {*}
 */
export const findRechargeRecordById = (
  data: RechargeReviewParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/findRechargeRecordById',
    data
  })
}

/**
 * @description: 充值
 * @param {AccountEntity} data
 * @return {*}
 */
export const recharge = (
  data: RechargeReviewParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/recharge',
    data
  })
}

/**
 * @description: 审核
 * @return {*}
 */
export const examine = (
  data: RechargeReviewParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/examine',
    data
  })
}
