import request from '@/axios'
import type { FilterParams, AttributeEntity } from './types'

/**
 * @description: 分页获取列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getAttributeList = (
  data: FilterParams
): Promise<IResponse<BasePage<AttributeEntity[]>>> => {
  return request.post({
    url: '/api/management/productAttribute/getProductAttributeList',
    data
  })
}

/**
 * @description: 新增
 * @param {AttributeEntity} data
 * @return {*}
 */
export const saveAttribute = (
  data: AttributeEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productAttribute/saveProductAttributeInfo',
    data
  })
}

/**
 * @description: 更新
 * @param {AttributeEntity} data
 * @return {*}
 */
export const updateAttribute = (
  data: AttributeEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productAttribute/updateProductAttributeInfo',
    data
  })
}

/**
 * @description: 删除
 * @param {AttributeEntity} data
 * @return {*}
 */
export const deleteAttribute = (
  data: AttributeEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productAttribute/deleteProductAttribute',
    data
  })
}
