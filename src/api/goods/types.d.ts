// 发布类型
export interface ReleaseTypeEntity {
  id?: string
  status?: string
  type?: string
  warehouseAddress?: string
  category?: string
}

// 商品基本信息
export interface BasicEntity {
  id?: string
  status?: string
  productCode?: string
  productName?: string
  productCnDeclare?: string
  productEnDeclare?: string
  productBrandName?: string
  productModel?: string
  productCnMaterial?: string
  productEnMaterial?: string
  productCnPurpose?: string
  productEnPurpose?: string
  productPrice?: string
  customsCode?: string
  goodsAttr?: string
  goodsType?: string
  isElectricity?: string
  productReviewUrl?: string
  productBarCode?: string
  productReleaseTypeId?: string
}

// 分销信息
export interface DistributionBasicEntity {
  id?: string
  status?: string
  productHostGraphUrl?: string
  productDimensionUrl?: string
  productCnTitle?: string
  productEnTitle?: string
  productCategoryId?: string
  productDetails?: string
  productReleaseTypeId?: string
  productBasicId?: string
}

// 规格信息
export interface SpecificationsBasicEntity {
  id?: string
  status?: string
  productBasicId?: string
  productAttr?: string[]
  productAttrOne?: string
  productAttrTwo?: string
  productSkuCode?: string
  productSkuUrl?: string[]
  productLength?: string
  productWidth?: string
  productHeight?: string
  productWeight?: string
  productSupplyPrice?: string
  productReleaseTypeId?: string
  productBasicId?: string
  productDistributionId?: string
}

export interface ProductListFilter extends SpecificationsBasicEntity {
  pageNum?: number
  pageSize?: number
}

export interface SpecificationsBasicSaveModel {
  productSpeBasic?: SpecificationsBasicEntity[]
  productAttrAll?: string | undefined
  productAttrOneAll?: string | undefined
  productAttrTwoAll?: string | undefined
}
