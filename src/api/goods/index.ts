import request from '@/axios'
import type {
  BasicEntity,
  DistributionBasicEntity,
  ProductListFilter,
  ReleaseTypeEntity,
  SpecificationsBasicEntity,
  SpecificationsBasicSaveModel
} from './types'

/**
 * @description: 新增发布类型
 * @param {ReleaseTypeEntity} data
 * @return {*}
 */
export const saveReleaseType = (
  data: ReleaseTypeEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productReleaseType/save',
    data
  })
}

/**
 * @description: 更新发布类型
 * @param {ReleaseTypeEntity} data
 * @return {*}
 */
export const updateReleaseType = (
  data: ReleaseTypeEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productReleaseType/update',
    data
  })
}

/**
 * @description: 获取发布类型详情
 * @param {string} id
 * @return {*}
 */
export const getByIdReleaseType = (id: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/productReleaseType/getById',
    params: {
      id
    }
  })
}

/**
 * @description: 新增商品基本信息
 * @param {BasicEntity} data
 * @return {*}
 */
export const saveBasic = (data: BasicEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productBasic/save',
    data
  })
}

/**
 * @description: 更新商品基本信息
 * @param {BasicEntity} data
 * @return {*}
 */
export const updateBasic = (data: BasicEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productBasic/update',
    data
  })
}

/**
 * @description: 获取商品基本信息详情
 * @param {string} id
 * @return {*}
 */
export const getByIdBasic = (id: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/productBasic/getById',
    params: {
      id
    }
  })
}

/**
 * @description: 新增分销信息
 * @param {DistributionBasicEntity} data
 * @return {*}
 */
export const saveDistributionBasic = (
  data: DistributionBasicEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productDistributionBasic/save',
    data
  })
}

/**
 * @description: 更新分销信息
 * @param {DistributionBasicEntity} data
 * @return {*}
 */
export const updateDistributionBasic = (
  data: DistributionBasicEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productDistributionBasic/update',
    data
  })
}

/**
 * @description: 获取分销信息详情
 * @param {string} id
 * @return {*}
 */
export const getByIdDistributionBasic = (
  id: string
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/productDistributionBasic/getById',
    params: {
      id
    }
  })
}

/**
 * @description: 新增规格信息
 * @param {SpecificationsBasicEntity} data
 * @return {*}
 */
export const saveSpecificationsBasic = (
  data: SpecificationsBasicSaveModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productSpecificationsBasic/save',
    data
  })
}

/**
 * @description: 更新规格信息
 * @param {SpecificationsBasicEntity} data
 * @return {*}
 */
export const updateSpecificationsBasic = (
  data: SpecificationsBasicEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productSpecificationsBasic/update',
    data
  })
}

/**
 * @description: 获取规格信息详情
 * @param {string} id
 * @return {*}
 */
export const getByIdSpecificationsBasic = (
  id: string
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/productSpecificationsBasic/getById',
    params: {
      id
    }
  })
}

/**
 * @description: 分页查询产品列表
 * @return {*}
 */
export const getProductList = (
  data: ProductListFilter
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productBasic/getProductList',
    data
  })
}
