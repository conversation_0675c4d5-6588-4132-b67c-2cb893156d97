import request from '@/axios'
import type { SellerFilterParams } from './types'

/**
 * @description: 卖家查询分页
 * @param {SellerFilterParams} data
 * @return {*}
 */
export const findSellerBankAccountList = (
  data: SellerFilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/findSellerBankAccountList',
    data
  })
}

/**
 * @description: 卖家更新状态
 * @return {*}
 */
export const SellerChangeStatus = (
  data: SellerFilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/changeStatus',
    data
  })
}

/**
 * @description: 新增商家账户信息
 * @return {*}
 */
export const saveSellerBankAccountInfo = (
  data: SellerFilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/saveSellerBankAccountInfo',
    data
  })
}

/**
 * @description: 更增商家账户信息
 * @return {*}
 */
export const updateSellerBankAccountInfo = (
  data: SellerFilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/updateSellerBankAccountInfo',
    data
  })
}
