export interface AccountEntity {
  id?: string
  status?: string
  customerCode?: string
  rechargeAccount?: string
  rechargeAccountId?: string
  rechargeMethod?: string
  receBank?: string
  receBankName?: string
  payBankName?: string
  payNumber?: string
  rechargeAmount?: string
  remarks?: string
  voucherUrl?: string
  examineStatus?: number
  msg?: string
}

export interface FilterParams extends AccountEntity {
  pageSize: number
  pageNum: number
}
