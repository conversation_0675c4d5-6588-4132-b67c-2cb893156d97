import request from '@/axios'
import type { FilterParams, AccountEntity } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getAccountRechargeList = (
  data: FilterParams
): Promise<IResponse<BasePage<AccountEntity[]>>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/getList',
    data
  })
}

/**
 * @description: 新增
 * @return {*}
 */
export const saveAccountRecharge = (
  data: AccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/save',
    data
  })
}

/**
 * @description: 更新
 * @param {AccountEntity} data
 * @return {*}
 */
export const updateAccountRecharge = (
  data: AccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/update',
    data
  })
}

/**
 * @description: 删除
 * @return {*}
 */
export const delByIdAccountRecharge = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeAccountRecharge/delById',
    params: { idList }
  })
}

/**
 * @description: 启用
 * @param {string} idList
 * @return {*}
 */
export const enableAccountRecharge = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/enable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 禁用
 * @param {string} idList
 * @return {*}
 */
export const disableAccountRecharge = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/disable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 审核
 * @param {AccountEntity} data
 * @return {*}
 */
export const examineAccountRecharge = (
  data: AccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeAccountRecharge/examine',
    data
  })
}
