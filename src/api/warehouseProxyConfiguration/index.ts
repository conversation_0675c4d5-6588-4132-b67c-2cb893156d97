import request from '@/axios'
import type {
  WarehouseAgentModel,
  WarehouseAgentVo,
  OverseasWareHouseModel,
  WarehouseMappingModel,
  WarehouseProductModel,
  IPageOverseasWareHouseVo,
  IPageWarehouseMappingVo,
  IPageWarehouseProductVo,
  DeleteParams
} from './types'

/**
 * @description: 查询仓库代理信息
 * @param {WarehouseAgentModel} data
 * @return {*}
 */
export const findWarehouseAgentList = (
  data: WarehouseAgentModel
): Promise<IResponse<WarehouseAgentVo[]>> => {
  return request.post({
    url: '/api/management/warehouseAgent/findWarehouseAgentList',
    data
  })
}

/**
 * @description: 保存仓库代理信息
 * @param {WarehouseAgentModel} data
 * @return {*}
 */
export const saveWarehouseAgent = (
  data: WarehouseAgentModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/saveWarehouseAgent',
    data
  })
}

/**
 * @description: 修改仓库代理信息
 * @param {WarehouseAgentModel} data
 * @return {*}
 */
export const updateWarehouseAgent = (
  data: WarehouseAgentModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/updateWarehouseAgent',
    data
  })
}

/**
 * @description: 获取第三方仓库渠道关系
 * @param {OverseasWareHouseModel} data
 * @return {*}
 */
export const getOverseasWarehouseChannel = (
  data: OverseasWareHouseModel
): Promise<IResponse<IPageOverseasWareHouseVo>> => {
  return request.post({
    url: '/api/management/warehouseAgent/getOverseasWarehouseChannel',
    data
  })
}

/**
 * @description: 保存第三方仓库渠道关系
 * @param {OverseasWareHouseModel} data
 * @return {*}
 */
export const saveWarehouseChannel = (
  data: OverseasWareHouseModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/saveWarehouseChannel',
    data
  })
}

/**
 * @description: 修改第三方仓库渠道关系
 * @param {OverseasWareHouseModel} data
 * @return {*}
 */
export const updateWarehouseChannel = (
  data: OverseasWareHouseModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/updateWarehouseChannel',
    data
  })
}

/**
 * @description: 获取第三方仓库映射关系
 * @param {WarehouseMappingModel} data
 * @return {*}
 */
export const getOverseasWarehouseMapping = (
  data: WarehouseMappingModel
): Promise<IResponse<IPageWarehouseMappingVo>> => {
  return request.post({
    url: '/api/management/warehouseAgent/getOverseasWarehouseMapping',
    data
  })
}

/**
 * @description: 保存第三方仓库映射关系
 * @param {WarehouseMappingModel} data
 * @return {*}
 */
export const saveWarehouseMapping = (
  data: WarehouseMappingModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/saveWarehouseMapping',
    data
  })
}

/**
 * @description: 删除第三方仓库映射关系
 * @param {DeleteParams} data
 * @return {*}
 */
export const deleteWarehouseMapping = (
  data: DeleteParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/deleteWarehouseMapping',
    data
  })
}

/**
 * @description: 获取第三方仓库产品关系
 * @param {WarehouseProductModel} data
 * @return {*}
 */
export const getOverseasWarehouseProduct = (
  data: WarehouseProductModel
): Promise<IResponse<IPageWarehouseProductVo>> => {
  return request.post({
    url: '/api/management/warehouseAgent/getOverseasWarehouseProduct',
    data
  })
}

/**
 * @description: 保存第三方仓库产品关系
 * @param {WarehouseProductModel} data
 * @return {*}
 */
export const saveWarehouseProduct = (
  data: WarehouseProductModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/saveWarehouseProduct',
    data
  })
}

/**
 * @description: 删除第三方仓库产品关系
 * @param {DeleteParams} data
 * @return {*}
 */
export const deleteWarehouseProduct = (
  data: DeleteParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/warehouseAgent/deleteWarehouseProduct',
    data
  })
}
