/**
 * @description: 仓库代理配置模块类型定义
 */

// 基础分页参数
export interface BasePageParams {
  pageSize: number
  pageNum: number
  order?: string
}

// 基础公共字段
export interface BaseWarehouseFields {
  whCode?: string // 主仓编码
  whNameCn?: string // 主仓名称
  status?: string // 状态
  agentId?: string // 仓库代理ID
}

// 产品相关公共字段
export interface BaseProductFields {
  productName?: string // 产品名称
  sku?: string // SKU
  sysProductName?: string // 系统产品名称
  sysSku?: string // 系统SKU
}

// 渠道相关公共字段
export interface BaseChannelFields {
  channelCode?: string // 渠道编码
  channelName?: string // 渠道名称
  sysChannelName?: string // 系统渠道
}

// 仓库相关公共字段
export interface BaseWarehouseInfoFields {
  warehouseCode?: string // 仓库编码
  warehouseName?: string // 仓库名称
}

// 通用分页响应类型
export interface IPageResponse<T> {
  size: number
  current: number
  pages: number
  total: number
  records: T[]
}

// 仓库代理信息相关类型
export interface WarehouseAgentModel extends BasePageParams {
  id?: string
  warehouseType?: string // 主仓系统类型
  warehouseName?: string // 自定义主仓名称
  info1?: string // 所需信息1
  info2?: string // 所需信息2
}

export interface WarehouseAgentVo {
  id?: string
  warehouseType?: string // 主仓系统类型
  warehouseName?: string // 自定义主仓名称
  info1?: string // 所需信息1
  info2?: string // 所需信息2
  updateUser?: string // 更新人
  updateTime?: string // 更新时间
}

// 第三方仓库相关类型
export interface OverseasWareHouseModel
  extends BasePageParams,
    BaseWarehouseFields,
    BaseChannelFields,
    BaseProductFields,
    BaseWarehouseInfoFields {
  customer?: string // 客户
}

export interface OverseasWareHouseVo
  extends BaseWarehouseFields,
    BaseChannelFields,
    BaseProductFields,
    BaseWarehouseInfoFields {
  customer?: string // 客户
}

// 仓库映射相关类型
export interface WarehouseMappingModel
  extends BasePageParams,
    BaseWarehouseFields,
    BaseWarehouseInfoFields {
  id?: string
}

export interface WarehouseMappingVo
  extends BaseWarehouseFields,
    BaseWarehouseInfoFields {
  id?: string // 主键ID
}

// 仓库产品关系相关类型
export interface WarehouseProductModel
  extends BasePageParams,
    BaseWarehouseFields,
    BaseProductFields {
  customer?: string
}

export interface WarehouseProductVo
  extends BaseWarehouseFields,
    BaseProductFields {}

// 分页响应类型别名
export type IPageOverseasWareHouseVo = IPageResponse<OverseasWareHouseVo>
export type IPageWarehouseMappingVo = IPageResponse<WarehouseMappingVo>
export type IPageWarehouseProductVo = IPageResponse<WarehouseProductVo>

// 删除操作参数类型
export interface DeleteParams {
  whCode: string // 要删除的ID列表
  agentId: string // 要删除的ID列表
  id?: string
}
