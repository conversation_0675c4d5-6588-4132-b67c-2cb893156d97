import request from '@/axios'
import type { FilterParams, BankEntity } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getBankAccountList = (
  data: FilterParams
): Promise<IResponse<BasePage<BankEntity[]>>> => {
  return request.post({
    url: '/api/management/financeBankAccount/getList',
    data
  })
}

/**
 * @description: 新增
 * @return {*}
 */
export const saveBankAccount = (data: BankEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeBankAccount/save',
    data
  })
}

/**
 * @description: 更新
 * @param {BankEntity} data
 * @return {*}
 */
export const updateBankAccount = (
  data: BankEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeBankAccount/update',
    data
  })
}

/**
 * @description: 删除
 * @return {*}
 */
export const delByIdBankAccount = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeBankAccount/delById',
    params: { idList }
  })
}

/**
 * @description: 启用
 * @param {string} idList
 * @return {*}
 */
export const enableBankAccount = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeBankAccount/enable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 禁用
 * @param {string} idList
 * @return {*}
 */
export const disableBankAccount = (
  idList: string[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeBankAccount/disable',
    params: {
      idList: idList.join(',')
    }
  })
}
