import request from '@/axios'
import type { LoginResponse, LoginRequest } from './types'

/**
 * @description: 登录
 * @param {any} data
 * @return {*}
 */
export const loginApi = (
  data: LoginRequest
): Promise<IResponse<LoginResponse>> => {
  return request.post({ url: '/api/management/base/login', data })
}

/**
 * @description: 登出
 * @param {any} data
 * @return {*}
 */
export const logOutApi = (): Promise<IResponse<any>> => {
  return request.post({ url: '/api/management/logout' })
}
