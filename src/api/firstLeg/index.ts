import request from '@/axios'
import type {
  FilterParams,
  FirstVesselEntity,
  FirstVesselVo,
  PickingGoodsData,
  PickingGoodsModel
} from './types'

/**
 * @description: 新增运单
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const saveFirstVessel = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/saveFirstVessel',
    data
  })
}

/**
 * @description: 更新运单
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const updateFirstVessel = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/updateFirstVessel',
    data
  })
}

/**
 * @description: 获取运单列表
 * @return {*}
 */
export const getFirstVesselList = (
  data: FilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/getFirstVesselList',
    data
  })
}

/**
 * @description: 拣货
 * @return {*}
 */
export const pickingGoods = (
  data: PickingGoodsModel[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/pickingGoods',
    data
  })
}

/**
 * @description: 保存拣货数据
 * @return {*}
 */
export const savePickingGoodsData = (
  data: PickingGoodsData[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/savePickingGoodsData',
    data
  })
}

/**
 * @description: 批量更新服务商测量数据
 * @return {*}
 */
export const batchUpdateServiceProviderMeasureData = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateServiceProviderMeasureData',
    data
  })
}

/**
 * @description: 批量更新头程信息
 * @return {*}
 */
export const batchUpdateFirstVessel = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateFirstVessel',
    data
  })
}

/**
 * @description: 批量更新头程信息并使用服务商测量数据
 * @return {*}
 */
export const batchUpdateFirstVesselAndUseServiceProviderMeasureData = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateFirstVesselAndUseServiceProviderMeasureData',
    data
  })
}

/**
 * @description: 批量更新并使用服务商轨迹
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchUpdateAndUseServiceProviderTrack = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateAndUseServiceProviderTrack',
    data
  })
}

/**
 * @description: 批量更新服务商主单号并使用服务商测量数据
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchUpdateAndUseServiceProviderExpressMainOrderNumber = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateAndUseServiceProviderExpressMainOrderNumber',
    data
  })
}

/**
 * @description: 批量实时同步服务商新轨迹
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchRealTimeSyncServiceProviderNewTrack = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchRealTimeSyncServiceProviderNewTrack',
    data
  })
}

/**
 * @description: 批量推送运单
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchPushFirstVessel = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchPushFirstVessel',
    data
  })
}

/**
 * @description: 批量再次推送运单
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchPushFirstVesselAgain = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchPushFirstVesselAgain',
    data
  })
}

/**
 * @description: 批量导入轨迹
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchImportTrack = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchImportTrack',
    data
  })
}

/**
 * @description: 批量编辑快递主单号
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchEditExpressMainOrderNumber = (
  data: FirstVesselEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchEditExpressMainOrderNumber',
    data
  })
}

/**
 * @description: 批量编辑费用(新增、删除费用)
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchEditCost = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchEditCost',
    data
  })
}

/**
 * @description: 批量删除内部备注
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchDeleteInternalRemarks = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchDeleteInternalRemarks',
    data
  })
}

/**
 * @description: 批量删除客户备注
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchDeleteCustomerRemarks = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchDeleteCustomerRemarks',
    data
  })
}

/**
 * @description: 批量取消运单
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchCancelFirstVessel = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchCancelFirstVessel',
    data
  })
}

/**
 * @description: 批量调整运单状态(转运中、已签收、取件、取消运单)
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchAdjustFirstVesselStatus = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchAdjustFirstVesselStatus',
    data
  })
}

/**
 * @description: 批量添加轨迹
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchAddTrack = (
  data: FirstVesselEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchAddTrack',
    data
  })
}

/**
 * @description: 批量添加备注(客户备注、内部备注)
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchAddRemarks = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchAddRemarks',
    data
  })
}

/**
 * @description: 根据id获取头程运单信息
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const getFirstVesselById = (data: {
  id: string
}): Promise<IResponse<FirstVesselVo>> => {
  return request.post({
    url: '/api/management/firstVessel/getFirstVesselById',
    data
  })
}

/**
 * @description: 获取第三方箱信息， 服务商收货数据
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const getThridBoxList = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/getThridBoxList',
    data
  })
}

/**
 * @description: 获取系统箱信息，系统收货数据
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const getSystemBoxList = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/getSystemBoxList',
    data
  })
}

/**
 * @description: 获取头程运单第三方轨迹列表
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const getFirstVesselThirdTrajectoryList = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/getFirstVesselThirdTrajectoryList',
    data
  })
}

/**
 * @description: 获取头程运单系统轨迹列表
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const getFirstVesselSystemTrajectoryList = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/getFirstVesselSystemTrajectoryList',
    data
  })
}

/**
 * @description: 批量更新轨迹
 * @return {*}
 */
export const batchUpdateTrack = (
  data: FirstVesselEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchUpdateTrack',
    data
  })
}

/**
 * @description: 批量删除轨迹
 * @return {*}
 */
export const batchRemoveTrack = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchRemoveTrack',
    data
  })
}

/**
 * @description: 批量查看收货数据
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchViewReceiptData = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchViewReceiptData',
    data
  })
}

/**
 * @description: 批量查看统计数据
 * @param {FirstVesselEntity} data
 * @return {*}
 */
export const batchViewStatisticsData = (
  data: FirstVesselEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVessel/batchViewStatisticsData',
    data
  })
}
