export interface BoxInfoCommodity {
  commodityId?: string
  commodityCount?: string
  [key: string]: any
}

export interface BoxInfo {
  boxNum?: number
  boxBarCode?: string
  boxBarCodeRule?: string
  systemBoxLength?: string
  systemBoxWidth?: string
  systemBoxHeight?: string
  systemBoxWeight?: string
  commodity?: CommodityVo[]
}

export interface FirstVesselEntity {
  id?: string
  customer?: string
  customerWaybillNumber?: string
  deliveryWarehouse?: string
  destinationType?: string
  shippingService?: string
  destination?: string
  declarationMethod?: string
  taxationMethod?: string
  storageType?: string
  purchaseInsurance?: string
  customerRemarks?: string
  internalRemarks?: string
  boxInfo?: BoxInfo[]
  onlyVessel?: boolean
  vatNo?: string
  vatAddress?: string
  eoriNo?: string
  eoriAddress?: string
  containerType?: string
  recipient?: string
  addressOne?: string
  addressTwo?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  phone?: string
  email?: string
  track?: string
  trackTime?: string
  status?: string
  type?: string
  /** 承运商 */
  carrier?: string
  /** 单号 */
  waybillNumber?: string
  expressMainOrder?: string
  ids?: string[]
}

export interface FilterParams extends FirstVesselEntity {
  pageSize?: number
  pageNum?: number
}

/**
 * 拣货模型
 */
export interface PickingGoodsModel {
  /** 唯一标识 */
  id?: string
  /** 长 */
  length?: number
  /** 宽 */
  width?: number
  /** 高 */
  height?: number
  /** 重量 */
  weight?: number
  /** 承运商 */
  carrier?: string
  /** 单号 */
  waybillNumber?: string
  /** 运输服务 */
  shippingService?: string
  /** 最大实际重量 */
  maxActualWeight?: number
  /** 最大货品重量 */
  maxWeightProduct?: number
  /** 最大材料重量 */
  maxMaterialWeight?: number
  /** 最大体积 */
  maxVolume?: number
  /** 超重费用 */
  heavyCharges?: number
  /** 总超重费用 */
  totalHeavyCharges?: number
}

/**
 * 商品Vo
 */
export interface CommodityVo {
  /** 商品id */
  commodityId?: string
  /** 商品数量 */
  commodityCount?: number
  [key: string]: any
}

/**
 * 箱子Vo
 */
export interface BoxVo {
  /** 箱子数量 */
  boxNum?: number
  /** 自定义箱条码 */
  boxBarCode?: string
  /** 箱条码规则 */
  boxBarCodeRule?: string
  /** 箱子长-系统/客户 */
  systemBoxLength?: string
  /** 箱子宽-系统/客户 */
  systemBoxWidth?: string
  /** 箱子高-系统/客户 */
  systemBoxHeight?: string
  /** 箱子重量-系统/客户 */
  systemBoxWeight?: string
  /** 箱子长-拣货 */
  pickBoxLength?: number
  /** 箱子宽-拣货 */
  pickBoxWidth?: number
  /** 箱子高-拣货 */
  pickBoxHeight?: number
  /** 箱子重量-拣货 */
  pickBoxWeight?: number
  /** 箱子长-第三方 */
  thirdBoxLength?: number
  /** 箱子宽-第三方 */
  thirdBoxWidth?: number
  /** 箱子高-第三方 */
  thirdBoxHeight?: number
  /** 箱子重量-第三方 */
  thirdBoxWeight?: number
  /** 商品 */
  commodity?: CommodityVo[]
}

/**
 * 头程运单Vo
 */
export interface FirstVesselVo {
  /** ID */
  id?: string
  /** 客户 */
  customer?: string
  /** 系统运单号 */
  systemWaybillNumber?: string
  /** 客户运单号 */
  customerWaybillNumber?: string
  /** 交货仓库 */
  deliveryWarehouse?: string
  /** 目的地类型 */
  destinationType?: string
  /** 运输服务 */
  shippingService?: string
  /** 目的地 */
  destination?: string
  /** 报关方式 */
  declarationMethod?: string
  /** 交税方式 */
  taxationMethod?: string
  /** 入库类型 */
  storageType?: string
  /** 购买保险 */
  purchaseInsurance?: string
  /** 客户备注 */
  customerRemarks?: string
  /** 内部备注 */
  internalRemarks?: string
  /** 创建用户 */
  createUser?: string
  /** 更新用户 */
  updateUser?: string
  /** 装运ID */
  shipmentId?: string
  /** 服务提供商 */
  serviceProvider?: string
  /** 系统箱子数量 */
  sysBoxNum?: number
  /** 实际箱子数量 */
  actualBoxNum?: number
  /** 实际重量 */
  actualWeight?: number
  /** 体积重量 */
  volumeWeight?: number
  /** 尺寸 */
  dimensional?: number
  /** 可变重量 */
  changeableWeight?: number
  /** 成本 */
  cost?: number
  /** 轨迹 */
  track?: string
  /** 供应商 */
  supplier?: string
  /** 跟踪号 */
  trackingNumber?: string
  /** 国家 */
  country?: string
  /** 商品名称 */
  commodityName?: string
  /** 商品属性 */
  commodityAttribute?: string
  /** 创建时间 */
  createTime?: string
  /** 状态时间 */
  statusTime?: string
  /** 箱子信息 */
  boxInfo?: BoxVo[]
  /** VAT号 */
  vatNo?: string
  /** VAT地址 */
  vatAddress?: string
  /** EORI号 */
  eoriNo?: string
  /** EORI地址 */
  eoriAddress?: string
  /** 货柜类型 */
  containerType?: string
  /** 收件人 */
  recipient?: string
  /** 地址一 */
  addressOne?: string
  /** 地址二 */
  addressTwo?: string
  /** 城市 */
  city?: string
  /** 州/省 */
  state?: string
  /** 邮编 */
  zipCode?: string
  /** 电话 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 只有船只 */
  onlyVessel?: boolean
  /** 轨迹时间 */
  trackTime?: string
  /** 状态 */
  status?: string
}

/**
 * 头程运单轨迹Vo
 */
export interface FirstVesselTrajectoryVo {
  /** ID */
  id?: string
  /** 运单ID */
  vesselId?: string
  /** 轨迹信息 */
  trackMessage?: string
  /** 创建时间 */
  createTime?: string
  /** 轨迹时间 */
  trackTime?: string
  /** 轨迹 */
  track?: string
}

/**
 * 拣货商品数据
 */
export interface PickingGoodsData {
  /**
   * 箱id
   */
  id?: string
  /**
   * 实重
   */
  actualWeight?: number
  /**
   * 重量积
   */
  weightProduct?: number
  /**
   * 材积重
   */
  materialWeight?: number
  /**
   * 体积
   */
  volume?: number
  /**
   * 单箱收费重
   */
  heavyCharges?: number
  /**
   * 总收费重
   */
  totalHeavyCharges?: number
  /**
   * 箱子长-拣货
   */
  pickBoxLength?: number
  /**
   * 箱子宽-拣货
   */
  pickBoxWidth?: number
  /**
   * 箱子高-拣货
   */
  pickBoxHeight?: number
  /**
   * 箱子重量-拣货
   */
  pickBoxWeight?: number
  /**
   * 运单id
   */
  waybillId?: string
}
