import request from '@/axios'
import type { OrgEntity } from './types'

/**
 * @description: 获取机构树
 * @return {*}
 */
export const getOrgTree = (): Promise<IResponse<any>> => {
  return request.get({ url: '/api/management/org/getOrgTree' })
}

/**
 * @description: 新增机构
 * @param {OrgEntity} data
 * @return {*}
 */
export const saveOrgInfo = (data: OrgEntity): Promise<IResponse<any>> => {
  return request.post({ url: '/api/management/org/saveOrgInfo', data })
}

/**
 * @description: 更新机构
 * @param {OrgEntity} data
 * @return {*}
 */
export const updateOrgInfo = (data: OrgEntity): Promise<IResponse<any>> => {
  return request.post({ url: '/api/management/org/updateOrgInfo', data })
}

/**
 * @description: 删除机构
 * @param {string} orgId
 * @return {*}
 */
export const deleteOrgInfo = (orgId: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/org/deleteOrgInfo',
    params: { orgId }
  })
}

/**
 * @description: 获取下拉选项机构树
 * @return {*}
 */
export const getOrgRoleTree = (): Promise<IResponse<any>> => {
  return request.get({ url: '/api/management/org/getOrgRoleTree' })
}
