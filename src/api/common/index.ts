import request from '@/axios'

/**
 * @description: 根据字典code获取字典详情
 * @param {string} dictCode
 * @return {*}
 */
export const getDictItemList = (
  dictCode: string
): Promise<IResponse<DictItemEntity[]>> => {
  return request.get({
    url: '/api/management/dict/getDictItemList',
    params: {
      dictCode
    }
  })
}

/**
 * @description: 获取所有卖家(客户)
 * @return {*}
 */
export const findSellerAll = (): Promise<IResponse<CompanyInfo[]>> => {
  return request.get({
    url: '/api/management/sellerManagement/findSellerAll'
  })
}

export const upload = () => {
  return request.post({
    url: '/api/management/upload',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    responseType: 'blob'
  })
}

/**
 * @description: 头程运输服务设置
 * @return {*}
 */
export const getAllFirstVesselSettingList = (): Promise<IResponse<any>> => {
  return request.get({
    // url: '/api/management/firstVessel/getFirstVesselList',
    url: '/api/management/firstVesselSetting/getAllFirstVesselSettingList'
  })
}

export const getFirstVesselSettingListByDestinationType = (
  destinationType: string
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVesselSetting/getFirstVesselSettingListByDestinationType',
    data: {
      destinationType
    }
  })
}

export const getServiceProviderAll = (): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/findAll',
    data: {}
  })
}

/**
 * @description: 获取全部仓库管理列表
 * @param {*} Promise
 * @return {*}
 */
export const baseWarehouseManageFindAll = (
  warehouseType?: string
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/findAll',
    data: {
      warehouseType
    }
  })
}

/**
 * @description: 获取地点管理全部列表
 * @return {*}
 */
export const baseLocationManageFindAll = (
  warehouseType?: string
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseLocationManage/findAll',
    data: {
      warehouseType
    }
  })
}

/**
 * @description: 获取服务商列表
 * @param {*} Promise
 * @return {*}
 */
export const getServiceProvider = (): Promise<IResponse<BaseOption[]>> => {
  return request.post({
    url: '/api/management/firstVessel/getServiceProvider'
  })
}

/**
 * @description: 获取收款账户列表
 * @return {*}
 */
export const findReceivingAccountList = (
  paymentMethod: string
): Promise<IResponse<any[]>> => {
  return request.post({
    url: '/api/management/receivingAccount/findReceivingAccountList',
    data: {
      paymentMethod
    }
  })
}

/**
 * @description: 获取商家账户列表
 * @return {*}
 */
export const getSellerBankAccountList = (
  seller: string
): Promise<IResponse<any[]>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/getSellerBankAccountList',
    data: {
      seller
    }
  })
}

/**
 * @description: 获取所有商品，用于下拉选项
 * @param {*} Promise
 * @return {*}
 */
export const findProductInfoList = (): Promise<IResponse<any[]>> => {
  return request.post({
    url: '/api/management/productBasic/findProductInfoList',
    data: {}
  })
}
