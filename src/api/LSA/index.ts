import request from '@/axios'
import type {
  LogisticsProviderEntity,
  LogisticsProviderFilterParams,
  LogisticsProviderVo,
  LogisticsProviderStatusParams
} from './types'

/**
 * @description: 分页获取物流商列表
 * @param {LogisticsProviderFilterParams} data - 查询参数
 * @return {Promise<IResponse<BasePage<LogisticsProviderVo[]>>>} 物流商分页数据
 */
export const findLogisticsProviderList = (
  data: LogisticsProviderFilterParams
): Promise<IResponse<BasePage<LogisticsProviderVo[]>>> => {
  return request.post({
    url: '/api/management/logisticsProvider/findLogisticsProviderList',
    data
  })
}

/**
 * @description: 根据ID查询物流商信息
 * @param {string} id - 物流商ID
 * @return {Promise<IResponse<LogisticsProviderVo>>} 物流商详细信息
 */
export const findLogisticsProviderInfo = (
  id: string
): Promise<IResponse<LogisticsProviderVo>> => {
  return request.post({
    url: '/api/management/logisticsProvider/findLogisticsProviderInfo',
    data: { id }
  })
}

/**
 * @description: 保存物流商信息（新增）
 * @param {LogisticsProviderEntity} data - 物流商信息
 * @return {Promise<IResponse<any>>} 操作结果
 */
export const saveLogisticsProvider = (
  data: LogisticsProviderEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/logisticsProvider/saveLogisticsProvider',
    data
  })
}

/**
 * @description: 修改物流商信息
 * @param {LogisticsProviderEntity} data - 物流商信息
 * @return {Promise<IResponse<any>>} 操作结果
 */
export const updateLogisticsProvider = (
  data: LogisticsProviderEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/logisticsProvider/updateLogisticsProvider',
    data
  })
}

/**
 * @description: 更新物流商状态
 * @param {LogisticsProviderStatusParams} data - 状态更新参数
 * @return {Promise<IResponse<any>>} 操作结果
 */
export const changeLogisticsProviderStatus = (
  data: LogisticsProviderStatusParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/logisticsProvider/changeLogisticsProviderStatus',
    data
  })
}
