/**
 * 物流商管理相关类型定义
 */

/**
 * 物流商实体接口
 */
export interface LogisticsProviderEntity {
  /** 主键 */
  id?: string
  /** 物流商名称 */
  logisticsProviderName?: string
  /** 账号 */
  account?: string
  /** 备注 */
  remark?: string
  /** 扩展字段1 */
  info1?: string
  /** 扩展字段2 */
  info2?: string
  /** 扩展字段3 */
  info3?: string
  /** 扩展字段4 */
  info4?: string
  /** 仓库 */
  warehouse?: string[]
  /** 状态 */
  status?: string
}

/**
 * 物流商查询参数接口
 */
export interface LogisticsProviderFilterParams extends LogisticsProviderEntity {
  /** 页面大小 */
  pageSize: number
  /** 页码 */
  pageNum: number
  /** 排序 */
  order?: string
}

/**
 * 物流商响应数据接口
 */
export interface LogisticsProviderVo {
  /** 主键 */
  id: string
  /** 物流商名称 */
  logisticsProviderName: string
  /** 账号 */
  account: string
  /** 备注 */
  remark: string
  /** 扩展字段1 */
  info1: string
  /** 扩展字段2 */
  info2: string
  /** 扩展字段3 */
  info3: string
  /** 扩展字段4 */
  info4: string
  /** 仓库 */
  warehouse: string[]
  /** 状态 */
  status: string
}

/**
 * 物流商状态更新参数接口
 */
export interface LogisticsProviderStatusParams {
  /** 物流商ID */
  id: string
  /** 状态 */
  status: string
}
