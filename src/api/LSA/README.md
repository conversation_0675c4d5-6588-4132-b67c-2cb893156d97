# 物流商管理 API 集成文档

## 概述

本模块为 `src/views/OWSSettings/LSA/index.vue` 页面提供物流商管理相关的 API 接口集成。

## API 接口

### 1. 分页获取物流商列表

```typescript
findLogisticsProviderList(data: LogisticsProviderFilterParams): Promise<IResponse<BasePage<LogisticsProviderVo[]>>>
```

**接口地址**: `POST /api/management/logisticsProvider/findLogisticsProviderList`

**请求参数**:
- `pageSize`: 页面大小
- `pageNum`: 页码
- `logisticsProviderName`: 物流商名称（可选）
- `account`: 账号（可选）
- `status`: 状态（可选）
- `order`: 排序（可选）

### 2. 根据ID查询物流商信息

```typescript
findLogisticsProviderInfo(id: string): Promise<IResponse<LogisticsProviderVo>>
```

**接口地址**: `POST /api/management/logisticsProvider/findLogisticsProviderInfo`

### 3. 保存物流商信息（新增）

```typescript
saveLogisticsProvider(data: LogisticsProviderEntity): Promise<IResponse<any>>
```

**接口地址**: `POST /api/management/logisticsProvider/saveLogisticsProvider`

### 4. 修改物流商信息

```typescript
updateLogisticsProvider(data: LogisticsProviderEntity): Promise<IResponse<any>>
```

**接口地址**: `POST /api/management/logisticsProvider/updateLogisticsProvider`

### 5. 更新物流商状态

```typescript
changeLogisticsProviderStatus(data: LogisticsProviderStatusParams): Promise<IResponse<any>>
```

**接口地址**: `POST /api/management/logisticsProvider/changeLogisticsProviderStatus`

## 类型定义

### LogisticsProviderEntity
物流商实体接口，包含以下字段：
- `id`: 主键
- `logisticsProviderName`: 物流商名称
- `account`: 账号
- `remark`: 备注
- `info1-info4`: 扩展字段1-4
- `warehouse`: 仓库数组
- `status`: 状态

### LogisticsProviderVo
物流商响应数据接口，字段与 LogisticsProviderEntity 相同，但所有字段都是必填的。

### LogisticsProviderFilterParams
物流商查询参数接口，继承自 LogisticsProviderEntity，额外包含：
- `pageSize`: 页面大小（必填）
- `pageNum`: 页码（必填）
- `order`: 排序（可选）

## 使用示例

```typescript
import { findLogisticsProviderList, saveLogisticsProvider } from '@/api/LSA'

// 获取物流商列表
const getList = async () => {
  const res = await findLogisticsProviderList({
    pageSize: 10,
    pageNum: 1,
    logisticsProviderName: '顺丰'
  })
  console.log(res.result.records)
}

// 新增物流商
const addProvider = async () => {
  await saveLogisticsProvider({
    logisticsProviderName: '新物流商',
    account: 'test123',
    status: '1'
  })
}
```

## 注意事项

1. 所有接口都使用 POST 方法
2. 需要在请求头中包含 Authorization Bearer token
3. 状态字段：'1' 表示启用，'0' 表示停用
4. 响应数据结构遵循项目统一的 IResponse 格式
