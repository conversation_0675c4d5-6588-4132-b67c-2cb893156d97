import request from '@/axios'
import type { FilterParams, WarehouseEntity } from './types'

/**
 * @description: 分页获取仓库列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getList = (
  data: FilterParams
): Promise<IResponse<BasePage<WarehouseEntity[]>>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/getList',
    data
  })
}

/**
 * @description: 新增
 * @return {*}
 */
export const saveWarehouse = (
  data: WarehouseEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/save',
    data
  })
}

/**
 * @description: 更新
 * @param {WarehouseEntity} data
 * @return {*}
 */
export const updateWarehouse = (
  data: WarehouseEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/update',
    data
  })
}

/**
 * @description: 删除
 * @param {string} idList
 * @return {*}
 */
export const delWarehouseById = (idList: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/baseWarehouseManage/delById',
    params: {
      idList
    }
  })
}

/**
 * @description: 根据id查询仓库信息
 * @param {string} id
 * @return {*}
 */
export const getWarehouseById = (id: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/baseWarehouseManage/getById',
    params: {
      id
    }
  })
}

/**
 * @description: 启用
 * @param {string} idList
 * @return {*}
 */
export const enableWarehouse = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/enable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 禁用
 * @param {string} idList
 * @return {*}
 */
export const disableWarehouse = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseWarehouseManage/disable',
    params: {
      idList: idList.join(',')
    }
  })
}
