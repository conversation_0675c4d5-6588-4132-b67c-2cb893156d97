export interface WarehouseEntity {
  id?: string
  status?: string
  delStatus?: number
  warehouseName?: string
  warehouseCode?: string
  warehouseType?: number
  contacts?: string
  contactsPhone?: string
  postalCode?: string
  country?: string
  countryCode?: string
  province?: string
  provinceCode?: string
  city?: string
  cityCode?: string
  detailedAddressOne?: string
  detailedAddressTwo?: string
  belongEnterprise?: string
  labelSource?: number
  warehousePartners?: string
}

export interface FilterParams extends WarehouseEntity {
  pageSize: number
  pageNum: number
}
