import request from '@/axios'
import type { MenuRequest, RoleEntity, RoleListFilterParams } from './types'

/**
 * @description: 分页获取角色列表
 * @param {any} data
 * @return {*}
 */
export const getRoleList = (
  data: RoleListFilterParams
): Promise<IResponse<BasePage<RoleEntity[]>>> => {
  return request.post({ url: '/api/management/role/getRoleList', data })
}

/**
 * @description: 保存角色信息
 * @param {RoleEntity} data
 * @return {*}
 */
export const saveRoleInfo = (data: RoleEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/role/saveRoleInfo',
    data
  }) as unknown as Promise<any>
}

/**
 * @description: 更新角色信息
 * @param {RoleEntity} data
 * @return {*}
 */
export const updateRoleInfo = (data: RoleEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/role/updateRoleInfo',
    data
  }) as unknown as Promise<any>
}

/**
 * @description: 删除角色
 * @param {string} roleId
 * @return {*}
 */
export const deleteOrgInfo = (roleId: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/role/deleteOrgInfo',
    params: { roleId }
  })
}

/**
 * @description: 获取全量菜单树
 * @param {*} Promise
 * @return {*}
 */
export const getMenuTree = (): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/role/getMenuTree'
  })
}

/**
 * @description: 保存或更新角色菜单配置
 * @return {*}
 */
export const saveOrUpdateRoleMenu = (
  data: MenuRequest
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/role/saveOrUpdateRoleMenu',
    data
  })
}

/**
 * @description: 根据角色id获取角色已配置菜单id
 * @param {string} roleId
 * @return {*}
 */
export const getMenuIdList = (roleId: string): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/role/getMenuIdList',
    params: { roleId }
  })
}
