import request from '@/axios'
import type { FilterParams, CategoryEntity } from './types'

/**
 * @description: 分页获取列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getCategoryList = (
  data: FilterParams
): Promise<IResponse<BasePage<CategoryEntity[]>>> => {
  return request.post({
    url: '/api/management/productCategory/getProductCategoryList',
    data
  })
}

/**
 * @description: 新增
 * @param {CategoryEntity} data
 * @return {*}
 */
export const saveCategory = (data: CategoryEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productCategory/saveProductCategoryInfo',
    data
  })
}

/**
 * @description: 更新
 * @param {CategoryEntity} data
 * @return {*}
 */
export const updateCategory = (
  data: CategoryEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productCategory/updateProductCategoryInfo',
    data
  })
}

/**
 * @description: 删除
 * @param {CategoryEntity} data
 * @return {*}
 */
export const deleteCategory = (
  data: CategoryEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productCategory/deleteProductCategory',
    data
  })
}

/**
 * @description: 分配属性
 * @return {*}
 */
export const allocationCategory = (
  data: CategoryEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/productCategory/allocationAttribute',
    data
  })
}
