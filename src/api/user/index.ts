import request from '@/axios'
import type { FilterParams, UserEntity } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getUserList = (
  data: FilterParams
): Promise<IResponse<BasePage<UserEntity[]>>> => {
  return request.post({ url: '/api/management/sysUser/getUserList', data })
}

/**
 * @description: 新增系统用户
 * @param {UserEntity} data
 * @return {*}
 */
export const saveSysUser = (data: UserEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/saveSysUser',
    data
  })
}

/**
 * @description: 更新系统用户
 * @param {UserEntity} data
 * @return {*}
 */
export const updateUser = (data: UserEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/updateUser',
    data
  })
}

/**
 * @description: 删除系统用户
 * @param {string} roleId
 * @return {*}
 */
export const deleteUser = (data: UserEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/deleteUser',
    data
  })
}

/**
 * @description: 禁用系统用户
 * @return {*}
 */
export const disableUser = (data: UserEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/disableUser',
    data
  })
}
/**
 * @description: 启用
 * @param {UserEntity} data
 * @return {*}
 */
export const enableUser = (data: UserEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/enableUser',
    data
  })
}

/**
 * @description: 重置密码
 * @param {string} userId
 * @return {*}
 */
export const resetPassword = (data: any): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sysUser/resetPassword',
    data
  })
}
