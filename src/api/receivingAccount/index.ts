import request from '@/axios'
import type { FilterParams, ReceivingAccountEntity } from './types'

/**
 * @description: 获取收款账户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getReceivingAccountList = (
  data: FilterParams
): Promise<IResponse<BasePage<ReceivingAccountEntity[]>>> => {
  return request.post({
    url: '/api/management/receivingAccount/getReceivingAccountList',
    data
  })
}

/**
 * @description: 保存收款账户
 * @param {ReceivingAccountEntity} data
 * @return {*}
 */
export const saveReceivingAccount = (
  data: ReceivingAccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/receivingAccount/saveReceivingAccount',
    data
  })
}

/**
 * @description: 更新收款账户
 * @param {ReceivingAccountEntity} data
 * @return {*}
 */
export const updateReceivingAccount = (
  data: ReceivingAccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/receivingAccount/updateReceivingAccount',
    data
  })
}

/**
 * @description: 删除
 * @param {string} ids
 * @return {*}
 */
export const deleteReceivingAccount = (
  data: ReceivingAccountEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/receivingAccount/deleteReceivingAccount',
    data
  })
}

/**
 * @description: 币种、基准币种下拉选项
 * @param {*} Promise
 * @return {*}
 */
export const getCurrencyDropDownList = (): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/currencyManage/getCurrencyDropDownList'
  })
}
