import request from '@/axios'
import type {
  SystemChannelWarehouseModel,
  SystemChannelWarehouseVo,
  SystemChannelConditionModel,
  SystemChannelConditionVo
} from './index.d'

// ==================== 系统渠道绑定仓库地址管理 ====================

/**
 * @description: 分页查询系统渠道绑定仓库地址信息
 * @param {SystemChannelWarehouseModel} data - 查询参数
 * @return {Promise<IResponse<BasePage<SystemChannelWarehouseVo[]>>>} 系统渠道绑定仓库地址分页数据
 */
export const findListSystemChannelWarehouse = (
  data: SystemChannelWarehouseModel
): Promise<IResponse<BasePage<SystemChannelWarehouseVo[]>>> => {
  return request.post({
    url: '/api/management/SystemChannelWarehouse/findList',
    data
  })
}

/**
 * @description: 保存系统渠道绑定仓库地址信息
 * @param {SystemChannelWarehouseModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const saveSystemChannelWarehouse = (
  data: SystemChannelWarehouseModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/SystemChannelWarehouse/save',
    data
  })
}

/**
 * @description: 修改系统渠道绑定仓库地址信息
 * @param {SystemChannelWarehouseModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const updateSystemChannelWarehouse = (
  data: SystemChannelWarehouseModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/SystemChannelWarehouse/update',
    data
  })
}

/**
 * @description: 根据id查询系统渠道绑定仓库地址信息
 * @param {SystemChannelWarehouseModel} data - 入参
 * @return {Promise<IResponse<SystemChannelWarehouseVo>>}
 */
export const findInfoSystemChannelWarehouse = (
  data: SystemChannelWarehouseModel
): Promise<IResponse<SystemChannelWarehouseVo>> => {
  return request.post({
    url: '/api/management/SystemChannelWarehouse/findInfo',
    data
  })
}

/**
 * @description: 更新系统渠道绑定仓库地址状态
 * @param {SystemChannelWarehouseModel} data - 入参
 * @return {Promise<IResponse<SystemChannelWarehouseVo>>}
 */
export const changeStatusSystemChannelWarehouse = (
  data: SystemChannelWarehouseModel
): Promise<IResponse<SystemChannelWarehouseVo>> => {
  return request.post({
    url: '/api/management/SystemChannelWarehouse/changeStatus',
    data
  })
}

// ==================== 系统渠道条件管理 ====================

/**
 * @description: 分页查询系统渠道条件信息
 * @param {SystemChannelConditionModel} data - 查询参数
 * @return {Promise<IResponse<BasePage<SystemChannelConditionVo[]>>>} 系统渠道条件分页数据
 */
export const findListSystemChannelCondition = (
  data: SystemChannelConditionModel
): Promise<IResponse<BasePage<SystemChannelConditionVo[]>>> => {
  return request.post({
    url: '/api/management/SystemChannelCondition/findList',
    data
  })
}

/**
 * @description: 保存系统渠道条件信息
 * @param {SystemChannelConditionModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const saveSystemChannelCondition = (
  data: SystemChannelConditionModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/SystemChannelCondition/save',
    data
  })
}

/**
 * @description: 修改系统渠道条件信息
 * @param {SystemChannelConditionModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const updateSystemChannelCondition = (
  data: SystemChannelConditionModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/SystemChannelCondition/update',
    data
  })
}

/**
 * @description: 根据id查询系统渠道条件信息
 * @param {SystemChannelConditionModel} data - 入参
 * @return {Promise<IResponse<SystemChannelConditionVo>>}
 */
export const findInfoSystemChannelCondition = (
  data: SystemChannelConditionModel
): Promise<IResponse<SystemChannelConditionVo>> => {
  return request.post({
    url: '/api/management/SystemChannelCondition/findInfo',
    data
  })
}

/**
 * @description: 更新系统渠道条件状态
 * @param {SystemChannelConditionModel} data - 入参
 * @return {Promise<IResponse<SystemChannelConditionVo>>}
 */
export const changeStatusSystemChannelCondition = (
  data: SystemChannelConditionModel
): Promise<IResponse<SystemChannelConditionVo>> => {
  return request.post({
    url: '/api/management/SystemChannelCondition/changeStatus',
    data
  })
}
