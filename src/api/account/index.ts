import request from '@/axios'
import type { FilterParams, AccountManagementEntity } from './types'

/**
 * @description: 分页获取列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getAccountList = (
  data: FilterParams
): Promise<IResponse<BasePage<AccountManagementEntity[]>>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/findSellerBankAccountList',
    data
  })
}

/**
 * @description: 新增
 * @param {AccountManagementEntity} data
 * @return {*}
 */
export const saveAccount = (
  data: AccountManagementEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/saveSellerBankAccountInfo',
    data
  })
}

/**
 * @description: 更新
 * @param {AccountManagementEntity} data
 * @return {*}
 */
export const updateAccount = (
  data: AccountManagementEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/updateSellerBankAccountInfo',
    data
  })
}

/**
 * @description: 更新状态
 * @param {AccountManagementEntity} data
 * @return {*}
 */
export const changeStatusAccount = (
  data: AccountManagementEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerBankAccount/changeStatus',
    data
  })
}
