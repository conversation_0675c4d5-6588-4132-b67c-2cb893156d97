export interface AccountManagementEntity {
  id?: string
  seller?: string
  businessType?: string
  accountType?: string
  currency?: string
  quota?: string
  preDeductionAmount?: string
  availableAmount?: string
  status?: string
  payMode?: string
  settlementMode?: string
  billGenerationDay?: string
  repaymentTerm?: string
  autoRepayment?: boolean
  minAmount?: string
  maxAmount?: string
}

export interface FilterParams extends AccountEntity {
  pageSize: number
  pageNum: number
}
