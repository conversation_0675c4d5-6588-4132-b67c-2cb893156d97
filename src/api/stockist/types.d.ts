export interface StockistEntity {
  /** 主键ID */
  id?: string
  /** 用户名 */
  userName?: string
  /** 客户等级 */
  level?: number
  /** 客户类型 */
  type?: string
  /** 客户名称 */
  companyName?: string
  /** 客户编码 */
  companyCode?: string
  /** 客户简称 */
  companyAbbr?: string
  /** 法人证件号 */
  legalPersonId?: string
  /** 营业执照号 */
  creditCode?: string
  /** 营业执照 */
  businessLicense?: string
  /** 联系人姓名 */
  contactsName?: string
  /** 联系人电话 */
  contactsPhone?: string
  /** 邮箱 */
  email?: string
  /** 详细地址1 */
  detailedAddressOne?: string
  /** 详细地址2 */
  detailedAddressTwo?: string
  /** 国家 */
  country?: string
  /** 省/州 */
  province?: string
  /** 市 */
  city?: string
  /** 备注 */
  remarks?: string
  /** 状态 0 禁用 1 启用 2 冻结 */
  status?: string
  /** 收货仓库 */
  receivingWarehouse?: string
  /** 海外仓 */
  overseasWarehouse?: string[]
  /** 法人姓名 */
  legalPersonName?: string
  /** 法人身份证 */
  legalPersonIdCard?: string
  /** 公司电话 */
  companyPhone?: string
  /** 注册地址ID */
  registerPlaceId?: string
  /** 联系地址ID */
  contactPlaceId?: string
  /** 联系人身份证 */
  contactsIdCard?: string
  /** 联系人职位 */
  contactsPosition?: string
  /** 邮编 */
  postalCode?: string
}

export interface FilterParams extends StockistEntity {
  /** 每页数量 */
  pageSize: number
  /** 页码 */
  pageNum: number
  /** 排序字段 */
  order?: string
}
