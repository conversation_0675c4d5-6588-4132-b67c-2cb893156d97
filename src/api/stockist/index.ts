import request from '@/axios'
import type { FilterParams, StockistEntity } from './types'

/**
 * @description: 分页获取列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getListStockist = (
  data: FilterParams
): Promise<IResponse<BasePage<StockistEntity[]>>> => {
  return request.post({
    url: '/api/management/sellerManagement/findSellerList',
    data
  })
}

/**
 * @description: 新增
 * @param {StockistEntity} data
 * @return {*}
 */
export const saveStockist = (data: StockistEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerManagement/saveSellerInfo',
    data
  })
}

/**
 * @description: 更新
 * @param {StockistEntity} data
 * @return {*}
 */
export const updateStockist = (
  data: StockistEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerManagement/updateSellerInfo',
    data
  })
}

/**
 * @description: 卖家充值
 * @return {*}
 */
export const sellerRechargeStockist = (
  data: StockistEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerManagement/sellerRecharge',
    data
  })
}

/**
 * @description: 重置密码
 * @param {string} sellerId
 * @return {*}
 */
export const resetSellerPassword = (
  sellerId: string
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/sellerManagement/resetSellerPassword',
    params: {
      sellerId
    }
  })
}

/**
 * @description: 通过、驳回审核、冻结、启用
 * @param {StockistEntity} data
 * @return {*}
 */
export const changeSellerStatusStockist = (
  data: StockistEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/sellerManagement/changeSellerStatus',
    data
  })
}
