import request from '@/axios'
import type { FilterParams, LocationEntity } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getLocationList = (
  data: FilterParams
): Promise<IResponse<BasePage<LocationEntity[]>>> => {
  return request.post({
    url: '/api/management/baseLocationManage/getList',
    data
  })
}

/**
 * @description: 新增地址
 * @return {*}
 */
export const saveLocation = (data: LocationEntity): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseLocationManage/save',
    data
  })
}

/**
 * @description: 更新地址
 * @param {LocationEntity} data
 * @return {*}
 */
export const updateLocation = (
  data: LocationEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseLocationManage/update',
    data
  })
}

/**
 * @description: 删除地址
 * @return {*}
 */
export const delByIdLocation = (idList: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/baseLocationManage/delById',
    params: { idList }
  })
}

/**
 * @description: 启用
 * @param {string} idList
 * @return {*}
 */
export const enableLocation = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseLocationManage/enable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 禁用
 * @param {string} idList
 * @return {*}
 */
export const disableLocation = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseLocationManage/disable',
    params: {
      idList: idList.join(',')
    }
  })
}
