export interface LocationEntity {
  id?: string
  locationName?: string
  locationAbbr?: string
  locationCode?: string
  locationAttr?: string
  locationType?: number
  locationTypeName?: string
  country?: string
  countryCode?: string
  province?: string
  provinceCode?: string
  city?: string
  cityCode?: string
  detailedAddressOne?: string
  detailedAddressTwo?: string
  postalCode?: string
  contacts?: string
  contactsPhone?: string
  status?: string
  delStatus?: number
}

export interface FilterParams extends LocationEntity {
  pageSize: number
  pageNum: number
}
