export interface FacilitatorEntity {
  id?: string
  serviceProviderName?: string
  serviceProviderAbbr?: string
  serviceProviderCode?: string
  serviceProviderType?: number
  contacts?: string
  contactsPhone?: string
  country?: string
  countryCode?: string
  province?: string
  provinceCode?: string
  city?: string
  cityCode?: string
  detailedAddressOne?: string
  detailedAddressTwo?: string
  postalCode?: string
  status?: string
}

export interface FilterParams extends FacilitatorEntity {
  pageSize: number
  pageNum: number
}
