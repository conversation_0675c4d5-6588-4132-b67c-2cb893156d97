import request from '@/axios'
import type { FilterParams, FacilitatorEntity } from './types'

/**
 * @description: 分页获取系统用户列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getServiceList = (
  data: FilterParams
): Promise<IResponse<BasePage<FacilitatorEntity[]>>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/getList',
    data
  })
}

/**
 * @description: 新增地址
 * @return {*}
 */
export const saveService = (
  data: FacilitatorEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/save',
    data
  })
}

/**
 * @description: 更新地址
 * @param {LocationEntity} data
 * @return {*}
 */
export const updateService = (
  data: FacilitatorEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/update',
    data
  })
}

/**
 * @description: 删除地址
 * @return {*}
 */
export const delByIdService = (idList: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/baseServiceProviderManage/delById',
    params: { idList }
  })
}

/**
 * @description: 启用
 * @param {string} idList
 * @return {*}
 */
export const enableService = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/enable',
    params: {
      idList: idList.join(',')
    }
  })
}

/**
 * @description: 禁用
 * @param {string} idList
 * @return {*}
 */
export const disableService = (idList: string[]): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/baseServiceProviderManage/disable',
    params: {
      idList: idList.join(',')
    }
  })
}
