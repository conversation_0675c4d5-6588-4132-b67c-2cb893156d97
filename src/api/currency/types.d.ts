export interface CurrencyEntity {
  id?: string
  startTime?: string
  endTime?: string
  currencyName?: string
  currencyCode?: string
  baseCurrencyName?: string
  baseCurrencyCode?: string
  officialExchangeRate?: number
  customExchangeRate?: number
  exchangeRateUpdateTime?: string
  status?: string
  customExchangeValue?: number
  adjustmentMethods?: Array<{
    name?: string
    value?: number
  }>
  ids?: string[]
  currencyCodes?: string[]
}

export interface JobInfo {
  jobName?: string
  cron?: string
}

export interface FilterParams extends CurrencyEntity {
  pageSize: number
  pageNum: number
}
