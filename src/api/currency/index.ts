import request from '@/axios'
import type { FilterParams, CurrencyEntity, JobInfo } from './types'

/**
 * @description: 获取币种汇率列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getCurrencyList = (
  data: FilterParams
): Promise<IResponse<BasePage<CurrencyEntity[]>>> => {
  return request.post({
    url: '/api/management/currencyManage/getCurrencyList',
    data
  })
}

/**
 * @description: 获取币种汇率历史记录列表
 * @param {CurrencyEntity} data
 * @return {*}
 */
export const getCurrencyHistoryRecordList = (
  data: CurrencyEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/currencyManage/getCurrencyHistoryRecordList',
    data
  })
}

/**
 * @description: 修改币种设置
 * @param {CurrencyEntity} data
 * @return {*}
 */
export const updateCurrencySetting = (
  data: CurrencyEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/currencyManage/updateCurrencySetting',
    data
  })
}

/**
 * @description: 启用或禁用币种
 * @param {CurrencyEntity} data
 * @return {*}
 */
export const enableOrDisableCurrency = (
  data: CurrencyEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/currencyManage/enableOrDisableCurrency',
    data
  })
}

/**
 * @description: 默认币种汇率定时更新策略
 * @param {JobInfo} data
 * @return {*}
 */
export const defaultRegularUpdateStrategy = (
  data: JobInfo
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/currencyManage/defaultRegularUpdateStrategy',
    data
  })
}

/**
 * @description: 币种配置
 * @param {JobInfo} data
 * @return {*}
 */
export const currencyRegularUpdateStrategy = (
  data: JobInfo
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/currencyManage/currencyRegularUpdateStrategy',
    data
  })
}
