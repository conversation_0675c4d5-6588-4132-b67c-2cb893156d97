import request from '@/axios'
import type { FilterParams, FirstVesselSettingEntity } from './types'

/**
 * @description: 查询
 * @param {FilterParams} data
 * @return {*}
 */
export const getFirstVesselSettingList = (
  data: FilterParams
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVesselSetting/getFirstVesselSettingList',
    data
  })
}

/**
 * @description: 保存
 * @param {FirstVesselSettingEntity} data
 * @return {*}
 */
export const saveFirstVesselSetting = (
  data: FirstVesselSettingEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVesselSetting/saveFirstVesselSetting',
    data
  })
}

/**
 * @description: 更新
 * @param {FirstVesselSettingEntity} data
 * @return {*}
 */
export const updateFirstVesselSetting = (
  data: FirstVesselSettingEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVesselSetting/updateFirstVesselSetting',
    data
  })
}

/**
 * @description: 删除
 * @param {FirstVesselSettingEntity} data
 * @return {*}
 */
export const deleteFirstVesselSetting = (
  data: FirstVesselSettingEntity
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/firstVesselSetting/deleteFirstVesselSetting',
    data
  })
}

/**
 * @description: 根据id查询详情
 * @param {string} id
 * @return {*}
 */
export const getFirstVesselSettingById = (
  id: string
): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/firstVesselSetting/getFirstVesselSettingById',
    params: { id }
  })
}
