export interface FirstVesselSettingEntity {
  id?: string
  transportServiceName?: string
  transportServiceCode?: string
  chargeMode?: string
  weightMode?: string
  roundMode?: string
  bubbleCoefficient?: string
  chargeWeightMode?: string
  ticketWeightPrecision?: string
  boxWeightPrecision?: string
  sizePrecision?: string
  minBoxRealWeight?: string
  minBoxMaterialWeight?: string
  minBoxChargeWeight?: string
  minTicketChargeWeight?: string
  arrivalCountry?: string
  customer?: string
  destinationType?: string
  ids?: string[]
  status?: string
}

export interface FilterParams extends FirstVesselSettingEntity {
  pageSize?: number
  pageNum?: number
}
