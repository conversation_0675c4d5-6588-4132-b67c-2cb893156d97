/**
 * 物流服务相关查询参数类型定义
 * 所有字段均为可选，用于条件筛选
 */
export interface SystemChannelModel {
  /**
   * 每页条数
   */
  pageSize?: number // integer(int32)

  /**
   * 页码
   */
  pageNum?: number // integer(int32)

  /**
   * 排序方式
   */
  order?: string

  /**
   * 唯一标识ID
   */
  id?: string

  /**
   * 名称
   */
  name?: string

  /**
   * 代码
   */
  code?: string

  /**
   * 物流商id
   */
  logisticsProviderId?: string

  /**
   * api服务代码
   */
  apiServiceCode?: string

  /**
   * 承运商
   */
  carrier?: string

  /**
   * 保险服务（0支持，1不支持）
   */
  insuranceServices?: string

  /**
   * 签名服务（0直接签名、1间接签名、2成人签名）
   */
  signatureService?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string // date-time格式

  /**
   * 修改时间
   */
  updateTime?: string // date-time格式

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 生效模式（0全都，1单一）
   */
  effectiveMode?: string
}

/**
 * 物流服务相关信息类型定义
 * 所有字段均为可选
 */
export interface SystemChannelVo {
  /**
   * ID标识
   */
  id?: string

  /**
   * 名称
   */
  name?: string

  /**
   * 代码
   */
  code?: string

  /**
   * 物流商id
   */
  logisticsProviderId?: string

  /**
   * api服务代码
   */
  apiServiceCode?: string

  /**
   * 承运商
   */
  carrier?: string

  /**
   * 保险服务（0支持，1不支持）
   */
  insuranceServices?: string

  /**
   * 签名服务（0直接签名、1间接签名、2成人签名）
   */
  signatureService?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string

  /**
   * 修改时间
   */
  updateTime?: string

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 生效模式（0全都，1单一）
   */
  effectiveMode?: string
}
