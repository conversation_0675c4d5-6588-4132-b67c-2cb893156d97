import request from '@/axios'
import type { SystemChannelVo, SystemChannelModel } from './types'

/**
 * @description: 分页获取查询系统渠道信息
 * @param {SystemChannelModel} data - 查询参数
 * @return {Promise<IResponse<BasePage<SystemChannelVo[]>>>} 物流商分页数据
 */
export const findListSystemChannel = (
  data: SystemChannelModel
): Promise<IResponse<BasePage<SystemChannelVo[]>>> => {
  return request.post({
    url: '/api/management/systemChannel/findList',
    data
  })
}

/**
 * @description: 保存系统渠道信息
 * @param {SystemChannelModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const saveSystemChannel = (
  data: SystemChannelModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/systemChannel/save',
    data
  })
}

/**
 * @description: 修改系统渠道信息
 * @param {SystemChannelModel} data - 入参
 * @return {Promise<IResponse<any>>}
 */
export const updateSystemChannel = (
  data: SystemChannelModel
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/systemChannel/update',
    data
  })
}

/**
 * @description: 根据id查询系统渠道信息
 * @param {SystemChannelModel} data - 入参
 * @return {Promise<IResponse<SystemChannelVo>>}
 */
export const findInfoSystemChannel = (
  data: SystemChannelModel
): Promise<IResponse<SystemChannelVo>> => {
  return request.post({
    url: '/api/management/systemChannel/findInfo',
    data
  })
}

/**
 * @description: 更新系统渠道状态
 * @param {SystemChannelModel} data - 入参
 * @return {Promise<IResponse<SystemChannelVo>>}
 */
export const changeStatusSystemChannel = (
  data: SystemChannelModel
): Promise<IResponse<SystemChannelVo>> => {
  return request.post({
    url: '/api/management/systemChannel/changeStatus',
    data
  })
}
