import {
  findProductInfoList,
  findSellerAll,
  getAllFirstVesselSettingList,
  getServiceProvider
} from '@/api/common'
import { getOrgRoleTree } from '@/api/institution'
import { getCurrencyDropDownList } from '@/api/receivingAccount'

type OptionItem =
  | 'orgRoleTree'
  | 'currencyOption'
  | 'sellerOptions'
  | 'firstVesselOptions'
  | 'serviceProviderOptions'
  | 'productOptions'

/**
 * @description: 通过code获取字典
 * @param {string} optionCode
 * @return {*}
 */
export const useOptions = (optionCode: OptionItem[]): any => {
  const orgRoleTree = ref<any[]>([])

  /**
   * @description: 机构树下拉选项
   */
  const orgRoleTreeApi = () => {
    getOrgRoleTree()
      .then(res => {
        orgRoleTree.value = res.result
      })
      .catch(() => {
        orgRoleTree.value = []
      })
  }

  const currencyOption = ref<CurrencyOptionEntity[]>([])

  /**
   * @description: 币种、基准币种下拉选项
   */
  const currencyOptionApi = () => {
    getCurrencyDropDownList()
      .then(res => {
        currencyOption.value = res.result as CurrencyOptionEntity[]
      })
      .catch(() => {
        currencyOption.value = []
      })
  }

  const sellerOptions = ref<CompanyInfo[]>([])

  /**
   * @description: 获取客户下拉选项
   */
  const getSellerApi = () => {
    findSellerAll()
      .then(res => {
        sellerOptions.value = res.result
      })
      .catch(() => {
        sellerOptions.value = []
      })
  }

  const firstVesselOptions = ref<CompanyInfo[]>([])

  /**
   * @description: 获取运输服务下拉选项
   */
  const getFirstVesselApi = () => {
    getAllFirstVesselSettingList()
      .then(res => {
        firstVesselOptions.value = res.result
      })
      .catch(() => {
        firstVesselOptions.value = []
      })
  }

  const serviceProviderOptions = ref<BaseOption[]>([])

  /**
   * @description: 获取运输服务下拉选项
   */
  const getServiceProviderApi = () => {
    getServiceProvider()
      .then(res => {
        serviceProviderOptions.value =
          res.result?.map((item: any) => {
            return {
              name: item.name,
              code: item.code,
              children: item.serviceProviderList
            }
          }) || []
      })
      .catch(() => {
        serviceProviderOptions.value = []
      })
  }

  const productOptions = ref<any[]>([])

  /**
   * @description: 获取所有商品，用于下拉选项
   */
  const getProductOptionsApi = () => {
    findProductInfoList()
      .then(res => {
        productOptions.value = res.result || []
      })
      .catch(() => {
        productOptions.value = []
      })
  }

  const init = () => {
    if (optionCode.includes('orgRoleTree')) {
      orgRoleTreeApi()
    }

    if (optionCode.includes('currencyOption')) {
      currencyOptionApi()
    }

    if (optionCode.includes('sellerOptions')) {
      getSellerApi()
    }

    if (optionCode.includes('firstVesselOptions')) {
      getFirstVesselApi()
    }

    if (optionCode.includes('serviceProviderOptions')) {
      getServiceProviderApi()
    }

    if (optionCode.includes('productOptions')) {
      getProductOptionsApi()
    }
  }

  init()

  return {
    orgRoleTree,
    orgRoleTreeApi,
    currencyOption,
    currencyOptionApi,
    sellerOptions,
    getSellerApi,
    firstVesselOptions,
    getFirstVesselApi,
    serviceProviderOptions,
    getServiceProviderApi,
    productOptions,
    getProductOptionsApi
  }
}
