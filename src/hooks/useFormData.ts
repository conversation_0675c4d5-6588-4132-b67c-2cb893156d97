import { type FormInstance, type FormRules } from 'element-plus'
import { cloneDeep } from 'lodash-es'
/**
 * @description: 初始化formData、重置formData
 * @param {T} initialFormData
 * @param {FormRules} initRules
 * @return {*}
 */
export const useFormData = <T = any>(
  initialFormData: T,
  initRules?: FormRules
) => {
  const generateInitialFormData = () => {
    return { ...cloneDeep(initialFormData) } as T
  }

  const formData = ref<T>(generateInitialFormData())
  const formDataRef = ref<FormInstance>()

  const rules = ref<FormRules | undefined>(initRules)

  const resetFormData = () => {
    formData.value = generateInitialFormData()
  }
  return {
    formData,
    formDataRef,
    rules,
    resetFormData
  }
}
