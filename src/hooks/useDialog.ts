import { cloneDeep } from 'lodash-es'

interface UseTableConfig<T> {
  initialFormData?: T
  viewEntity: ViewEntity
  titleMap: Record<DlViewType, string>
  apiMap?: Record<DlViewType | any, (data: T) => Promise<any>>
  ruleMap?: Record<string, any>
  openCb?: () => void
  closeCb?: () => void
  confirmCb?: () => void
}

export const useDialog = <T = any>(config: UseTableConfig<T>) => {
  const { initialFormData, titleMap, viewEntity, apiMap, ruleMap } = config

  const titleStr = computed(() => {
    return titleMap[viewEntity.type] as string
  })

  const rules = ref(ruleMap)

  const formData = ref<T>({ ...initialFormData } as T)

  const methods = {
    handleOpen: () => {
      if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
        formData.value = Object.assign(formData.value, viewEntity.record)
      }
      config.openCb?.()
    },
    handleClose: () => {
      setTimeout(() => {
        formData.value = initialFormData
      })
      config.closeCb?.()
    },
    handleConfirm: (formDataRef: any) => {
      formDataRef.validate(async (valid: any) => {
        if (valid) {
          console.log('formData.value--111', cloneDeep(formData.value))

          await apiMap?.[viewEntity.type](formData.value)
          config.confirmCb?.()
        }
      })
    }
  }

  return {
    titleStr: titleStr.value,
    formData: formData.value,
    rules: rules.value,
    dlMethods: methods
  }
}
