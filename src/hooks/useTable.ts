import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from './useI18n'

const { t } = useI18n()

interface UseTableConfig<T> {
  /**
   * 是否初始化的时候请求一次
   */
  immediate?: boolean
  initialFormData?: T
  fetchDataApi: () => Promise<{
    list: any[]
    total?: number
  }>
  fetchDelApi?: (record?: any) => Promise<boolean>
}

export const useTable = <T = any>(config: UseTableConfig<T>) => {
  const { immediate = true, initialFormData } = config

  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  const dataList = ref<any[]>([])
  const record = ref<any>()
  const viewType = ref<DlViewType>()
  const viewVisible = ref(false)
  const selection = ref<T[]>([])
  const formData = ref<T>({ ...initialFormData } as T)

  const viewEntity = ref<ViewEntity>({
    type: 'ADD',
    visible: false,
    record: undefined
  })
  let isPageSizeChange = false

  watch(
    () => currentPage.value,
    () => {
      if (!isPageSizeChange) methods.getList()
      isPageSizeChange = false
    }
  )

  watch(
    () => pageSize.value,
    () => {
      if (unref(currentPage) === 1) {
        methods.getList()
      } else {
        currentPage.value = 1
        isPageSizeChange = true
        methods.getList()
      }
    }
  )

  onMounted(() => {
    if (immediate) {
      methods.getList()
    }
  })

  const methods = {
    /**
     * 获取表单数据
     */
    getList: async () => {
      loading.value = true
      try {
        const res = await config?.fetchDataApi()
        if (res) {
          dataList.value = res.list
          total.value = res.total || 0
        }
      } catch (errs) {
        dataList.value = []
        console.log('fetchDataApi error', errs)
      } finally {
        loading.value = false
      }
    },

    refresh: () => {
      methods.getList()
    },

    handleAdd: (cb?: () => void) => {
      viewEntity.value.visible = true
      viewEntity.value.type = 'ADD'
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      cb && cb()
    },

    handleEdit: (row: any, cb?: (record: any) => void) => {
      viewEntity.value.visible = true
      viewEntity.value.type = 'EDIT'
      viewEntity.value.record = row
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      cb && cb(row)
    },

    handleDetail: (row: any, cb?: (record: any) => void) => {
      viewEntity.value.visible = true
      viewEntity.value.type = 'DETAIL'
      viewEntity.value.record = row
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      cb && cb(row)
    },

    // sortableChange: (e: any) => {
    //   console.log('sortableChange', e)
    //   const { oldIndex, newIndex } = e
    //   dataList.value.splice(newIndex, 0, dataList.value.splice(oldIndex, 1)[0])
    //   // to do something
    // }

    hadnleDel: async (record: any, idsLength?: number) => {
      const { fetchDelApi } = config
      if (!fetchDelApi) {
        console.warn('fetchDelApi is undefined')
        return
      }
      ElMessageBox.confirm(t('common.delMessage'), t('common.delWarning'), {
        confirmButtonText: t('common.delOk'),
        cancelButtonText: t('common.delCancel'),
        type: 'warning'
      })
        .then(async () => {
          const res = await fetchDelApi(record)
          if (res) {
            ElMessage.success(t('common.delSuccess'))

            // 计算出临界点
            const current =
              unref(total) % unref(pageSize) === idsLength ||
              unref(pageSize) === 1
                ? unref(currentPage) > 1
                  ? unref(currentPage) - 1
                  : unref(currentPage)
                : unref(currentPage)

            currentPage.value = current
            methods.getList()
          }
        })
        .catch(() => {
          // ElMessage({
          //   type: 'info',
          //   message: t('common.delCancel')
          // })
        })
    },

    handleQuery: () => {
      methods.getList()
    },

    handleReset: () => {
      formData.value = Object.assign(formData.value, initialFormData)

      methods.handleQuery()
    },
    handleSelectionChange: (selected: any[]) => {
      selection.value = selected
    }
  }

  return {
    tableMethods: methods,
    tableState: {
      currentPage,
      pageSize,
      total,
      dataList,
      loading,
      viewVisible,
      viewType,
      record
    },
    viewEntity: viewEntity.value,
    formData: formData.value,
    selection
  }
}
