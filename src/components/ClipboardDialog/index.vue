<script setup lang="ts">
import { useClipboard } from '@/hooks/useClipboard'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ResetPwd'
})
const visible = defineModel({ default: false })

const { textinfo } = defineProps<{
  textinfo?: string
}>()

// const textinfo = ref('18510830081 密码重置成功，新密码为 409147')

const { copy, isSupported } = useClipboard()

const handleOpen = () => {
  visible.value = true
}

const handleConfirn = () => {
  if (textinfo) {
    copy(textinfo)
    ElMessage.success('复制成功')
  }
}
</script>

<template>
  <el-dialog v-model="visible" title="重置密码" width="500" @open="handleOpen">
    <div>{{ textinfo }}</div>
    <template #footer>
      <div class="">
        <el-button
          type="primary"
          :disabled="!isSupported"
          @click="handleConfirn"
        >
          复制密码
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
