<script setup lang="ts">
import { El<PERSON><PERSON>down, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { useI18n } from '@/hooks/useI18n'
import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'

const { push } = useRouter()

const userStore = useUserStore()

const { t } = useI18n()

const loginOut = () => {
  userStore.logoutConfirm()
}

// const dialogVisible = ref<boolean>(false)

// const toDocument = () => {
//   window.open('https://element-plus-admin-doc.cn/')
// }

const toPage = (path: string) => {
  push(path)
}
</script>

<template>
  <ElDropdown class="custom-hover" trigger="click">
    <div class="flex items-center cursor-pointer">
      <img src="@/assets/imgs/avatar.jpg" alt="" class="w-[calc(50px-25px)] rounded-[50%]" />
      <span class="<lg:hidden text-14px pl-[5px] text-[inherit]">
        <!-- {{ userStore.getUserInfo?.username }} -->
        admin
      </span>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem>
          <div @click="toPage('/personal/personal-center')">
            <!-- {{ t('router.personalCenter') }} -->
            个人中心
          </div>
        </ElDropdownItem>
        <!-- <ElDropdownItem>
          <div @click="toDocument">{{ t('common.document') }}</div>
        </ElDropdownItem> -->

        <ElDropdownItem>
          <div @click="loginOut">{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>

<style scoped lang="scss">
.fade-bottom-enter-active,
.fade-bottom-leave-active {
  transition:
    opacity 0.25s,
    transform 0.3s;
}

.fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(-10%);
}

.fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(10%);
}
</style>
