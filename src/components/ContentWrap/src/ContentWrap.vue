<script setup lang="ts">
import { ElCard, ElTooltip } from 'element-plus'

const { title = '', message = '' } = defineProps<{
  title?: string
  message?: string
}>()
</script>

<template>
  <ElCard shadow="never">
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-16px font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <div class="i-bi:question-circle-fill ml-5px text-14"></div>
        </ElTooltip>
        <div class="flex pl-20px flex-grow">
          <slot name="header"></slot>
        </div>
      </div>
    </template>
    <div>
      <slot></slot>
    </div>
  </ElCard>
</template>
