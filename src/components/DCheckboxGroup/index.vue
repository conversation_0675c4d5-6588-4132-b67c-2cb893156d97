<script setup lang="ts">
defineOptions({
  name: 'DCheckboxGroup'
})
const { options = [], fields = { label: 'itemText', value: 'itemValue' } } =
  defineProps<{
    options: any[] | DictItemEntity[]
    fields?: {
      label: string
      value: string
    }
  }>()
const value = defineModel<any>()
</script>

<template>
  <el-checkbox-group v-model="value">
    <el-checkbox
      v-for="(item, index) of options"
      :key="index"
      :value="item[fields.value]"
      :label="item[fields.label]"
    />
  </el-checkbox-group>
</template>

<style lang="scss" scoped></style>
