<script setup lang="ts">
defineOptions({
  name: 'DRadioGroup'
})
const { options = [], fields = { label: 'itemText', value: 'itemValue' } } =
  defineProps<{
    options: any[] | DictItemEntity[]
    fields?: {
      label: string
      value: string
    }
  }>()
const value = defineModel<any>()
</script>

<template>
  <el-radio-group v-model="value">
    <el-radio
      v-for="(item, index) of options"
      :key="index"
      :value="item[fields.value]"
      :label="item[fields.label]"
    ></el-radio>
  </el-radio-group>
</template>

<style lang="scss" scoped></style>
