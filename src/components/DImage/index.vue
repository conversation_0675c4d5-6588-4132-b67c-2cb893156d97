<script setup lang="ts">
defineOptions({
  name: 'DImage'
})
const {
  url,
  previewSrcList = [],
  width = '100px',
  height = '100px',
  initialIndex = 0,
  fit = 'cover'
} = defineProps<{
  url: string
  previewSrcList?: string[]
  initialIndex?: number
  fit?: '' | 'fill' | 'cover' | 'contain' | 'none' | 'scale-down'
  width?: string
  height?: string
}>()

/**
 * @description: 处理预览图
 * @param {*} computed
 * @return {*}
 */
const previewList = computed(() => {
  if (previewSrcList.length > 0) {
    return previewSrcList
  } else {
    return [url]
  }
})
</script>

<template>
  <el-image
    v-if="url?.startsWith('http')"
    :style="{ width: width, height: height }"
    :src="url"
    :zoom-rate="1.2"
    :max-scale="7"
    :min-scale="0.2"
    :preview-src-list="previewList"
    show-progress
    :initial-index="initialIndex"
    :fit="fit"
    :z-index="99999"
    preview-teleported
  />
</template>

<style lang="scss" scoped></style>
