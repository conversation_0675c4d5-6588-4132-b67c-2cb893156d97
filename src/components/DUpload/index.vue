<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import { downloadFile } from '@/utils/download'
import type { UploadFile } from 'element-plus'

defineOptions({
  name: 'DUpload'
})
const userStore = useUserStore()

const {
  limit = 9,
  multiple = true,
  accept = 'image/*',
  fileMaxSize = 2,
  isShowTip = false,
  type = 'upload'
} = defineProps<{
  limit?: number
  multiple?: boolean
  accept?: string
  fileMaxSize?: number
  isShowTip?: boolean
  type?: 'preview' | 'upload'
}>()

// const testUrl = {
//   key: 'management/6612d1ee19ce42db2ba54cfd4c0ec734.png',
//   url: 'http://urill.oss-accelerate.aliyuncs.com/management/6612d1ee19ce42db2ba54cfd4c0ec734.png?Expires=1745048536&OSSAccessKeyId=LTAI5tRRNbgbsj8jDyJEJuG9&Signature=yRmnSDXI0n9T3CShi41YAflWyBg%3D'
// }
// const fileList = ref<any>([testUrl])
const fileList = defineModel<any[]>({ default: [] })

const _temFile = ref<any[]>([])
const beforeUpload = (rawFile: any): any => {
  _temFile.value.push(rawFile)
  // 校验图片数量
  if (_temFile.value?.length + fileList.value?.length > limit) {
    ElMessage.error(`上传的图片超出限制，最多允许上传${limit}张`)
    return false
  }

  // 校验文件大小
  const isSizeValid = rawFile.size / 1024 / 1024 < fileMaxSize
  if (!isSizeValid) {
    ElMessage.error(`图片大小不能超过 ${fileMaxSize}M`)
    return false
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(rawFile)
    reader.onload = e => {
      const img = new Image()
      img.src = e?.target?.result as any
      img.onload = () => {
        const width = img.width
        const height = img.height
        // 校验宽高比例和尺寸
        const isRatioValid = width === height
        const isSizeLargeEnough = width > 800 && height > 800
        if (isRatioValid && isSizeLargeEnough) {
          resolve(true)
        } else {
          ElMessage.error('图片宽高比例必须为 1:1 且宽高都要大于 800px')
          reject(false)
        }
      }
    }
  })
}

/**
 * @description: 图片change
 * @param {*} uploadFile
 * @param {*} _uploadFiles
 * @return {*}
 */
const onChange = (uploadFile: UploadFile) => {
  const { response } = uploadFile
  if (response) {
    fileList.value.push((response as any)?.result)

    // 上传成功以后清空缓存的图片
    _temFile.value = []
  }
}

const imageRefs = ref<any[]>([])

const setRef = (_index: any, el: any) => {
  imageRefs.value.push(el)
}

/**
 * @description: 预览图片
 * @param {*} index
 * @return {*}
 */
const handleClickPreview = (index: number) => {
  imageRefs.value[index]?.showPreview()
}

/**
 * @description: 下载图片
 * @param {*} item
 * @param {*} _index
 * @return {*}
 */
const hadleDownload = (item: any) => {
  downloadFile(item.url)
}

/**
 * @description: 删除图片
 * @param {*} _item
 * @param {*} index
 * @return {*}
 */
const hadleDel = (_item: any, index: number) => {
  fileList.value.splice(index, 1)
}

/**
 * @description: 图片上传超出限制
 * @param {*} files
 * @param {*} uploadFiles
 * @return {*}
 */
const onExceed = () => {
  ElMessage.error('已选择的文件超过数量限制')
}

const isUpload = computed(() => {
  return type === 'upload'
})
</script>

<template>
  <div class="w-full flex flex-wrap">
    <div
      v-for="(item, index) in fileList"
      :key="index"
      class="flex flex-col image-item"
    >
      <template v-if="isShowTip">
        <el-tag v-if="index === 0" type="primary" class="mb-8 w-85">
          主图
        </el-tag>
        <el-tag v-if="index === 1" type="primary" class="mb-8 w-85">
          尺寸图
        </el-tag>
      </template>

      <el-image
        :key="item.url"
        :ref="el => setRef(index, el)"
        style="width: 85px; height: 85px; border-radius: 6px"
        :src="item.url"
        show-progress
        :z-index="99999"
        preview-teleported
        :preview-src-list="fileList?.map((item: any) => item.url)"
        fit="cover"
        :class="{ 'mt-32': isShowTip && index > 1 }"
      />
      <div class="cover" :style="{ top: isShowTip ? '32px' : 0 }">
        <div
          class="i-ep:zoom-in color-white text-18 cursor-pointer"
          @click="handleClickPreview(index)"
        ></div>
        <div
          class="i-ep:download color-white text-18 cursor-pointer"
          @click="hadleDownload(item)"
        ></div>
        <div
          v-if="isUpload"
          class="i-ep:delete color-white text-18 cursor-pointer"
          @click="hadleDel(item, index)"
        ></div>
      </div>
    </div>
    <template v-if="isUpload">
      <el-upload
        v-if="fileList?.length < limit"
        :multiple="multiple"
        action="/api/management/upload"
        :headers="{
          Authorization: `Bearer ${userStore.getToken}`
        }"
        :accept="accept"
        :limit="limit"
        :show-file-list="false"
        :before-upload="(row: any) => beforeUpload(row)"
        :on-change="onChange"
        :onExceed="onExceed"
      >
        <div
          class="w-85 h-85 rounded-6 bg-[#f9f9f9] flex justify-center items-center border-1 border-dashed border-[#cdd0d6]"
          :class="{
            'mt-32': !!fileList?.length && isShowTip,
            'ml-16': !!fileList?.length
          }"
        >
          <div class="i-ep:plus text-24"></div>
        </div>
      </el-upload>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.image-item {
  position: relative;
  transition: all;
  & + .image-item {
    margin-left: 16px;
  }
  &:hover .cover {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 85px;
    height: 85px;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(#000, 0.8);
    padding: 0 6px;
    border-radius: 6px;
  }
  .cover {
    display: none;
  }
}
</style>
