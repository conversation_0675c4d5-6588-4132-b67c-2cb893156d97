<script setup lang="ts">
import { getProductList } from '@/api/goods'
import type { ProductListFilter } from '@/api/goods/types'
import type { CommodityVo } from '@/api/firstLeg/types'

defineOptions({
  name: 'AddProduct'
})

const visible = defineModel({ default: false })

// 定义父组件传递的事件
const emit = defineEmits(['confirm'])

const formData = ref({
  name: '',
  num: 0
})

// 已选商品列表 - 全局保存，不随分页变化
const selectedProducts = ref<CommodityVo[]>([])

const productList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表格引用，用于设置选中状态
const tableRef = ref()

// 标志：是否正在恢复选中状态，防止触发选择变化事件
const isRestoring = ref(false)

// 获取商品唯一标识
const getProductKey = (product: any) => {
  return product.id || product.commodityId
}

// 检查商品是否已选中
const isProductSelected = (product: any) => {
  const productKey = getProductKey(product)
  return selectedProducts.value.some(item => getProductKey(item) === productKey)
}

// 存储上一次的选中状态，用于计算差异
const lastSelection = ref<any[]>([])

// 表格多选相关 - 真正的增量更新逻辑
const handleSelectionChange = (val: any[]) => {
  // 如果正在恢复选中状态，则忽略此次变化
  if (isRestoring.value) {
    // 更新上一次选中状态
    lastSelection.value = [...val]
    return
  }

  // 找出新选中的商品（在 val 中但不在 lastSelection 中）
  const newlySelected = val.filter(
    item =>
      !lastSelection.value.some(
        last => getProductKey(last) === getProductKey(item)
      )
  )

  // 找出取消选中的商品（在 lastSelection 中但不在 val 中）
  const deselected = lastSelection.value.filter(
    item => !val.some(current => getProductKey(current) === getProductKey(item))
  )

  // 从全局列表中移除取消选中的商品
  deselected.forEach(item => {
    const index = selectedProducts.value.findIndex(
      selected => getProductKey(selected) === getProductKey(item)
    )
    if (index > -1) {
      selectedProducts.value.splice(index, 1)
    }
  })

  // 向全局列表添加新选中的商品
  newlySelected.forEach(item => {
    // 检查是否已存在，避免重复
    const exists = selectedProducts.value.some(
      selected => getProductKey(selected) === getProductKey(item)
    )
    if (!exists) {
      selectedProducts.value.push({
        commodityId: item.id,
        ...item
      })
    }
  })

  // 更新上一次选中状态
  lastSelection.value = [...val]
}

// 设置当前页面的选中状态
const setCurrentPageSelection = () => {
  // 使用 setTimeout 确保表格完全渲染完成
  setTimeout(() => {
    if (!tableRef.value) {
      return
    }

    // 根据全局已选列表找到当前页面需要选中的商品
    const toSelectProducts = productList.value.filter(product =>
      isProductSelected(product)
    )

    if (toSelectProducts.length > 0) {
      // 设置恢复标志，防止触发选择变化事件
      isRestoring.value = true

      // 清空当前选中状态
      tableRef.value.clearSelection()

      toSelectProducts.forEach(product => {
        tableRef.value.toggleRowSelection(product, true)
      })

      // 验证选中结果并重置标志
      setTimeout(() => {
        const currentSelection = tableRef.value.getSelectionRows()

        // 如果没有成功选中任何商品，尝试其他方法
        if (currentSelection.length === 0 && toSelectProducts.length > 0) {
          // 清除恢复标志，手动触发选择变化
          isRestoring.value = false
          handleSelectionChange(toSelectProducts)
        } else {
          // 更新上次选中状态为当前恢复的选中状态
          lastSelection.value = [...currentSelection]
          isRestoring.value = false
        }
      }, 100)
    } else {
      // 确保清空选择
      tableRef.value.clearSelection()
    }
  }, 200) // 增加延迟确保表格渲染完成
}

// 搜索产品
const handleSearch = () => {
  currentPage.value = 1
  init()
}

const init = async () => {
  // 构建请求参数，使用类型断言
  const params: ProductListFilter & Record<string, any> = {
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }

  // 添加搜索条件
  if (formData.value.name) {
    params.productName = formData.value.name
  }

  const res = await getProductList(params)
  productList.value = res.result.records
  total.value = res.result.total

  // 重置上次选中状态，因为数据已更新
  lastSelection.value = []

  // 数据加载完成后，设置当前页面的选中状态
  setCurrentPageSelection()
}

const handlePageChange = () => {
  init()
}

const handleOpen = () => {
  // 打开弹窗时清空已选列表
  selectedProducts.value = []
  lastSelection.value = []
  init()
}

const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择商品')
    return
  }

  // 向父组件传递已选商品列表
  emit('confirm', selectedProducts.value)
  visible.value = false
}

// 移除已选商品
const removeSelectedProduct = (index: number) => {
  selectedProducts.value.splice(index, 1)
  // 更新当前页面的选中状态
  setCurrentPageSelection()
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="添加产品"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex justify-between mb-16">
      <div class="flex items-center">
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder="请输入产品名称"
        ></el-input>
        <el-button type="primary" class="ml-16" @click="handleSearch"
          >搜索</el-button
        >
      </div>

      <div class="flex items-center">
        <el-popover placement="bottom-start" :width="500" trigger="click">
          <template #reference>
            <el-button>已选{{ selectedProducts.length }}个产品</el-button>
          </template>
          <div>
            <el-table
              :data="selectedProducts"
              :border="true"
              style="width: 100%"
              max-height="300"
            >
              <el-table-column label="SKU" prop="productSkuCode" />
              <el-table-column label="产品名称" prop="productName" />
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click="removeSelectedProduct($index)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-popover>
      </div>
    </div>
    <el-table
      ref="tableRef"
      :data="productList"
      :border="true"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="60" />
      <el-table-column label="SKU" prop="productSkuCode" />
      <el-table-column label="产品名称" prop="productName" />
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
      @change="handlePageChange"
    />

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
