import { defineStore } from 'pinia'
import { store } from '../index'
import type {
  BasicEntity,
  DistributionBasicEntity,
  ReleaseTypeEntity,
  SpecificationsBasicEntity
} from '@/api/goods/types'
import type { SelectedAttr } from '@/views/Product/Goods/type'

interface Step3Data {
  formData: SpecificationsBasicEntity
  tableData: any[]
  selectedAttr: SelectedAttr[]
}

interface GoodsState {
  publishType: string | undefined
  productReleaseTypeId: string | undefined
  productBasicId: string | undefined
  productDistributionId: string | undefined
  step0FormData: ReleaseTypeEntity | undefined
  step1FormData: BasicEntity | undefined
  step2FormData: DistributionBasicEntity | undefined
  step3FormData: Step3Data | undefined
  fileList: any[] | undefined
}

export const useGoodsStore = defineStore('goods', {
  state: (): GoodsState => {
    return {
      publishType: undefined,
      productReleaseTypeId: undefined,
      productBasicId: undefined,
      productDistributionId: undefined,
      step0FormData: undefined,
      step1FormData: undefined,
      step2FormData: undefined,
      step3FormData: undefined,
      fileList: undefined
    }
  },
  getters: {
    /**
     * @description: 是否仅备货至海外仓(海外仓不需要填写分销信息)
     * @return {*}
     */
    isOnlyOverseasWarehouse(): boolean {
      return this.publishType === 'onlyOverseasWarehouse'
    },
    getProductReleaseTypeId(): string | undefined {
      return this.productReleaseTypeId
    },
    getProductBasicId(): string | undefined {
      return this.productBasicId
    },
    getProductDistributionId(): string | undefined {
      return this.productDistributionId
    },
    getStep0FormData(): ReleaseTypeEntity | undefined {
      return this.step0FormData
    },
    getStep1FormData(): BasicEntity | undefined {
      return this.step1FormData
    },
    getStep2FormData(): DistributionBasicEntity | undefined {
      return this.step2FormData
    },
    getStep3FormData(): Step3Data | undefined {
      return this.step3FormData
    },
    getFileList(): any[] | undefined {
      return this.fileList
    }
  },
  actions: {
    clearCurGoodsInfo() {
      this.setPublishType(undefined)
      this.setProductReleaseTypeId(undefined)
      this.setProductBasicId(undefined)
      this.setProductDistributionId(undefined)
      this.setStep0FormData(undefined)
      this.setStep1FormData(undefined)
      this.setStep2FormData(undefined)
      this.setStep3FormData(undefined)
    },
    setPublishType(type: string | undefined) {
      this.publishType = type
    },
    setProductReleaseTypeId(id: string | undefined) {
      this.productReleaseTypeId = id
    },
    setProductBasicId(id: string | undefined) {
      this.productBasicId = id
    },
    setProductDistributionId(id: string | undefined) {
      this.productDistributionId = id
    },
    setStep0FormData(data: ReleaseTypeEntity | undefined) {
      this.step0FormData = data
    },
    setStep1FormData(data: BasicEntity | undefined) {
      this.step1FormData = data
    },
    setStep2FormData(data: DistributionBasicEntity | undefined) {
      this.step2FormData = data
    },
    setStep3FormData(data: Step3Data | undefined) {
      this.step3FormData = data
    },
    setFileList(file: any[] | undefined) {
      this.fileList = file
    }
  }
})

export const useGoodsStoreWithOut = () => {
  return useGoodsStore(store)
}
