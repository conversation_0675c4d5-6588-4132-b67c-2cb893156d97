import { defineStore } from 'pinia'
import { store } from '../index'
import type { ComponentSize } from 'element-plus'
import { useStorage } from '@/hooks/useStorage'
// import { setCssVar, humpToUnderline } from '@/utils'
// import { colorIsDark, hexToRGB, lighten, mix } from '@/utils/color'
// import { ElMessage, type ComponentSize } from 'element-plus'
// import { useCssVar } from '@vueuse/core'
// import { unref } from 'vue'
// import { useDark } from '@vueuse/core'

interface AppState {
  currentSize: ComponentSize
}

const { storagePrefixKey } = useStorage()

export const useAppStore = defineStore('app', {
  state: (): AppState => {
    return {
      currentSize: 'default' // 组件尺寸
    }
  },
  getters: {
    getCurrentSize(): ComponentSize {
      return this.currentSize
    }
  },
  actions: {},
  persist: {
    key: `${storagePrefixKey}_app`,
    storage: localStorage
  }
})

export const useAppStoreWithOut = () => {
  return useAppStore(store)
}
