import { defineStore } from 'pinia'
import { store } from '../index'
import { ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/useI18n'
// import { loginOutApi } from '@/api/login'
// import { useTagsViewStore } from './tagsView'
import router from '@/router'
import { useStorage } from '@/hooks/useStorage'
import { enCrypt } from '@/utils/crypto'
import { loginApi, logOutApi } from '@/api/login'
import type { LoginRequest, LoginResponse } from '@/api/login/types'

interface UserState {
  userInfo?: LoginRequest
  tokenKey: string
  token: string
  roleRouters?: string[] | AppCustomRouteRecordRaw[]
  rememberMe: boolean
  loginInfo?: LoginResponse
}

const { storagePrefixKey } = useStorage()

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    return {
      userInfo: undefined,
      tokenKey: 'Authorization',
      token: '',
      roleRouters: undefined,
      // 记住我
      rememberMe: true,
      loginInfo: undefined
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token
    },
    getUserInfo(): LoginRequest | undefined {
      return this.userInfo
    },
    getRoleRouters(): string[] | AppCustomRouteRecordRaw[] | undefined {
      console.log('this.roleRouters', this.roleRouters)

      return this.roleRouters
    },
    getRememberMe(): boolean {
      return this.rememberMe
    },
    getLoginInfo(): LoginResponse | undefined {
      return this.loginInfo
    }
  },
  actions: {
    setTokenKey(tokenKey: string) {
      this.tokenKey = tokenKey
    },
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo?: LoginRequest) {
      this.userInfo = userInfo
    },
    setRoleRouters(roleRouters: string[] | AppCustomRouteRecordRaw[]) {
      this.roleRouters = roleRouters
    },
    /**
     * @description: 登出确认
     * @return {*}
     */
    logoutConfirm(): void {
      const { t } = useI18n()
      ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
        .then(async () => {
          this.reset()
          const res = await logOutApi().catch(() => {})
          if (res) {
            this.reset()
          }
        })
        .catch(() => {})
    },
    /**
     * @description: 登出后重置状态
     * @return {*}
     */
    reset(): void {
      // const tagsViewStore = useTagsViewStore()
      // tagsViewStore.delAllViews()
      this.setToken('')
      this.setUserInfo(undefined)
      this.setRoleRouters([])
      router.replace('/login')
    },
    logout() {
      this.reset()
    },
    setRememberMe(rememberMe: boolean) {
      this.rememberMe = rememberMe
    },
    setLoginInfo(loginInfo: LoginResponse | undefined) {
      this.loginInfo = loginInfo
    },
    /**
     * @description: 登录逻辑
     * @return {*}
     */
    async doLogin(form: LoginRequest): Promise<void> {
      try {
        const params = Object.assign({}, form, {
          password: enCrypt(form.password),
          username: enCrypt(form.username)
        })
        const loginRes = await loginApi(params)
        const { access_token } = loginRes.result
        this.setToken(access_token)
        this.setLoginInfo(loginRes.result)
        return Promise.resolve()
      } catch (e) {
        this.reset()
        return Promise.reject(e)
      }
    },
    /**
     * @description: 验证码图片
     * @return {*}
     */
    randomImage() {
      // let timeKey = new Date().getTime()
      // state.timeKey = timeKey.toString()
      // getRandomImage(timeKey).then(res => {
      //   state.codeSrc = res.result
      // })
    }
  },
  persist: {
    key: `${storagePrefixKey}_user`,
    storage: localStorage
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
