<script setup lang="ts">
import BreadCrumb from './components/BreadCrumb.vue'
defineOptions({
  name: '<PERSON>erR<PERSON>'
})
</script>

<template>
  <div
    class="w-full h-[--headerHeight] bg-white flex justify-between items-center px-8"
  >
    <div class="flex items-center">
      <!-- <div class="i-ant-design:menu-unfold-outlined text-18"></div>
      <div class="i-ant-design:menu-fold-outlined text-18"></div> -->
      <!-- class="ml-10" -->
      <BreadCrumb></BreadCrumb>
      <!-- <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">homepage</el-breadcrumb-item>
        <el-breadcrumb-item>promotion list</el-breadcrumb-item>
        <el-breadcrumb-item>promotion detail</el-breadcrumb-item>
      </el-breadcrumb> -->
    </div>

    <div class="flex items-center">
      <LocaleDropdown></LocaleDropdown>
      <div class="w-20"></div>
      <UserInfo></UserInfo>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
