<script setup lang="ts">
import { getParentPaths, findRouteByPath } from '@/router/utils'
import { useI18n } from '@/hooks/useI18n'
import { usePermissionStore } from '@/store/modules/permission'

const { t } = useI18n()

const permissionStore = usePermissionStore()

const route = useRoute()
const levelList = ref<AppRouteRecordRaw[]>([])
const router = useRouter()

const routes: any = permissionStore.getRouters

const getBreadcrumb = (): void => {
  // 当前路由信息
  let currentRoute: any

  currentRoute = findRouteByPath(router.currentRoute.value.path, routes)

  // 当前路由的父级路径组成的数组
  const parentRoutes = getParentPaths(router.currentRoute.value.name as string, routes, 'name')
  // 存放组成面包屑的数组
  const matched: any[] = []

  // 获取每个父级路径对应的路由信息
  parentRoutes.forEach((path: string) => {
    if (path !== '/') matched.push(findRouteByPath(path, routes))
  })

  matched.push(currentRoute)

  matched.forEach((item, index) => {
    if (currentRoute?.query || currentRoute?.params) return
    if (item?.children) {
      item.children.forEach((v: any) => {
        if (v?.meta?.title === item?.meta?.title) {
          matched.splice(index, 1)
        }
      })
    }
  })

  levelList.value = matched.filter(item => item?.meta && item?.meta.title !== false)
}

const handleLink = (item: AppRouteRecordRaw) => {
  console.log('item', item)

  const { redirect, name, path } = item
  if (redirect) {
    // 重定向的路由如果隐藏的话直接返回 不跳转
    const result = item.children?.find((v: AppRouteRecordRaw) => {
      if (v.path === redirect) {
        return v
      }
    })
    if (result?.meta?.hidden) {
      return
    }

    router.push(redirect as any)
  } else {
    if (name) {
      // if (item.query) {
      //   router.push({
      //     name,
      //     query: item.query
      //   })
      // } else if (item.params) {
      //   router.push({
      //     name,
      //     params: item.params
      //   })
      // } else {
      //   router.push({ name })
      // }

      router.push({ name })
    } else {
      router.push({ path })
    }
  }
}

onMounted(() => {
  getBreadcrumb()
})

watch(
  () => route.path,
  () => {
    getBreadcrumb()
  },
  {
    deep: true
  }
)
</script>

<template>
  <el-breadcrumb class="!leading-[50px] select-none" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="item in levelList" :key="item.path" class="!inline !items-stretch">
        <a @click.prevent="handleLink(item)">
          {{ t(item.meta.title!) }}
        </a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>
