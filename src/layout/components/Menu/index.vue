<script lang="ts" setup>
import { usePermissionStore } from '@/store/modules/permission'
import { hasOneShowingChild } from '@/utils/routerHelper'
import MenuTitle from './MenuTitle.vue'

defineOptions({
  name: 'MenuRoot'
})

const route = useRoute()

const defaultActive = ref('/')
const permissionStore = usePermissionStore()

const routers = computed(() => permissionStore.getRouters)

watch(
  () => route.path,
  () => {
    defaultActive.value = route.path
  },
  {
    immediate: true,
    deep: true
  }
)

const _hasOneShowingChild = (v: any): boolean => {
  const meta = v.meta ?? {}
  const { oneShowingChild, onlyOneChild } = hasOneShowingChild(v.children, v)

  return (oneShowingChild &&
    (!onlyOneChild?.children || onlyOneChild?.noShowingChildren) &&
    !meta?.alwaysShow) as boolean
}
</script>

<template>
  <div
    class="h-[calc(100%-var(--headerHeight))] border-r-[1px] border-r-solid border-[#dcdfe6] custom-menu"
  >
    <el-scrollbar class="bg-white">
      <el-menu
        :default-active="defaultActive"
        router
        unique-opened
        :collapse="false"
      >
        <template v-for="route of routers" :key="route.path">
          <template v-if="!route.meta?.hidden">
            <el-menu-item v-if="_hasOneShowingChild(route)" :index="route.path">
              <MenuTitle :meta="route.meta" />
            </el-menu-item>
            <!-- 二级菜单 -->
            <el-sub-menu v-else :index="route.path">
              <template #title>
                <MenuTitle :meta="route.meta" />
              </template>
              <template
                v-for="routeChildItem in route.children"
                :key="routeChildItem.path"
              >
                <template v-if="!routeChildItem.meta?.hidden">
                  <!-- 三级菜单 -->
                  <el-sub-menu
                    v-if="
                      !routeChildItem.meta.alwaysShow &&
                      routeChildItem.children?.length
                    "
                    :index="routeChildItem.path"
                  >
                    <template #title>
                      <MenuTitle :meta="routeChildItem.meta" />
                    </template>
                    <template
                      v-for="rccItem in routeChildItem.children"
                      :key="rccItem.path"
                    >
                      <template v-if="!rccItem.meta?.hidden">
                        <el-menu-item :index="rccItem.path">
                          <MenuTitle :meta="rccItem.meta" />
                        </el-menu-item>
                      </template>
                    </template>
                  </el-sub-menu>
                  <el-menu-item v-else :index="routeChildItem.path">
                    <MenuTitle :meta="routeChildItem.meta" />
                  </el-menu-item>
                </template>
              </template>
            </el-sub-menu>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.custom-menu {
  &:deep(.el-menu) {
    border-right: none;
  }
}
</style>
