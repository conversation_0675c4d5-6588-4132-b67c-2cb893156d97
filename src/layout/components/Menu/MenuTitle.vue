<script setup lang="ts">
import type { RouteMetaCustom } from 'types/router'

defineOptions({
  name: 'MenuTitle'
})

const { meta } = defineProps<{
  meta: RouteMetaCustom
}>()
</script>

<template>
  <div v-if="meta.icon" class="flex items-center">
    <!-- {{ meta.icon }} -->
    <div :class="[meta.icon]" class="text-16 mr-5"></div>
    <!-- <div class="text-16 mr-5 i-ri:accessibility-fill"></div> -->
    <div>{{ meta.title }}</div>
  </div>
  <span v-else>{{ meta.title }}</span>
</template>

<style lang="scss" scoped></style>
