<template>
  <div class="flex w-100vw h-100vh bg-[--defbg]">
    <div class="w-full h-full max-w-[--menuWidth]">
      <!-- logo -->
      <div
        class="h-[--headerHeight] bg-white border-r-[1px] border-b-[1px] border-b-solid border-r-solid border-[#dcdfe6]"
      ></div>
      <!-- 菜单 -->
      <Menu></Menu>
    </div>
    <div class="w-[calc(100%-var(--menuWidth))] h-full">
      <!-- Header -->
      <Header></Header>
      <div class="w-full h-[calc(100%-var(--headerHeight))]">
        <!-- <el-scrollbar  class="w-full h-[calc(100%-var(--headerHeight))]" height="100%" class="py-20 px-20"> -->
        <el-scrollbar class="w-full h-[calc(100%-var(--headerHeight))] py-20 px-20">
          <router-view></router-view>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Header from './components/Header/index.vue'
import Menu from './components/Menu/index.vue'
</script>

<style lang="scss" scoped></style>
