import { createApp } from 'vue'

// 引入unocss
import '@/plugins/unocss'

// import './styles/index.scss'

// 引入element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入全局样式
import '@/styles/index.scss'

// 引入路由
import { setupRouter } from '@/router'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

import App from './App.vue'

import './permission'

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupElementPlus(app)

  setupRouter(app)

  app.mount('#app')
}

setupAll()
