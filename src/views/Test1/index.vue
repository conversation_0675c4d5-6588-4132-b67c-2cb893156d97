<template>
  <div>
    <h1 class="cc">1111</h1>
    <div class="i-vscode-icons:file-type-light-pnpm" />
    <div class="i-ri:4k-fill"></div>
    <div class="i-ri:home-2-line text-36"></div>
    <el-input :prefix-icon="'i-ep:list'" style="width: 240px" placeholder="Please input" />
    <!-- <el-icon :size="20">
      <Edit />
    </el-icon>

    <el-icon><CirclePlus /></el-icon>

    <el-icon color="#409efc" class="no-inherit">
      <Share />
    </el-icon> -->
    <el-button type="primary" @click="handlego">
      <div class="i-ri:4k-fill mr-8"></div>
      Primary
    </el-button>
    <h2>el-button+图标:</h2>
    <el-button type="primary">
      <el-icon> <i-ep-edit /> </el-icon> 新增11
    </el-button>
    <el-button type="primary" icon="i-ep:credit-card"> 新增1122221 </el-button>
    <el-button type="primary" icon="i-ep:monitor"> 新增 </el-button>
    <el-button type="primary" icon="i-ep-credit-card"> 新增 </el-button>
    <el-button type="primary" icon="i-ep-edit"> 新增 </el-button>
    <el-button type="primary" icon="i-ri:home-2-line"> 新增 </el-button>
    <div>{{ testRef }}</div>
    <el-button type="primary" @click="updateTestRef">updateTestRef</el-button>
    <el-button type="primary" @click="getTestUnref">getTestUnref</el-button>
    <el-button type="primary" @click="handlego">Primary</el-button>
    <el-button type="primary" @click="handleLanguage">切换语言</el-button>
    <div class="m-4">
      <p>Child options expand when clicked (default)</p>
      <el-cascader v-model="value" :options="options" @change="handleChange" />
    </div>
    <div class="m-4">
      <p>Child options expand when hovered</p>
      <el-cascader v-model="value" :options="options" :props="props" @change="handleChange" />
    </div>

    <el-checkbox-group v-model="checkList">
      <el-checkbox label="Option A" value="Value A" />
      <el-checkbox label="Option B" value="Value B" />
      <el-checkbox label="Option C" value="Value C" />
      <el-checkbox label="disabled" value="Value disabled" disabled />
      <el-checkbox label="selected and disabled" value="Value selected and disabled" disabled />
    </el-checkbox-group>
    <LocaleDropdown />
    <div>多语言</div>
    <ul class="flex">
      <li>{{ $t('common.inputText') }}</li>
      <li class="ml-3">{{ t('common.inputText') }}</li>
    </ul>

    <button
      bg="blue-400 hover:blue-500 dark:blue-500 dark:hover:blue-600"
      text=" white"
      p="y-10 x-5"
      border="2 rounded blue-200"
    >
      Button
    </button>

    <div flex-center class="bg-[#000]/45" w-100 line-clamp-3 h-80 m-2 rounded text-teal-400>
      标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签标签
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useI18n } from '@/hooks/useI18n'
import { useLocale } from '@/hooks/useLocale'
import { useLocaleStoreWithOut } from '@/store/modules/locale'
const { t } = useI18n()

defineOptions({
  name: 'TestView'
})

const testRef = ref('123')

const updateTestRef = () => {
  if (testRef.value === '123') {
    testRef.value = '456'
  } else if (testRef.value === '456') {
    testRef.value = '789'
  } else {
    testRef.value = '456'
  }
}

const getTestUnref = () => {
  const originRef = unref(testRef)
  console.log('originRef', originRef)
}

const { changeLocale } = useLocale()
const { currentLocale } = useLocaleStoreWithOut()
console.log('t', t('common.inputText'))

const router = useRouter()
const handlego = () => {
  router.push({
    path: '/'
  })
}

const handleLanguage = () => {
  changeLocale(currentLocale.lang === 'en' ? 'zh-CN' : 'en')
}

const checkList = ref(['Value selected and disabled', 'Value A'])

const value = ref([])

const props = {
  expandTrigger: 'hover' as const
}

const handleChange = (value: any) => {
  console.log(value)
}

const options = [
  {
    value: 'guide',
    label: 'Guide',
    children: [
      {
        value: 'disciplines',
        label: 'Disciplines',
        children: [
          {
            value: 'consistency',
            label: 'Consistency'
          },
          {
            value: 'feedback',
            label: 'Feedback'
          },
          {
            value: 'efficiency',
            label: 'Efficiency'
          },
          {
            value: 'controllability',
            label: 'Controllability'
          }
        ]
      },
      {
        value: 'navigation',
        label: 'Navigation',
        children: [
          {
            value: 'side nav',
            label: 'Side Navigation'
          },
          {
            value: 'top nav',
            label: 'Top Navigation'
          }
        ]
      }
    ]
  },
  {
    value: 'component',
    label: 'Component',
    children: [
      {
        value: 'basic',
        label: 'Basic',
        children: [
          {
            value: 'layout',
            label: 'Layout'
          },
          {
            value: 'color',
            label: 'Color'
          },
          {
            value: 'typography',
            label: 'Typography'
          },
          {
            value: 'icon',
            label: 'Icon'
          },
          {
            value: 'button',
            label: 'Button'
          }
        ]
      },
      {
        value: 'form',
        label: 'Form',
        children: [
          {
            value: 'radio',
            label: 'Radio'
          },
          {
            value: 'checkbox',
            label: 'Checkbox'
          },
          {
            value: 'input',
            label: 'Input'
          },
          {
            value: 'input-number',
            label: 'InputNumber'
          },
          {
            value: 'select',
            label: 'Select'
          },
          {
            value: 'cascader',
            label: 'Cascader'
          },
          {
            value: 'switch',
            label: 'Switch'
          },
          {
            value: 'slider',
            label: 'Slider'
          },
          {
            value: 'time-picker',
            label: 'TimePicker'
          },
          {
            value: 'date-picker',
            label: 'DatePicker'
          },
          {
            value: 'datetime-picker',
            label: 'DateTimePicker'
          },
          {
            value: 'upload',
            label: 'Upload'
          },
          {
            value: 'rate',
            label: 'Rate'
          },
          {
            value: 'form',
            label: 'Form'
          }
        ]
      },
      {
        value: 'data',
        label: 'Data',
        children: [
          {
            value: 'table',
            label: 'Table'
          },
          {
            value: 'tag',
            label: 'Tag'
          },
          {
            value: 'progress',
            label: 'Progress'
          },
          {
            value: 'tree',
            label: 'Tree'
          },
          {
            value: 'pagination',
            label: 'Pagination'
          },
          {
            value: 'badge',
            label: 'Badge'
          }
        ]
      },
      {
        value: 'notice',
        label: 'Notice',
        children: [
          {
            value: 'alert',
            label: 'Alert'
          },
          {
            value: 'loading',
            label: 'Loading'
          },
          {
            value: 'message',
            label: 'Message'
          },
          {
            value: 'message-box',
            label: 'MessageBox'
          },
          {
            value: 'notification',
            label: 'Notification'
          }
        ]
      },
      {
        value: 'navigation',
        label: 'Navigation',
        children: [
          {
            value: 'menu',
            label: 'Menu'
          },
          {
            value: 'tabs',
            label: 'Tabs'
          },
          {
            value: 'breadcrumb',
            label: 'Breadcrumb'
          },
          {
            value: 'dropdown',
            label: 'Dropdown'
          },
          {
            value: 'steps',
            label: 'Steps'
          }
        ]
      },
      {
        value: 'others',
        label: 'Others',
        children: [
          {
            value: 'dialog',
            label: 'Dialog'
          },
          {
            value: 'tooltip',
            label: 'Tooltip'
          },
          {
            value: 'popover',
            label: 'Popover'
          },
          {
            value: 'card',
            label: 'Card'
          },
          {
            value: 'carousel',
            label: 'Carousel'
          },
          {
            value: 'collapse',
            label: 'Collapse'
          }
        ]
      }
    ]
  },
  {
    value: 'resource',
    label: 'Resource',
    children: [
      {
        value: 'axure',
        label: 'Axure Components'
      },
      {
        value: 'sketch',
        label: 'Sketch Templates'
      },
      {
        value: 'docs',
        label: 'Design Documentation'
      }
    ]
  }
]
</script>

<style lang="scss" scoped>
.cc {
  // color: $defColor;
}
</style>
