// const options1: any[] = [
//   {
//     value: '1',
//     label: '颜色'
//   },
//   {
//     value: '2',
//     label: '尺寸'
//   },
//   {
//     value: '3',
//     label: '重量'
//   },
//   {
//     value: '4',
//     label: '材质'
//   },
//   {
//     value: '5',
//     label: '风格'
//   },
//   {
//     value: '6',
//     label: '型号'
//   },
//   {
//     value: '7',
//     label: '商品数量'
//   },
//   {
//     value: '8',
//     label: '容量'
//   }
// ]
/**
 * @description: 根据value获取options中的label
 * @param {string} value
 * @param {any} options
 * @return {*}
 */
export const getLabelByValue = (value: string, options: any[]) => {
  const item = options.find(item => item.itemValue === value)
  return item ? item.itemText : ''
}

/**
 * @description: 将规格转为table
 * @param {any} data
 * @return {*}
 */
export function generateCombinations(data: any[]): any {
  const result: any[][] = []
  // 过滤 values 中 value 为空的数据
  const filteredData = data.map(item => ({
    label: item.label,
    values: item.values.filter((val: { value: string }) => val.value)
  }))

  function backtrack(index: number, currentCombination: any[]) {
    if (index === filteredData.length) {
      // 当遍历完所有类别时，将当前组合添加到结果数组中
      result.push([...currentCombination])
      return
    }

    const values = filteredData[index].values
    for (let i = 0; i < values.length; i++) {
      currentCombination.push(values[i])
      // 递归调用以处理下一个类别
      backtrack(index + 1, currentCombination)
      // 回溯，移除最后添加的元素以尝试其他可能
      currentCombination.pop()
    }
  }

  backtrack(0, [])
  return result
}
