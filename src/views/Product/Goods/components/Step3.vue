<script setup lang="ts">
import { saveSpecificationsBasic } from '@/api/goods'
import type { SpecificationsBasicEntity } from '@/api/goods/types'
import { useFormData } from '@/hooks/useFormData'
import { generateCombinations, getLabelByValue } from '../helper'
import { useGoodsStore } from '@/store/modules/goods'
import type { SelectedAttr } from '../type'
import { cloneDeep } from 'lodash-es'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'Step3'
})

const emits = defineEmits(['nextStep', 'prevStep'])

const goodsStore = useGoodsStore()

const specification_attribute = useDictItem('specification_attribute')

const { formData, formDataRef, rules } = useFormData<SpecificationsBasicEntity>(
  {
    productAttr: []
  },
  {
    productAttr: [
      { required: true, message: '请选择规格属性', trigger: 'change' }
    ]
  }
)
const tableData = ref<any[]>([])

const selectedAttr = ref<SelectedAttr[]>([])

const handleProductAttrChange = (value: any) => {
  selectedAttr.value = value.map((item: any, index: number) => {
    const _label = getLabelByValue(item, specification_attribute.value)
    return {
      label: _label,
      key: index == 0 ? 'productAttrOne' : 'productAttrTwo',
      values: [
        {
          label: _label,
          value: undefined
        }
      ]
    }
  })
}

const handleAddAttrBySubItem = (item: any) => {
  item.values.push({
    label: item.label,
    value: undefined
  })
}

const defaultRow = {
  productAttrOne: undefined,
  productAttrTwo: undefined,
  productSkuCode: undefined,
  productSkuUrl: cloneDeep([]),
  productLength: undefined,
  productWidth: undefined,
  productHeight: undefined,
  productWeight: undefined,
  productSupplyPrice: undefined
}

/**
 * @description: 生成SKU
 * @return {*}
 */
const handleGenerateSku = () => {
  const attrArr = generateCombinations(selectedAttr.value)
  tableData.value = []
  for (const element of attrArr) {
    tableData.value.push({
      ...cloneDeep(defaultRow),
      productAttrOne: element[0]?.value,
      productAttrTwo: element[1]?.value,
      productReleaseTypeId: goodsStore.getProductReleaseTypeId,
      productBasicId: goodsStore.getProductBasicId,
      productDistributionId: goodsStore.getProductDistributionId
    })
  }
}

/**
 * @description: 重置sku
 * @return {*}
 */
const handleResetSku = () => {
  formData.value.productAttr = []
  selectedAttr.value = []
  tableData.value = []
}

const init = () => {
  if (goodsStore.step3FormData) {
    formData.value = goodsStore.step3FormData.formData
    tableData.value = goodsStore.step3FormData.tableData
    selectedAttr.value = goodsStore.step3FormData.selectedAttr
  }
}
init()

/**
 * @description: 上一步
 * @return {*}
 */
const prevStep = () => {
  goodsStore.setStep3FormData({
    formData: formData.value,
    tableData: tableData.value,
    selectedAttr: selectedAttr.value
  })
  emits('prevStep')
}

/**
 * @description : 删除行
 * @param {*} _row
 * @param {*} _column
 * @param {*} index
 * @return {*}
 */
const removeRow = (_row: any, _column: any, index: any): any => {
  tableData.value.splice(index, 1)
}

/**
 * @description: 填充数据
 * @return {*}
 */
const fillData = ref({
  productLength: undefined,
  productWidth: undefined,
  productHeight: undefined,
  productWeight: undefined,
  productSupplyPrice: undefined
})

/**
 * @description: 填充数据
 * @return {*}
 */
const handleFillRow = () => {
  for (const element of tableData.value) {
    Object.assign(element, fillData.value)
  }
}

const nextStep = () => {
  console.log('selectedAttr', selectedAttr.value)

  console.log('formData', formData.value)
  console.log('tableData.value', tableData.value)
  // return
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      const productAttrOneAll =
        selectedAttr.value[0] &&
        selectedAttr.value[0].values
          .filter((item: any) => item.value)
          .map((item: any) => item.value)
          .join(',')

      const productAttrTwoAll =
        selectedAttr.value[1] &&
        selectedAttr.value[1].values
          .filter((item: any) => item.value)
          .map((item: any) => item.value)
          .join(',')

      const params = {
        productSpeBasic: tableData.value.map((item: any) => {
          return {
            ...item,
            productSkuUrl: item.productSkuUrl
              ?.map((item: { url: any }) => item.url)
              .join(','),
            productAttr: formData.value.productAttr?.join(',')
          }
        }),
        productAttrAll: formData.value.productAttr?.join(','),
        productAttrOneAll,
        productAttrTwoAll
      }
      console.log('params', params)

      const response = await saveSpecificationsBasic(params)
      if (response) {
        emits('nextStep')
      }
    }
  })
}
</script>

<template>
  <ContentWrap title="商品规格信息" class="mt-20">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      require-asterisk-position="right"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="规格属性" prop="productAttr">
            <el-checkbox-group
              v-model="formData.productAttr"
              :max="2"
              @change="handleProductAttrChange"
            >
              <el-checkbox
                v-for="item of specification_attribute"
                :key="item.itemValue"
                :label="item.itemText"
                :value="item.itemValue"
              />
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <template v-for="(item, index) in selectedAttr" :key="index">
          <el-col :span="24">
            <el-form-item :label="item.label" prop="" :key="index">
              <template
                v-for="(subItem, subIndex) of item.values"
                :key="subIndex"
              >
                <el-input
                  v-model.trim="subItem.value"
                  clearable
                  placeholder=""
                  class="!w-100"
                  :class="{ 'ml-16': subIndex !== 0 }"
                ></el-input>
              </template>

              <el-button
                type="primary"
                class="ml-16"
                @click="() => handleAddAttrBySubItem(item)"
                >添加规格</el-button
              >
            </el-form-item>
          </el-col>
        </template>

        <el-col :span="24">
          <el-button type="primary" @click="handleGenerateSku"
            >生成SKU</el-button
          >
          <el-button @click="handleResetSku">重置</el-button>
          <el-button v-if="!tableData?.length" @click="prevStep"
            >上一步</el-button
          >
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap v-if="tableData?.length" class="mt-20">
    <!-- 批量填充 -->
    <el-row :gutter="16" class="w-full mb-20">
      <el-col :span="4">
        <div class="mb-8">长度(CM)</div>
        <el-input
          v-model.trim="fillData.productLength"
          clearable
          placeholder=""
        ></el-input>
      </el-col>

      <el-col :span="4">
        <div class="mb-8">宽度(CM)</div>
        <el-input
          v-model.trim="fillData.productWidth"
          clearable
          placeholder=""
        ></el-input>
      </el-col>

      <el-col :span="4">
        <div class="mb-8">高度(CM)</div>
        <el-input
          v-model.trim="fillData.productHeight"
          clearable
          placeholder=""
        ></el-input>
      </el-col>

      <el-col :span="4">
        <div class="mb-8">重量(g)</div>
        <el-input
          v-model.trim="fillData.productWeight"
          clearable
          placeholder=""
        ></el-input>
      </el-col>

      <el-col :span="4">
        <div class="mb-8">供货价(美元)</div>
        <el-input
          v-model.trim="fillData.productSupplyPrice"
          clearable
          placeholder=""
        ></el-input>
      </el-col>
      <el-col :span="4">
        <div class="mb-8 lh-22">&nbsp;</div>
        <el-button type="primary" @click="handleFillRow">批量填充</el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" :border="true" style="width: 100%">
      <template v-for="item of selectedAttr" :key="item.key">
        <el-table-column :label="item.label" width="180">
          <template #default="{ row }">
            <el-input v-model.trim="row[item.key]" disabled></el-input>
          </template>
        </el-table-column>
      </template>

      <el-table-column label="商品编码(SKU)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productSkuCode"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="SKU主图" prop="name" width="180">
        <template #default="{ row, $index }">
          <DUpload
            v-model="row.productSkuUrl"
            :limit="1"
            :key="$index"
          ></DUpload>
        </template>
      </el-table-column>
      <el-table-column label="长度(CM)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productLength"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="宽度(CM)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productWidth"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="高度(CM)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productHeight"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="重量(g)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productWeight"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="供货价(美元)" prop="name" width="180">
        <template #default="{ row }">
          <el-input
            v-model.trim="row.productSupplyPrice"
            clearable
            placeholder=""
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="90">
        <template #default="{ row, column, $index }">
          <el-button text type="danger" @click="removeRow(row, column, $index)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="w-full flex justify-end mt-16">
      <el-button @click="prevStep">上一步</el-button>
      <el-button type="primary" @click="nextStep">提交</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
