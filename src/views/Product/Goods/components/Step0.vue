<script setup lang="ts">
import { saveReleaseType } from '@/api/goods'
import type { ReleaseTypeEntity } from '@/api/goods/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'
import { useGoodsStore } from '@/store/modules/goods'

defineOptions({
  name: 'Step0'
})
const goodsStore = useGoodsStore()
const emits = defineEmits(['nextStep'])
const { formData, formDataRef, rules } = useFormData<ReleaseTypeEntity>(
  {
    type: undefined,
    warehouseAddress: undefined,
    category: undefined
  },
  {
    type: [{ required: true, message: '请选择发布类型', trigger: 'change' }],
    warehouseAddress: [
      { required: true, message: '请选择第三方仓库地址', trigger: 'change' }
    ],
    category: [{ required: true, message: '请选择商品类别', trigger: 'change' }]
  }
)

const publish_typeList = useDictItem('publish_type')

watch(
  () => formData.value.type,
  () => {
    goodsStore.setPublishType(formData.value.type as string)
  }
)

// 仅加入分销平台
const isOnlyDistribution = computed(() => {
  return formData.value.type === 'onlyDistribution'
})

const init = () => {
  if (goodsStore.step0FormData) {
    formData.value = goodsStore.step0FormData
  }
}
init()

const nextStep = () => {
  console.log('formData', formData.value)
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      const response = await saveReleaseType(formData.value)
      if (response.result) {
        goodsStore.setProductReleaseTypeId(response.result)
        goodsStore.setStep0FormData(formData.value)
        emits('nextStep')
      }
    }
  })
}
</script>

<template>
  <ContentWrap title="商品发布类型">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      require-asterisk-position="right"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="发布类型" prop="type">
            <DSelect
              v-model="formData.type"
              :options="publish_typeList"
              placeholder="请选择发布类型"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isOnlyDistribution">
          <el-form-item label="第三方仓库地址" prop="warehouseAddress">
            <el-select
              v-model="formData.warehouseAddress"
              clearable
              placeholder=""
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品类别" prop="category">
            <el-select v-model="formData.category" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :offset="isOnlyDistribution ? 0 : 12" :span="12">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
