<script setup lang="ts">
import { saveDistributionBasic } from '@/api/goods'
import type { DistributionBasicEntity } from '@/api/goods/types'
// import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'
import { useGoodsStore } from '@/store/modules/goods'
import DetailImgText from './DetailImgText.vue'

defineOptions({
  name: 'Step2'
})

const emits = defineEmits(['nextStep', 'prevStep'])

const goodsStore = useGoodsStore()

const { formData, formDataRef, rules } = useFormData<DistributionBasicEntity>(
  {
    productHostGraphUrl: undefined,
    productDimensionUrl: undefined,
    productCnTitle: undefined,
    productEnTitle: undefined,
    productCategoryId: undefined,
    productDetails: undefined,
    productReleaseTypeId: undefined,
    productBasicId: undefined
  },
  {
    productCnTitle: [
      { required: true, message: '请输入商品中文标题', trigger: 'blur' }
    ],
    productEnTitle: [
      { required: true, message: '请输入商品英文标题', trigger: 'blur' }
    ]
  }
)

// const publish_typeList = useDictItem('publish_type')
const fileList = ref<any[]>([])

const init = () => {
  if (goodsStore.step2FormData) {
    formData.value = goodsStore.step2FormData
  }
  if (goodsStore.getFileList) {
    fileList.value = goodsStore.getFileList
  }
}
init()

const prevStep = () => {
  emits('prevStep')
}

const nextStep = () => {
  console.log('formData', formData.value)
  console.log('fileList', fileList.value)

  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      formData.value.productReleaseTypeId = goodsStore.getProductReleaseTypeId
      formData.value.productBasicId = goodsStore.getProductBasicId
      formData.value.productHostGraphUrl = fileList.value
        ?.map(item => item.url)
        ?.join()
      const response = await saveDistributionBasic(formData.value)
      if (response.result) {
        goodsStore.setProductDistributionId(response.result)
        goodsStore.setStep2FormData(formData.value)
        goodsStore.setFileList(fileList.value)
        emits('nextStep')
      }
    }
  })
}
</script>

<template>
  <ContentWrap title="商品分销信息" class="mt-20">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      require-asterisk-position="right"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="商品图片" prop="">
            <div class="w-full">
              <!-- <GoodsImage v-model="fileList"></GoodsImage> -->
              <DUpload v-model="fileList" isShowTip></DUpload>
              <div class="text-12 color-blueGray">
                商品图片要求5-9张，宽高比例为1：1且大于800px，大小2M内
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品中文标题" prop="productCnTitle">
            <el-input
              v-model.trim="formData.productCnTitle"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请尽可能详细描述您的商品，可以大大增加曝光率和点击率并且可以显著降低售后率
            </div>
            <div class="text-12 color-blueGray">
              如：2025新款乌木沉香车载香薰固体香膏，适用于轿车，SUV等各类车型
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品英文标题" prop="productEnTitle">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请尽可能详细描述您的商品，可以大大增加曝光率和点击率并且可以显著降低售后率
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="text-16 fw-700 mb-18">商品属性</div>
        </el-col>

        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性1" prop="">
            <el-input
              v-model.trim="formData.productEnTitle"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap title="详情图文" class="mt-20">
    <DetailImgText></DetailImgText>
    <div class="w-full flex justify-end mt-16">
      <el-button @click="prevStep">上一步</el-button>
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
