<script setup lang="ts">
import { saveBasic } from '@/api/goods'
import type { BasicEntity } from '@/api/goods/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'
import { useGoodsStore } from '@/store/modules/goods'

defineOptions({
  name: 'Step1'
})

const emits = defineEmits(['nextStep', 'prevStep'])

const goodsStore = useGoodsStore()

const { formData, formDataRef, rules } = useFormData<BasicEntity>(
  {
    id: undefined,
    status: undefined,
    productCode: undefined,
    productName: undefined,
    productCnDeclare: undefined,
    productEnDeclare: undefined,
    productBrandName: undefined,
    productModel: undefined,
    productCnMaterial: undefined,
    productEnMaterial: undefined,
    productCnPurpose: undefined,
    productEnPurpose: undefined,
    productPrice: undefined,
    customsCode: undefined,
    goodsAttr: undefined,
    goodsType: undefined,
    isElectricity: undefined,
    productReviewUrl: undefined,
    productBarCode: undefined,
    productReleaseTypeId: undefined
  },
  {
    productName: [
      { required: true, message: '请输入商品名称', trigger: 'blur' }
    ],
    productCnDeclare: [
      { required: true, message: '请输入中文申报品名', trigger: 'blur' }
    ],
    productEnDeclare: [
      { required: true, message: '请输入英文申报品名', trigger: 'blur' }
    ],
    productBrandName: [
      { required: true, message: '请输入品牌名', trigger: 'blur' }
    ],
    productModel: [{ required: true, message: '请输入型号', trigger: 'blur' }],
    productCnMaterial: [
      { required: true, message: '请输入中文材质', trigger: 'blur' }
    ],
    productEnMaterial: [
      { required: true, message: '请输入英文材质', trigger: 'blur' }
    ],
    productCnPurpose: [
      { required: true, message: '请输入中文用途', trigger: 'blur' }
    ],
    productEnPurpose: [
      { required: true, message: '请输入英文用途', trigger: 'blur' }
    ],
    productPrice: [
      { required: true, message: '请输入申报单价', trigger: 'blur' }
    ],
    customsCode: [
      { required: true, message: '请输入海关编码', trigger: 'blur' }
    ],
    goodsAttr: [
      { required: true, message: '请选择货物属性', trigger: 'change' }
    ],
    goodsType: [
      { required: true, message: '请选择货物类型', trigger: 'change' }
    ],
    isElectricity: [
      { required: true, message: '请选择是否带磁/带电', trigger: 'change' }
    ],
    productReviewUrl: [
      { required: true, message: '请输入商品审核链接', trigger: 'blur' }
    ]
  }
)

const magnetic_or_electric = useDictItem('magnetic_or_electric')
const cargo_attribute = useDictItem('cargo_attribute')
const cargo_type = useDictItem('cargo_type')

const init = () => {
  if (goodsStore.step1FormData) {
    formData.value = goodsStore.step1FormData
  }
}
init()

const prevStep = () => {
  emits('prevStep')
}

const nextStep = () => {
  console.log('formData', formData.value)

  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      formData.value.productReleaseTypeId = goodsStore.getProductReleaseTypeId

      const response = await saveBasic(formData.value)
      if (response.result) {
        goodsStore.setProductBasicId(response.result)
        goodsStore.setStep1FormData(formData.value)
        emits('nextStep')
      }
    }
  })
}
</script>

<template>
  <ContentWrap title="商品基本信息" class="mt-20">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      require-asterisk-position="right"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="商品集编码（SPU）" prop="productCode">
            <el-input
              v-model.trim="formData.productCode"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              不填写则系统自动生成，SKU只能输入数字、字母、中横线，最大长度不能超过20字符
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品名称" prop="productName">
            <el-input
              v-model.trim="formData.productName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中文申报品名" prop="productCnDeclare">
            <el-input
              v-model.trim="formData.productCnDeclare"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              出口申报给海关的商品中文名称
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="英文申报品名" prop="productEnDeclare">
            <el-input
              v-model.trim="formData.productEnDeclare"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              出口申报给海关的商品英文名称
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="品牌名" prop="productBrandName">
            <el-input
              v-model.trim="formData.productBrandName"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              如无品牌请填写“无”，任一国家/地区有品牌均需如实填写
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="型号" prop="productModel">
            <el-input
              v-model.trim="formData.productModel"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">如无型号请填写“无”</div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="中文材质" prop="productCnMaterial">
            <el-input
              v-model.trim="formData.productCnMaterial"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">请输入商品材质的中文名</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="英文材质" prop="productEnMaterial">
            <el-input
              v-model.trim="formData.productEnMaterial"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">请输入商品材质的英文名</div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="中文用途" prop="productCnPurpose">
            <el-input
              v-model.trim="formData.productCnPurpose"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请输入商品的用途（中文） 例如：照明/装饰/工业生产/儿童玩具等
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="英文用途" prop="productEnPurpose">
            <el-input
              v-model.trim="formData.productEnPurpose"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">请输入商品的用途（英文）</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申报单价(USD)" prop="productPrice">
            <el-input
              v-model.trim="formData.productPrice"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请输入商品成本价，并换算成美元单位
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="海关编码" prop="customsCode">
            <el-input
              v-model.trim="formData.customsCode"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请提供采购链接、销售链接或图片链接，用于辅助审核人员确认产品品类及外观
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="货物属性" prop="goodsAttr">
            <DSelect
              v-model="formData.goodsAttr"
              :options="cargo_attribute"
              placeholder="请选择货物属性"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="货物类型" prop="goodsType">
            <DSelect
              v-model="formData.goodsType"
              :options="cargo_type"
              placeholder="请选择货物类型"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否带磁/带电" prop="isElectricity">
            <DSelect
              v-model="formData.isElectricity"
              :options="magnetic_or_electric"
              placeholder="请选择是否带磁/带电"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="商品审核链接" prop="productReviewUrl">
            <el-input
              v-model.trim="formData.productReviewUrl"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              请提供采购链接、销售链接或图片链接，用于辅助审核人员确认产品品类及外观
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="自定义条码" prop="productBarCode">
            <el-input
              v-model.trim="formData.productBarCode"
              clearable
              placeholder=""
            ></el-input>
            <div class="text-12 color-blueGray">
              使用第三方商品条码（如：亚马逊商品标签）作为海外仓商品标签，请务必填写
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button @click="prevStep">上一步</el-button>
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
