<script setup lang="ts">
defineOptions({
  name: 'DetailImgText'
})

const formData = ref({
  name: '',
  color1: 'rgba(38, 59, 95, 1)',
  color2: 'rgba(153, 73, 44, 1)',
  fontSize: 12,
  radio2: '1'
})
</script>

<template>
  <div class="bg-[#f9f9f9] w-full h-full p-10">
    <el-row :gutter="16" class="w-full">
      <el-col :span="16">
        <el-scrollbar height="500px">
          <div class="w-full h-full pos-relative pb-100 min-h-500">
            <div
              class="h-100 bg-white flex-center pos-relative border-b-1 border-b-solid border-b-[#e6e6e6]"
            >
              <div class="flex items-center">
                <div class="i-ep:picture-filled"></div>
                <span class="text-14 ml-8">请在右侧面板添加图片</span>
              </div>

              <div class="w-120 flex justify-between pos-absolute right-20">
                <div class="i-ep:top text-20 cursor-pointer"></div>
                <div class="i-ep:bottom text-20 cursor-pointer"></div>
                <div class="i-ep:circle-close text-20 cursor-pointer"></div>
              </div>
            </div>
            <div
              class="h-100 bg-white flex-center pos-relative border-b-1 border-b-solid border-b-[#e6e6e6]"
            >
              <div class="flex items-center">
                <div class="i-ep:document text-20"></div>
                <span class="text-14 ml-8">请在右侧面板添加文本</span>
              </div>

              <div class="w-120 flex justify-between pos-absolute right-20">
                <div class="i-ep:top text-20 cursor-pointer"></div>
                <div class="i-ep:bottom text-20 cursor-pointer"></div>
                <div class="i-ep:circle-close text-20 cursor-pointer"></div>
              </div>
            </div>

            <div
              class="w-full h-100 bg-white flex items-center justify-around border-1 border-solid border-[#e6e6e6] pos-absolute bottom-0 left-0 right-0"
            >
              <div
                class="h-80 px-10 border-1 border-solid border-[#e6e6e6] rounded-6 flex-center flex-col cursor-pointer"
              >
                <div class="i-ep:picture-filled text-20"></div>
                <div class="mt-8">插入图片组件</div>
              </div>
              <div
                class="h-80 px-10 border-1 border-solid border-[#e6e6e6] rounded-6 flex-center flex-col cursor-pointer"
              >
                <div class="i-ep:document text-20"></div>
                <div class="mt-8">插入文本组件</div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-col>
      <el-col :span="8">
        <div class="bg-white p-10 h-full">
          <div class="mb-16 fw-600">文本</div>
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
            type="textarea"
            :rows="5"
            :maxlength="150"
            resize="none"
            show-word-limit
          ></el-input>
          <el-row :gutter="16" class="mt-16">
            <el-col :span="12">
              <div class="mb-16 fw-600">颜色</div>
              <el-color-picker
                v-model="formData.color1"
                class="custom-color-picker"
                show-alpha
              />
            </el-col>
            <el-col :span="12">
              <div class="mb-16 fw-600">背景颜色</div>
              <el-color-picker
                v-model="formData.color2"
                class="custom-color-picker"
                show-alpha
              />
            </el-col>
          </el-row>
          <div class="my-16 fw-600">字体大小</div>
          <el-input-number v-model="formData.fontSize" :min="12" :max="100" />

          <div class="my-16 fw-600">对齐方式</div>
          <el-radio-group v-model="formData.radio2">
            <el-radio-button label="左对齐" value="1" />
            <el-radio-button label="居中对齐" value="2" />
            <el-radio-button label="右对齐" value="3" />
            <el-radio-button label="两侧对齐" value="4" />
          </el-radio-group>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped></style>
