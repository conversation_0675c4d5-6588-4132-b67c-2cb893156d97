<script setup lang="ts">
import { useGoodsStore } from '@/store/modules/goods'
import Step0 from './Step0.vue'
import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
defineOptions({
  name: 'GoodsEntity'
})
const goodsStore = useGoodsStore()
const active = ref(2)

const prevStep = () => {
  if (goodsStore.isOnlyOverseasWarehouse && active.value === 3) {
    active.value -= 2
  } else {
    active.value--
  }
}

const nextStep = () => {
  if (goodsStore.isOnlyOverseasWarehouse && active.value === 1) {
    active.value += 2
  } else {
    active.value++
  }
}
</script>

<template>
  <div class="w-full mb-16">
    <el-select v-model="active" clearable placeholder="" class="!w-200">
      <el-option :value="0" label="step0"></el-option>
      <el-option :value="1" label="step1"></el-option>
      <el-option :value="2" label="step2"></el-option>
      <el-option :value="3" label="step3"></el-option>
    </el-select>
    <el-steps
      v-if="active !== 0"
      style="max-width: 700px; margin: 0 auto"
      :active="active"
      align-center
    >
      <el-step title="商品基本信息" />
      <el-step
        v-if="!goodsStore.isOnlyOverseasWarehouse"
        title="商品分销信息"
      />
      <el-step title="商品规格信息" />
    </el-steps>
  </div>

  <Step0 v-if="active === 0" @nextStep="nextStep"></Step0>
  <Step1 v-if="active === 1" @prevStep="prevStep" @nextStep="nextStep"></Step1>
  <Step2 v-if="active === 2" @prevStep="prevStep" @nextStep="nextStep"></Step2>
  <Step3 v-if="active === 3" @prevStep="prevStep"></Step3>
</template>

<style lang="scss" scoped></style>
