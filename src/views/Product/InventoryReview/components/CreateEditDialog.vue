<script setup lang="ts">
defineOptions({
  name: 'CreateEditDialog'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog v-model="visible" title="服务商信息" width="600" @open="handleOpen" @close="handleClose">
    <el-scrollbar max-height="400" class="w-full">
      <el-form :model="formData" :rules="rules" ref="formDataRef" label-position="top" @submit.prevent class="w-full">
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="服务商" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商简称" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商代码" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国家" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省/州" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址一" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址二" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址三" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商类型" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
