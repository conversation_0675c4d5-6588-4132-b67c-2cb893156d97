<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import Attribute from './components/Attribute.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import CreateEditDialog from './components/CreateEditDialog.vue'
import type { CategoryEntity } from '@/api/category/types'
import { deleteCategory, getCategoryList } from '@/api/category'

defineOptions({
  name: 'Category'
})
const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})

const handleDelete = () => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择品类')
    return
  }
  tableMethods.hadnleDel(selection.value)
}

const handleStatusChange = async (type: '0' | '1') => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择品类')
    return
  }
  // const ids = selection.value.map(item => item.id?.toString())
  if (type === '0') {
    const result = selection.value.filter(
      item => item.status?.toString() === '0'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的品类中存在停用的品类')
      return
    }
    // await disableService(ids as string[])
  } else if (type === '1') {
    const result = selection.value.filter(
      item => item.status?.toString() === '1'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的品类中存在启用的品类')
      return
    }
    // await enableService(ids as string[])
  }
  // clearSelection
  tableMethods.handleQuery()
}

// const rvisible = ref(false)
// const rRecord = ref()

// const handleReview = (row: CategoryEntity) => {
//   rvisible.value = true
//   rRecord.value = row
// }

// const awvisible = ref(false)
// const handleAW = () => {
//   awvisible.value = true
// }

const bvisible = ref(false)
const handleBatchCreate = () => {
  bvisible.value = true
}

const avisible = ref(false)
const handleAttribute = () => {
  avisible.value = true
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="产品分类" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="级别" prop="">
            <el-select v-model="formData.level" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增品类</el-button
      >
      <el-button type="primary" @click="handleBatchCreate">批量创建</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
      <el-button type="primary">导出</el-button>
      <el-button type="primary" @click="handleStatusChange('1')"
        >启用</el-button
      >
      <el-button type="warning" @click="handleStatusChange('0')"
        >停用</el-button
      >
      <el-button type="primary" @click="handleAttribute">属性配置</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="ID" prop="id" width="180" />
      <el-table-column label="Pid" prop="parentId" width="180" />
      <el-table-column label="分类名称" prop="name" width="180" />
      <el-table-column label="级别" prop="level" width="180" />
      <el-table-column label="属性" prop="attributes" width="180" />
      <el-table-column label="说明" prop="description" width="180" />
      <el-table-column label="状态" prop="status" width="180" />
      <el-table-column label="操作" fixed="right" width="360">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            type="primary"
            @click="
              () =>
                tableMethods.handleAdd(() => {
                  viewEntity.record = row
                  viewEntity.subType = 1
                })
            "
            >新增子类</el-button
          >
          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
          <el-button
            v-if="row.status === '1'"
            type="warning"
            @click="handleStatusChange('1')"
            >停用</el-button
          >
          <el-button
            v-if="row.status === '0'"
            type="primary"
            @click="handleStatusChange('1')"
            >启用</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.handleQuery()"
  ></CreateEditDialog>
  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
  <Attribute v-model="avisible"></Attribute>
</template>

<style lang="scss" scoped></style>
