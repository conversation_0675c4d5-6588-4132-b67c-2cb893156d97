<script setup lang="ts">
defineOptions({
  name: 'Attribute'
})

const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="属性配置"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="分类属性" prop="">
        <el-input v-model.trim="formData.name" clearable placeholder="">
          <template #append>
            <el-button icon="i-ri:search-line" />
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="属性名称" prop="date" width="180" />
      <el-table-column label="输入类型" prop="date" width="180" />
      <el-table-column label="属性值" prop="date" width="180" />
      <el-table-column label="限制值" prop="date" width="180" />
    </el-table>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
