<script setup lang="ts">
import ProductMapping from './components/ProductMapping.vue'
import AddDistribution from './components/AddDistribution.vue'
import PrintBarcode from './components/PrintBarcode.vue'
import WithdrawDistribution from './components/WithdrawDistribution.vue'
import { useTable } from '@/hooks/useTable'
import type { SpecificationsBasicEntity } from '@/api/goods/types'
import { getProductList } from '@/api/goods'
import { handleTableDataSpan } from '@/utils'
import { handleProductAttr } from './hepler'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'List'
})

// 合并行span标记数组
const rowSpanArr = ref<number[]>([])

const specification_attribute = useDictItem('specification_attribute')

const {
  // viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
  // selection
} = useTable<SpecificationsBasicEntity>({
  immediate: true,
  initialFormData: {
    status: ''
    // name: undefined,
    // level: undefined
  },
  fetchDataApi: async () => {
    const res = await getProductList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    rowSpanArr.value = handleTableDataSpan(res.result.records, 'productCode')

    return {
      list: res.result.records,
      total: res.result.total
    }
  }
  // fetchDelApi: async (record: SpecificationsBasicEntity) => {
  //   // if (Array.isArray(record)) {
  //   //   const ids = record.map((item: any) => item.id?.toString())
  //   //   const res = await deleteCategory(ids)
  //   //   return !!res
  //   // } else {
  //   //   const res = await deleteCategory([record.id])
  //   //   return !!res
  //   // }
  //   // const res = await deleteCategory({ id: record.id })
  //   // return !!res
  //   return false
  // }
})

/**
 * @description: 合并行方法
 * @param {*} data
 * @return {*}
 */
const tableSpanMethod = (data: any) => {
  const { rowIndex, columnIndex } = data
  if (columnIndex === 1) {
    const rowSpan = rowSpanArr.value[rowIndex]
    return [rowSpan, 1]
  }
}

const visible = ref(false)
const handleOpen = () => {
  visible.value = true
}

const bvisible = ref(false)
const handleResetPwd = () => {
  bvisible.value = true
}
const advisible = ref(false)
const handleAD = () => {
  advisible.value = true
}
const pbvisible = ref(false)
const handlePB = () => {
  pbvisible.value = true
}
const wdvisible = ref(false)
const handleWD = () => {
  wdvisible.value = true
}

const options = [
  {
    label: '全部',
    value: '全部'
  },
  {
    label: '待处理',
    value: '待处理'
    // disabled: true
  },
  {
    label: '已同意',
    value: '已同意'
  },
  {
    label: '已拒绝',
    value: '已拒绝'
  },
  {
    label: '已删除',
    value: '已删除'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.status"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="商品编码" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="起始时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="date"
              placeholder=""
              class="!w-full"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="截止时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="date"
              placeholder=""
              class="!w-full"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button>重置</el-button>
              <el-button>刷新</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="handleAD">加入分销</el-button>
      <el-button type="primary" @click="handleResetPwd">打印条码</el-button>
      <el-button type="primary" @click="handleWD">撤回分销</el-button>
      <el-button type="danger">删除</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      :span-method="tableSpanMethod"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="商品信息" prop="date" width="180">
        <template #default="{ row }">
          <div>
            <span>商品集编码：</span>
            <span>{{ row.productCode }}</span>
          </div>
          <div>
            <span>商品名称：</span>
            <span>{{ row.productName }}</span>
          </div>
          <div>
            <span>归属卖家：</span>
            <span>{{ row.sellerName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品编码" prop="productSkuCode" width="180" />
      <el-table-column label="商品图片" prop="productSkuUrl" width="180">
        <template #default="{ row }">
          <DImage :url="row.productSkuUrl"></DImage>
        </template>
      </el-table-column>
      <el-table-column label="商品属性" width="140">
        <template #default="{ row }">
          <template v-if="row.productAttr">
            <div
              v-for="(item, index) of handleProductAttr(
                row,
                specification_attribute
              )"
              :key="index"
            >
              <span>{{ item.attr }}:</span>
              <span class="ml-5">{{ item.value }}</span>
            </div>
          </template>
          <!-- <div>{{ row.productAttr }}</div>
          <div>{{ row.productAttrOne }}</div>
          <div>{{ row.productAttrTwo }}</div> -->
        </template>
      </el-table-column>
      <el-table-column label="用户尺寸/重量" width="180">
        <template #default="{ row }">
          <div>
            {{
              `${row.productLength} x ${row.productLength} x ${row.productLength} CM`
            }}
          </div>
          <div>{{ row.productWeight }} KG</div>
        </template>
      </el-table-column>
      <el-table-column label="仓储尺寸" prop="productSize" width="180" />
      <el-table-column label="仓库重量" prop="productWeight" width="180" />
      <el-table-column label="库存数量" prop="productNumber" width="180" />
      <el-table-column label="创建时间/更新时间" prop="date" width="180">
        <template #default="{ row }">
          <div>{{ row.createTime }}</div>
          <div>{{ row.updateTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="statusName" width="180" />
      <el-table-column label="操作" fixed="right" width="400">
        <template #default="{}">
          <el-button type="primary" @click="handlePB">打印条码</el-button>
          <el-button type="primary" @click="handleOpen">商品映射</el-button>
          <el-button type="primary">撤回分销</el-button>
          <el-button type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <ProductMapping v-model="visible"></ProductMapping>

  <AddDistribution v-model="advisible"></AddDistribution>
  <PrintBarcode v-model="pbvisible"></PrintBarcode>
  <WithdrawDistribution v-model="wdvisible"></WithdrawDistribution>
</template>

<style lang="scss" scoped></style>
