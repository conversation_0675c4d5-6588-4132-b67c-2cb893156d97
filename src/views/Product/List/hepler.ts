// const options1: any[] = [
//   {
//     id: '0a5994f8fdf2e35e40fb899f0a758ec6',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '材质',
//     itemValue: 'texture',
//     description: null,
//     sortOrder: 3,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:12:52'
//   },
//   {
//     id: '0f90f95b5dfd9adcc7d7b9175ba32fb7',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '重量',
//     itemValue: 'weight',
//     description: null,
//     sortOrder: 2,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:12:35'
//   },
//   {
//     id: '4e795739b507bb12babf38e144406991',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '颜色',
//     itemValue: 'colour',
//     description: null,
//     sortOrder: 0,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:11:42'
//   },
//   {
//     id: '756d30bc8a8d5e5d59d7e1dd31b0377d',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '商品数量',
//     itemValue: 'productQuantity',
//     description: null,
//     sortOrder: 6,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:14:34'
//   },
//   {
//     id: '8f6af6b10eae53af5232b9d9b2f10eb6',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '尺寸',
//     itemValue: 'size',
//     description: null,
//     sortOrder: 1,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:12:02'
//   },
//   {
//     id: 'd41e0099c14c748f251ba2a662547131',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '容量',
//     itemValue: 'volume',
//     description: null,
//     sortOrder: 7,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:15:07'
//   },
//   {
//     id: 'd50cfda10cea2060d9dce7f02b50f0a9',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '型号',
//     itemValue: 'version',
//     description: null,
//     sortOrder: 5,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:13:55'
//   },
//   {
//     id: 'e407b4ade178104715f789d0afc38934',
//     dictId: '2f269e4b56cd26691008fc1a91b9ff32',
//     itemText: '风格',
//     itemValue: 'style',
//     description: null,
//     sortOrder: 4,
//     createUser: 'admin',
//     createTime: '2025-04-20T15:13:15'
//   }
// ]

/**
 * @description: 组装属性和对应的值
 * @param {any} row
 * @param {any} attrOptions
 * @return {*}
 */
export function handleProductAttr(row: any, attrOptions: any[]) {
  const attrList = row.productAttr.split(',')

  return attrList.map((item: any, index: number) => {
    const option = attrOptions.find((option: any) => option.itemValue === item)
    let val = undefined
    if (index === 0) {
      val = row.productAttrOne
    }
    if (index === 1) {
      val = row.productAttrTwo
    }
    return {
      attr: option ? option.itemText : '',
      value: val
    }
  })
}
