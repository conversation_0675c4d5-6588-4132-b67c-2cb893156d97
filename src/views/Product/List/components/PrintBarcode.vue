<script setup lang="ts">
defineOptions({
  name: 'PrintBarcode'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog v-model="visible" title="打印条码" width="600" @open="handleOpen" @close="handleClose">
    <el-scrollbar max-height="400" class="w-full">
      <el-form :model="formData" :rules="rules" ref="formDataRef" label-position="top" @submit.prevent class="w-full">
        <el-row :gutter="16" class="w-full">
          <el-col :span="24">
            <el-form-item label="打印模板" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="打印数量" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择打印机" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="打印预览" prop=""> </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
