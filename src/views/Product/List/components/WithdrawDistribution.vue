<script setup lang="ts">
defineOptions({
  name: 'WithdrawDistribution'
})
const visible = defineModel({ default: false })

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="撤回分销"
    width="600"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex items-center justify-center flex-col">
      <div
        class="bg-#fdeeef w-80 h-80 rounded-50% flex items-center justify-center"
      >
        <div class="i-ep:warn-triangle-filled text-40 color-#e82c35"></div>
      </div>
      <div class="text-20 mt-20">确定撤回分销?</div>
      <div class="mt-20">商品撤回后将在分销商城中下架</div>
      <div>此操作不可逆，请谨慎操作</div>
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确定撤回 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
