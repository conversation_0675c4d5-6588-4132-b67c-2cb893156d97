<script setup lang="ts">
defineOptions({
  name: 'AddDistribution'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])
</script>

<template>
  <el-dialog
    v-model="visible"
    title="加入分销"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column label="商品编码" prop="date" width="180" />
      <el-table-column label="商品名称" prop="date" width="180" />
      <el-table-column label="库存数量" prop="date" width="180" />
      <el-table-column label="分销数量" prop="date" width="180">
        <template #default="{}">
          <el-input v-model="formData.num" />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
