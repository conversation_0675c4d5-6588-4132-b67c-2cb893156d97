<script setup lang="ts">
defineOptions({
  name: 'CreateEditDialog'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])
</script>

<template>
  <el-dialog
    v-model="visible"
    title="商品映射"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="!pr-16">
      <div>
        <span>商品名称:</span>
        <span class="ml-16">时尚真皮手提包(KAT-GLT1)</span>
      </div>
      <el-divider />
      <div class="flex justify-end mb-18">
        <el-button type="primary">添加映射</el-button>
      </div>
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-width="100"
        label-position="left"
        @submit.prevent
        class="w-full"
      >
        <div class="border-#e5e7eb border-solid border-1 rounded-8 p-20">
          <div class="flex justify-between items-center mb-18">
            <div>映射 #1</div>
            <div class="i-ep:delete-filled text-16"></div>
          </div>
          <el-form-item label="选择平台" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="店铺ID" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="平台商品编码" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="映射数量" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </div>

        <div class="my-18 text-16">映射结果列表</div>

        <el-table :data="tableData" :border="true">
          <el-table-column label="平台" prop="date" width="180" />
          <el-table-column label="店铺ID" prop="date" width="180" />
          <el-table-column label="平台商品编码" prop="date" width="180" />
          <el-table-column label="映射数量" prop="date" width="180" />
          <el-table-column label="创建时间" prop="date" width="180" />
          <el-table-column label="状态" prop="date" width="180" />
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="{}">
              <el-button type="primary" text>编辑</el-button>
              <el-button type="danger" text>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
