<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import { useTable } from '@/hooks/useTable'
import type { AttributeEntity } from '@/api/attribute/types'
import { deleteAttribute, getAttributeList } from '@/api/attribute'

defineOptions({
  name: 'Attribute'
})

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<AttributeEntity>({
  immediate: true,
  initialFormData: {
    name: undefined
  },
  fetchDataApi: async () => {
    const res = await getAttributeList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: AttributeEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteAttribute({ id: record.id })
    return !!res
  }
})

const handleDelete = () => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择属性')
    return
  }
  tableMethods.hadnleDel(selection.value)
}

const handleStatusChange = async (type: '0' | '1') => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择属性')
    return
  }
  // const ids = selection.value.map(item => item.id?.toString())
  if (type === '0') {
    const result = selection.value.filter(
      item => item.status?.toString() === '0'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的属性中存在停用的属性')
      return
    }
    // await disableService(ids as string[])
  } else if (type === '1') {
    const result = selection.value.filter(
      item => item.status?.toString() === '1'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的属性中存在启用的属性')
      return
    }
    // await enableService(ids as string[])
  }
  // clearSelection
  tableMethods.handleQuery()
}

// const rvisible = ref(false)
// const rRecord = ref()

// const handleReview = (row: CategoryEntity) => {
//   rvisible.value = true
//   rRecord.value = row
// }

// const awvisible = ref(false)
// const handleAW = () => {
//   awvisible.value = true
// }

const bvisible = ref(false)
const handleBatchCreate = () => {
  bvisible.value = true
}

// const avisible = ref(false)
// const handleAttribute = () => {
//   avisible.value = true
// }
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="属性名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增属性</el-button
      >
      <el-button type="primary" @click="handleBatchCreate">批量创建</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
      <el-button type="primary">导出</el-button>
      <el-button type="primary" @click="handleStatusChange('1')"
        >启用</el-button
      >
      <el-button type="warning" @click="handleStatusChange('0')"
        >停用</el-button
      >
    </div>
    <el-table
      :data="dataList"
      v-loading="loading"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="ID" prop="id" width="180" />
      <el-table-column label="属性名" prop="name" width="180" />
      <el-table-column label="输入类型" prop="inputType" width="180" />
      <el-table-column label="属性值" prop="value" />
      <el-table-column label="是否可以新增属性" prop="isAddValue" width="180" />
      <el-table-column label="描述" prop="description" width="180" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
          <el-button
            v-if="row.status === '1'"
            type="warning"
            @click="handleStatusChange('1')"
            >停用</el-button
          >
          <el-button
            v-if="row.status === '0'"
            type="primary"
            @click="handleStatusChange('1')"
            >启用</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :viewEntity="viewEntity"
    @refresh="() => tableMethods.handleQuery()"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
