<script setup lang="ts">
import RuleConfig from './components/RuleConfig.vue'

defineOptions({
  name: 'BusinessSetting'
})

const formData = ref({
  name: ''
})
const rules = ref({})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const visible = ref(false)
const handleOpen = () => {
  visible.value = true
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="客户信息" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="产品类型" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账户类型" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="客户信息" prop="date" width="180" />
      <el-table-column label="产品类型" prop="date" width="180" />
      <el-table-column label="账户类型" prop="date" width="180" />
      <el-table-column label="账单日" prop="date" width="180" />
      <el-table-column label="坏款期限" prop="date" width="180" />
      <el-table-column label="自动还款" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="190">
        <template #default="{}">
          <el-button type="primary" @click="handleOpen">编辑</el-button>
          <el-button type="primary">操作日志</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <RuleConfig v-model="visible"></RuleConfig>
</template>

<style lang="scss" scoped></style>
