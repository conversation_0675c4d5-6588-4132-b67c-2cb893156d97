<script setup lang="ts">
defineOptions({
  name: 'RuleConfigForm'
})
const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})
</script>

<template>
  <el-scrollbar class="w-full">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
      class="w-full"
    >
      <el-row :gutter="16" class="w-full">
        <el-col :span="12">
          <el-form-item label="客户付款模式" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
            <div class="text-bluegray text-12">
              表示该客户付款再进行对账确认和采购申请
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品托管账户" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
            <div class="text-bluegray text-12">
              信用账与专用账户管理还是普通账户管理
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算方式" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
            <div class="text-bluegray text-12">该账户的结算方式</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账单生成日" prop="">
            <div>
              <el-input-number v-model="formData.num" :min="1" :max="10" />
              <div class="text-bluegray text-12">每月第几天</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="还款期限" prop="">
            <div>
              <el-input-number v-model="formData.num" :min="1" :max="10" />
              <div class="text-bluegray text-12">账单生成后第几天</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="自动还款" prop="">
            <div>
              <el-radio-group v-model="formData.name">
                <el-radio :label="'开启'" :value="1"></el-radio>
                <el-radio :label="'关闭'" :value="2"></el-radio>
              </el-radio-group>
              <div class="text-bluegray text-12">
                信用账单结束时，能否自动从预付款账户扣款
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-scrollbar>
</template>

<style lang="scss" scoped></style>
