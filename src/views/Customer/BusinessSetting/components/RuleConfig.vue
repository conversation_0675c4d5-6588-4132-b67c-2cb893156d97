<script setup lang="ts">
import RuleConfigForm from './RuleConfigForm.vue'

defineOptions({
  name: 'RuleConfig'
})
const visible = defineModel({ default: false })

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="客户交易规则配置"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="mb-16">
      当前选择客户<span class="text-blue">62761</span>共选择客户1个
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="头程">
        <el-scrollbar max-height="400">
          <RuleConfigForm></RuleConfigForm>
        </el-scrollbar>
      </el-tab-pane>
      <el-tab-pane label="海外仓">
        <el-scrollbar max-height="400">
          <RuleConfigForm></RuleConfigForm>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
