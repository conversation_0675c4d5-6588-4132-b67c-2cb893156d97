<script setup lang="ts">
import { saveAccount, updateAccount } from '@/api/account'

defineOptions({
  name: 'CreateDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    seller: undefined,
    businessType: undefined,
    accountType: undefined,
    currency: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const titleMap: Record<DlViewType, string> = {
  ADD: '新增账户号码',
  EDIT: '编辑账户号码',
  DETAIL: '账户号码详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveAccount,
  EDIT: updateAccount,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="500"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="所属客户" prop="">
        <el-input
          v-model.trim="formData.seller"
          clearable
          placeholder=""
        ></el-input>
      </el-form-item>
      <el-form-item label="业务类型" prop="">
        <el-select v-model="formData.businessType" clearable placeholder="">
          <el-option value="leg" label="头程"></el-option>
          <el-option value="overseasPosition" label="海外仓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户类型" prop="">
        <el-select v-model="formData.accountType" clearable placeholder="">
          <el-option :value="1" label="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户币种" prop="">
        <el-select v-model="formData.currency" clearable placeholder="">
          <el-option :value="1" label="1"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
