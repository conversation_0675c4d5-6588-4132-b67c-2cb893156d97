<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateDialog from './components/CreateDialog.vue'
import { getAccountList, updateAccount } from '@/api/account'
import type { AccountManagementEntity } from '@/api/account/types'

defineOptions({
  name: 'Account'
})

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
  // selection
} = useTable<AccountManagementEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    accountType: undefined,
    currency: undefined,
    seller: undefined,
    minAmount: undefined,
    maxAmount: undefined
  },
  fetchDataApi: async () => {
    const res = await getAccountList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

/**
 * @description: 更新状态
 * @return {*}
 */
const handleUpdataStatus = async (
  type: string,
  row: AccountManagementEntity
) => {
  await updateAccount({
    id: row.id,
    status: type
  })
  tableMethods.handleQuery()
}

// const rvisible = ref(false)
// const rRecord = ref()

// const handleReview = (row: AccountManagementEntity) => {
//   rvisible.value = true
//   rRecord.value = row
// }

// const awvisible = ref(false)
// const handleAW = () => {
//   awvisible.value = true
// }
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="账号类型" prop="">
            <el-select v-model="formData.accountType" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.currency" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="适用产品" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <el-select v-model="formData.status" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户信息" prop="">
            <el-select v-model="formData.seller" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="金额范围" prop="">
            <div class="flex w-full">
              <el-input
                v-model.trim="formData.minAmount"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
              <div class="mx-8">-</div>
              <el-input
                v-model.trim="formData.maxAmount"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增账户号码</el-button
      >
      <el-button>导出</el-button>
    </div>
    <el-table
      :data="dataList"
      v-loading="loading"
      :border="true"
      class="w-full"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="客户信息" prop="seller" width="180" />
      <el-table-column label="账户" prop="date" width="180" />
      <el-table-column
        label="预扣款金额"
        prop="preDeductionAmount"
        width="180"
      />
      <el-table-column label="冻结金额" prop="date" width="180" />
      <el-table-column label="可用金额" prop="availableAmount" width="180" />
      <el-table-column label="账户状态" prop="status" width="180" />
      <el-table-column label="最后一次充值时间" prop="date" width="180" />
      <el-table-column label="最后一次充值天数" prop="date" width="180" />
      <el-table-column label="当前额度" prop="availableAmount" width="180" />
      <el-table-column label="额度有效期" prop="date" width="180" />
      <el-table-column label="最后起期" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="100">
        <template #default="{ row }">
          <el-button
            v-if="row.status === '1'"
            type="danger"
            @click="handleUpdataStatus('0', row)"
            >禁用</el-button
          >
          <el-button
            v-if="row.status === '0'"
            type="primary"
            @click="handleUpdataStatus('1', row)"
            >启用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateDialog>
</template>

<style lang="scss" scoped></style>
