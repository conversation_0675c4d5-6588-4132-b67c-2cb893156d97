<script setup lang="ts">
import {
  findReceivingAccountList,
  getSellerBankAccountList
} from '@/api/common'
import { recharge } from '@/api/rechargeReview'
import type { RechargeReviewEntity } from '@/api/rechargeReview/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const payment_method = inject('payment_method') as Ref<DictItemEntity[]>
const sellerOptions = inject('sellerOptions') as Ref<any[]>

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<RechargeReviewEntity>(
    {
      id: undefined,
      sellerId: undefined,
      rechargeType: undefined,
      rechargeAccount: undefined,
      payAccount: undefined,
      rechargeAmount: undefined,
      expectedArrivalAmount: undefined,
      actualExchangeRate: undefined,
      actualArrivalAmount: undefined,
      estimateExchangeRate: undefined,
      examineStatus: undefined,
      rechargeVoucher: undefined
    },
    {
      sellerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
      collectionAccount: [
        { required: true, message: '请选择收款账户', trigger: 'change' }
      ],
      rechargeType: [
        { required: true, message: '请选择付款方式', trigger: 'change' }
      ],
      rechargeAccount: [
        { required: true, message: '请选择充值账户', trigger: 'change' }
      ],
      rechargeAmount: [
        { required: true, message: '请输入充值金额', trigger: 'blur' }
      ],
      rechargeVoucher: [
        { required: true, message: '请上传充值凭证', trigger: 'change' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '充值',
  EDIT: '编辑收款账户',
  DETAIL: '收款账户详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const fileList = ref<any[]>([])

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: recharge,
  EDIT: recharge,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)

    if (formData.value.rechargeVoucher) {
      fileList.value = [
        {
          url: formData.value.rechargeVoucher,
          key: formData.value.rechargeVoucher
        }
      ]
    }
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
  accountInfo.value = {}
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  console.log(fileList.value)
  if (fileList.value.length > 0) {
    formData.value.rechargeVoucher = fileList.value[0]?.url
  }
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
// 根据付款方式，获取收款账户列表
const collectionAccountOptions = ref<any[]>([])
const handleRechargeTypeChange = async (val: string) => {
  formData.value.collectionAccount = undefined
  accountInfo.value = {}
  const res = await findReceivingAccountList(val)
  collectionAccountOptions.value = res.result
}

const accountInfo = ref<any>({})
// 根据收款账户获取账户详情信息
const handleCollectionAccountChange = (val: string) => {
  accountInfo.value = {}
  accountInfo.value = collectionAccountOptions.value.find(
    item => item.id === val
  )
  console.log(accountInfo.value)
}

// 根据卖家获取银行账户列表
const rechargeAccountOptions = ref<any[]>([])
const handleSellerChange = async (val: string) => {
  formData.value.rechargeAccount = undefined
  const res = await getSellerBankAccountList(val)
  rechargeAccountOptions.value = res.result
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="left"
        label-width="110"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="客户" prop="sellerId">
              <DSelect
                v-model="formData.sellerId"
                :options="sellerOptions"
                placeholder="请选择"
                :fields="{
                  label: 'companyName',
                  value: 'id'
                }"
                @change="handleSellerChange"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账户" prop="collectionAccount">
              <DSelect
                v-model="formData.collectionAccount"
                :options="collectionAccountOptions"
                placeholder="请选择"
                :fields="{
                  label: 'accountName',
                  value: 'id'
                }"
                @change="handleCollectionAccountChange"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款方式" prop="rechargeType">
              <DSelect
                v-model="formData.rechargeType"
                :options="payment_method"
                placeholder="请选择"
                @change="handleRechargeTypeChange"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款币种" prop="currency">
              <span>{{ accountInfo.currencyName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值账户" prop="rechargeAccount">
              <el-select
                v-model="formData.rechargeAccount"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item of rechargeAccountOptions"
                  :key="item.id"
                  :value="item.id"
                  :label="`${item.businessTypeName}${item.currencyName ? ` ${item.currencyName}` : ''}`"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="户名" prop="currency">
              <span>{{ accountInfo.accountHolder }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款账号" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.payAccount"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号" prop="currency">
              <span>{{ accountInfo.accountNumber }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值金额" prop="rechargeAmount">
              <el-input
                v-model.trim="formData.rechargeAmount"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="currency">
              <span>{{ accountInfo.bankName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计到账金额" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.expectedArrivalAmount"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联行号" prop="currency">
              <span>{{ accountInfo.jointBank }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="bankName">
              <el-input
                v-model.trim="formData.remark"
                clearable
                placeholder=""
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款码" prop="bankNumber">
              <DImage
                v-if="accountInfo.paymentCode"
                :url="accountInfo.paymentCode"
                width="60px"
                height="60px"
              ></DImage>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值凭证" prop="rechargeVoucher">
              <DUpload v-model="fileList" :limit="1"></DUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
