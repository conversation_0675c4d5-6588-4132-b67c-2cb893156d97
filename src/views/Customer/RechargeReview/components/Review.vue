<script setup lang="ts">
import { examine, findRechargeRecordById } from '@/api/rechargeReview'
import type { RechargeReviewEntity } from '@/api/rechargeReview/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'Review'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<RechargeReviewEntity>(
    {
      id: undefined,
      examineRemark: undefined,
      actualExchangeRate: undefined,
      actualArrivalAmount: undefined
    },
    {}
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '充值审核',
  EDIT: '充值审核',
  DETAIL: '充值详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: examine,
  EDIT: examine,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')
const detailData = ref<any>({})
const handleOpen = async () => {
  const res = await findRechargeRecordById({ id: viewEntity.record.id })
  detailData.value = res.result
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
// 审核状态(0待审核，1通过，2不通过)
const handleConfirm = (status: number) => {
  formData.value.examineStatus = status
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      formData.value.id = detailData.value.id
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}

// 处理拒绝操作，校验审核原因后显示二次确认弹窗
const handleReject = async () => {
  // 先校验审核备注是否已填写
  if (!formData.value.examineRemark?.trim()) {
    ElMessage.warning('请填写审核备注')
    return
  }

  try {
    await ElMessageBox.confirm('确定要拒绝这笔充值申请吗？', '确认拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    handleConfirm(2)
  } catch {
    // 用户取消操作，不做任何处理
  }
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="left"
        label-width="110"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="付款方式">
              <span>{{ detailData.rechargeType }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账户">
              <span>{{ detailData.collectionAccount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款账号">
              <span>{{ detailData.payAccount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款币种">
              <span>{{ detailData.currencyName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值金额">
              <span>{{ detailData.rechargeAmount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值账户">
              <span>{{ detailData.rechargeAccount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计到账金额">
              <span>{{ detailData.expectedArrivalAmount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际汇率">
              <el-input
                v-if="viewEntity.type === 'ADD'"
                v-model.trim="formData.actualExchangeRate"
                clearable
                placeholder=""
              ></el-input>
              <span v-else>{{ detailData.actualExchangeRate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估汇率">
              <span>{{ detailData.estimateExchangeRate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际到账金额">
              <el-input
                v-if="viewEntity.type === 'ADD'"
                v-model.trim="formData.actualArrivalAmount"
                clearable
                placeholder=""
              ></el-input>
              <span v-else>{{ detailData.actualArrivalAmount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <span>{{ detailData.remark }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核备注">
              <el-input
                v-if="viewEntity.type === 'ADD'"
                v-model.trim="formData.examineRemark"
                clearable
                placeholder=""
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
              ></el-input>
              <span v-else>{{ detailData.examineRemark }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值凭证">
              <DImage
                v-if="detailData.rechargeVoucher"
                :url="detailData.rechargeVoucher"
                width="60px"
                height="60px"
              ></DImage>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div v-if="viewEntity.type === 'ADD'" class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReject">拒绝</el-button>
        <el-button type="primary" @click="handleConfirm(1)"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
