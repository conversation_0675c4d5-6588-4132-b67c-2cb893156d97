<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import Review from './components/Review.vue'
import type { BankEntity } from '@/api/bankAccount/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useToggle } from '@/hooks/useToggle'
import { financeAccountRecharge } from '@/api/rechargeReview'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'Currency'
})

const user_status = useDictItem('user_status')
const payment_method = useDictItem('payment_method')
const { sellerOptions } = useOptions(['sellerOptions'])

provide('payment_method', payment_method)
provide('sellerOptions', sellerOptions)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<BankEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    keywords: undefined
  },
  fetchDataApi: async () => {
    const res = await financeAccountRecharge({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const [rVisible, handleR] = useToggle()

const handleRAfter = (row: any, type: DlViewType = 'ADD') => {
  viewEntity.record = row
  viewEntity.type = type
  handleR()
}

const value = ref('全部')
const options = [
  {
    label: '待审核',
    value: '待审核'
  },
  {
    label: '已审核',
    value: '已审核'
  },
  {
    label: '已拒绝',
    value: '已拒绝'
  },
  {
    label: '全部',
    value: '全部'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="left"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="充值单据号" prop="">
            <el-input
              v-model.trim="formData.keywords"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.seller"
              :options="sellerOptions"
              placeholder="请选择"
              :fields="{
                label: 'companyName',
                value: 'id'
              }"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="0" prop="">
            <template #label></template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.status"
                :options="user_status"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <DSelect
                v-model="formData.status"
                :options="user_status"
                placeholder="请选择"
                class="flex-1"
              ></DSelect>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="付款方式" prop="">
            <DSelect
              v-model="formData.collectionAccount"
              :options="payment_method"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发起方" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建人" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审核人" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="0" prop="">
            <template #label></template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.status"
                :options="user_status"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-date-picker
                v-model="formData.status"
                type="daterange"
                placeholder=""
                class="flex-1"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <!-- class="pt-30" -->
          <el-form-item label="" prop="">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex justify-between mb-18">
      <div>
        <el-button @click="() => tableMethods.handleAdd()"> 充值 </el-button>
        <el-dropdown>
          <el-button class="ml-12">
            导入/导出
            <div class="i-ep:arrow-down ml-5"></div>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>导入批量充值</el-dropdown-item>
              <el-dropdown-item>导出选中项</el-dropdown-item>
              <el-dropdown-item>导出筛选项</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div>
        <el-checkbox label="只查看汇率变化单据" :value="1" />
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="充值单据号" prop="rechargeBillNo" width="180">
        <template #default="{ row }">
          <el-link type="primary" @click="handleRAfter(row, 'DETAIL')">
            {{ row.rechargeBillNo }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="客户" prop="seller" width="180" />
      <el-table-column label="付款方式" prop="rechargeType" width="180" />
      <el-table-column label="付款账号" prop="payAccount" min-width="180" />
      <el-table-column
        label="收款账户"
        prop="collectionAccount"
        min-width="180"
      />
      <el-table-column
        label="充值账户"
        prop="rechargeAccount"
        min-width="180"
      />
      <el-table-column label="充值金额" prop="rechargeAmount" min-width="180" />
      <el-table-column label="充值币种" prop="accountName" min-width="180" />
      <el-table-column
        label="预计到账金额"
        prop="expectedArrivalAmount"
        min-width="180"
      />
      <el-table-column
        label="实际到账金额"
        prop="actualArrivalAmount"
        min-width="180"
      />
      <el-table-column label="到账币种" prop="accountName" min-width="180" />
      <el-table-column
        label="预估汇率/实际汇率"
        prop="accountName"
        min-width="180"
      >
        <template #default="{ row }">
          <span
            >{{ row.estimateExchangeRate }}/{{ row.actualExchangeRate }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" min-width="180" />
      <el-table-column label="拒绝原因" prop="examineRemark" min-width="180" />
      <el-table-column label="发起方" prop="examineUser" min-width="180" />
      <el-table-column label="创建人" prop="createUser" min-width="180" />
      <el-table-column label="状态" prop="examineStatus" min-width="180" />
      <el-table-column label="创建时间" prop="createTime" min-width="180" />
      <el-table-column label="审核时间" prop="examineTime" min-width="180" />
      <el-table-column label="审核人" prop="examineUser" min-width="180" />
      <el-table-column label="审核备注" prop="examineRemark" min-width="180" />
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <!-- v-if="row.examineStatus === 0" -->
          <el-button type="primary" @click="handleRAfter(row)">
            审核
          </el-button>

          <!-- <el-button> 拒绝 </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
  <Review
    v-model="rVisible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></Review>
</template>

<style lang="scss" scoped></style>
