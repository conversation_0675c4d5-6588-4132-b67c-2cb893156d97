<script setup lang="ts">
import Seller from './components/Seller/index.vue'
import Buyer from './components/Buyer/index.vue'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'FundAccount'
})

const { sellerOptions } = useOptions(['sellerOptions'])
const { currencyOption } = useOptions(['currencyOption'])

provide('sellerOptions', sellerOptions)
provide('currencyOption', currencyOption)

const active = ref(1)
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="active">
      <el-tab-pane :label="'卖家'" :name="1">
        <Seller />
      </el-tab-pane>
      <el-tab-pane :label="'买家'" :name="2">
        <Buyer v-if="false" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
