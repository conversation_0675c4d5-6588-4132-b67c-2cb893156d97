<script setup lang="ts">
import {
  saveSellerBankAccountInfo,
  updateSellerBankAccountInfo
} from '@/api/fundAccount'
import type { SellerEntity } from '@/api/fundAccount/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const business_type = inject('business_type') as DictItemEntity[]
const sellerOptions = inject('sellerOptions') as Ref<CompanyInfo[]>
const currencyOption = inject('currencyOption') as Ref<CurrencyOptionEntity[]>
const settlement_method = inject('settlement_method') as DictItemEntity[]
const month = inject('month') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<SellerEntity>(
    {
      id: undefined,
      seller: undefined,
      businessType: undefined,
      currency: undefined,
      quota: undefined,
      status: undefined,
      settlementMode: undefined,
      billGenerationDay: undefined
    },
    {
      businessType: [
        { required: true, message: '请选择类型', trigger: 'change' }
      ],
      currency: [{ required: true, message: '请选择币种', trigger: 'change' }],
      settlementMode: [
        { required: true, message: '请选择结算周期', trigger: 'change' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新增账户',
  EDIT: '编辑账户',
  DETAIL: '账户详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveSellerBankAccountInfo,
  EDIT: updateSellerBankAccountInfo,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="400"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        label-width="90"
        ref="formDataRef"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="24">
            <el-form-item label="客户" prop="">
              <DSelect
                v-model="formData.seller"
                :options="sellerOptions"
                placeholder="请选择"
                :fields="{
                  label: 'companyName',
                  value: 'id'
                }"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="类型" prop="businessType">
              <DSelect
                v-model="formData.businessType"
                :options="business_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="币种" prop="currency">
              <!-- currency -->
              <DSelect
                v-model="formData.currency"
                :options="currencyOption"
                :fields="{ label: 'currencyName', value: 'currencyCode' }"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="结算周期" prop="settlementMode">
              <DSelect
                v-model="formData.settlementMode"
                :options="settlement_method"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              formData.settlementMode &&
              formData.settlementMode !== 'billBalance'
            "
            :span="24"
          >
            <el-form-item label="结算日" prop="">
              <DSelect
                v-model="formData.billGenerationDay"
                :options="month"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="信用额度" prop="">
              <el-input
                v-model.trim="formData.quota"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
