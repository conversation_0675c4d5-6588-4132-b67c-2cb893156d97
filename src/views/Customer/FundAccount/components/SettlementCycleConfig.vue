<script setup lang="ts">
import { updateSellerBankAccountInfo } from '@/api/fundAccount'
import type { SellerEntity } from '@/api/fundAccount/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'SettlementCycleConfig'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const settlement_method = inject('settlement_method') as DictItemEntity[]
const month = inject('month') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<SellerEntity>(
    {
      ids: undefined,
      quota: undefined
    },
    {
      settlementMode: [
        { required: true, message: '请选择结算周期', trigger: 'change' }
      ],
      quota: [{ required: true, message: '请输入信用额度', trigger: 'blur' }]
    }
  )

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: updateSellerBankAccountInfo,
  EDIT: updateSellerBankAccountInfo,
  DETAIL: () => Promise.resolve({})
}

const handleOpen = () => {
  console.log('viewEntity', viewEntity.record)
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      formData.value.ids = viewEntity.record?.map((item: any) => item.id)
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="结算周期配置"
    width="400"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-popover placement="bottom" :width="400" trigger="click">
      <template #reference>
        <div class="ml-20 mb-16 cursor-pointer">
          当前选择客户<span class="mx-5 color-#3489ff">{{
            viewEntity.record?.length
          }}</span
          >个
        </div>
      </template>
      <el-table :data="viewEntity.record">
        <el-table-column label="客户" prop="seller" />
        <el-table-column label="类型" prop="businessTypeName" />
        <el-table-column label="币种" prop="currencyName" />
      </el-table>
    </el-popover>
    <el-form
      :model="formData"
      :rules="rules"
      label-width="90"
      ref="formDataRef"
      @submit.prevent
      class="w-full"
    >
      <el-row :gutter="16" class="w-full">
        <el-col :span="24">
          <el-form-item label="结算周期" prop="settlementMode">
            <DSelect
              v-model="formData.settlementMode"
              :options="settlement_method"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col
          v-if="
            formData.settlementMode && formData.settlementMode !== 'billBalance'
          "
          :span="24"
        >
          <el-form-item label="结算日" prop="">
            <DSelect
              v-model="formData.billGenerationDay"
              :options="month"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="信用额度" prop="quota">
            <el-input
              v-model.trim="formData.quota"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
