<script setup lang="ts">
import { deleteSettdoc, getSettdocList } from '@/api/settlementDoc'
import type { SettdocEntity } from '@/api/settlementDoc/types'
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from '../CreateEditDialog.vue'
import SetCreditLimit from '../SetCreditLimit.vue'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'Buyer'
})
const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
  // selection
} = useTable<SettdocEntity>({
  immediate: true,
  initialFormData: {
    status: '1',
    type: undefined
  },
  fetchDataApi: async () => {
    const res = await getSettdocList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteSettdoc(ids)
      return !!res
    } else {
      const res = await deleteSettdoc([record.id])
      return !!res
    }
  }
})

const [visible, handleVisible] = useToggle()
const [sclvisible, handlescl] = useToggle()
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <el-select v-model="formData.status" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.type" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <el-select v-model="formData.type" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="最后一次充值距今" prop="" label-width="140">
            <div class="flex w-full">
              <el-select
                v-model="formData.name"
                clearable
                placeholder=""
                class="!w-120"
              >
                <el-option value="1" label="大于"></el-option>
                <el-option value="2" label="小雨"></el-option>
                <el-option value="3" label="等于"></el-option>
              </el-select>
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="最后一次充值时间" prop="" label-width="140">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <el-form-item label="" prop="" class="mt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="mb-18">
      <el-button type="primary" @click="() => handleVisible()"
        >新增账户
      </el-button>
      <el-button @click="() => handlescl()">设置信用额度</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="客户" prop="orderType" width="180" />
      <el-table-column label="币种" prop="date" min-width="180" />
      <el-table-column label="冻结金额" prop="costCategory" min-width="180" />
      <el-table-column label="账户余额" prop="tradingDate" min-width="180" />
      <el-table-column label="已消耗额度" prop="tradingDate" min-width="180" />
      <el-table-column label="信用额度" prop="tradingDate" min-width="180" />
      <el-table-column prop="tradingDate" min-width="180">
        <template #header>
          <div>最后一次充值时间</div>
          <div>最后一次充值距今</div>
        </template>
        <template #default="{ row }">
          <div>{{ row.date }}</div>
        </template>
      </el-table-column>
      <el-table-column label="账户状态" prop="status" width="180">
        <template #default="{}">
          <el-switch
            v-model="formData.name"
            active-value="1"
            inactive-value="2"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="{}">
          <el-button text>编辑</el-button>
          <el-button text>操作日志</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <!-- 新增编辑 -->
  <CreateEditDialog
    v-model="visible"
    :view-entity="viewEntity"
  ></CreateEditDialog>
  <!-- 设置信用额度 -->
  <SetCreditLimit
    v-model="sclvisible"
    :view-entity="viewEntity"
  ></SetCreditLimit>
</template>

<style lang="scss" scoped></style>
