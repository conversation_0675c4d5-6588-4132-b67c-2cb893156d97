<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import type { SellerEntity } from '@/api/fundAccount/types'
import { findSellerBankAccountList } from '@/api/fundAccount'

defineOptions({
  name: 'OperationLog'
})

const viewVisible = defineModel({ default: false })
const business_type = inject('business_type') as DictItemEntity[]
const settlement_method = inject('settlement_method') as DictItemEntity[]

const sellerOptions = inject('sellerOptions') as Ref<CompanyInfo[]>
const currencyOption = inject('currencyOption') as Ref<CurrencyOptionEntity[]>

const {
  // viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<SellerEntity>({
  immediate: false,
  initialFormData: {
    businessType: undefined,
    seller: undefined,
    currency: undefined,
    settlementMode: undefined,
    status: undefined
  },
  fetchDataApi: async () => {
    const res = await findSellerBankAccountList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const handleOpen = () => {}

const handleClose = () => {
  viewVisible.value = false
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  viewVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="操作日志"
    width="900"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="w-full px-16">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="left"
        @submit.prevent
      >
        <el-row :gutter="8">
          <el-col :span="8">
            <el-form-item label="操作时间" prop="">
              <el-date-picker
                v-model="formData.operateTime"
                type="daterange"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户" prop="">
              <DSelect
                v-model="formData.seller"
                :options="sellerOptions"
                placeholder="请选择"
                :fields="{
                  label: 'companyName',
                  value: 'id'
                }"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类型" prop="">
              <DSelect
                v-model="formData.businessType"
                :options="business_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="币种" prop="">
              <DSelect
                v-model="formData.currency"
                :options="currencyOption"
                :fields="{ label: 'currencyName', value: 'currencyCode' }"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算周期" prop="">
              <DSelect
                v-model="formData.settlementMode"
                :options="settlement_method"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="" prop="">
              <div class="w-full flex justify-end">
                <el-button
                  type="primary"
                  @click="() => tableMethods.handleQuery()"
                  >搜索</el-button
                >
                <el-button @click="() => tableMethods.handleReset()"
                  >重置</el-button
                >
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="loading"
        :data="dataList"
        :border="true"
        max-height="400"
        class="w-full"
      >
        <el-table-column label="操作时间" prop="operateTime" />
        <el-table-column label="操作人" prop="operateTime" />
        <el-table-column label="操作IP" prop="operateTime" />
        <el-table-column label="操作设备" prop="operateTime" />
        <el-table-column label="客户" prop="seller" />
        <el-table-column label="类型" prop="businessTypeName" />
        <el-table-column label="币种" prop="currencyName" />
        <el-table-column label="结算周期" prop="settlementMode" />
        <el-table-column label="结算日" prop="billGenerationDay" />
        <el-table-column label="信用额度" prop="quota" />
      </el-table>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, ->, sizes, prev, pager, next, jumper"
        :total="total"
        class="mt-16"
      />
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
