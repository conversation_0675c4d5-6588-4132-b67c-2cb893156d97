<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from '../CreateEditDialog.vue'
import SettlementCycleConfig from '../SettlementCycleConfig.vue'
import SetCreditLimit from '../SetCreditLimit.vue'
import OperationLog from '../OperationLog.vue'
import { useToggle } from '@/hooks/useToggle'
import type { SellerEntity } from '@/api/fundAccount/types'
import {
  findSellerBankAccountList,
  SellerChangeStatus
} from '@/api/fundAccount'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'Seller'
})

const business_type = useDictItem('business_type')
const user_status = useDictItem('user_status')
const settlement_method = useDictItem('settlement_method')
const month = useDictItem('month')

provide('business_type', business_type)
provide('settlement_method', settlement_method)
provide('month', month)

const sellerOptions = inject('sellerOptions') as Ref<CompanyInfo[]>
const currencyOption = inject('currencyOption') as Ref<CurrencyOptionEntity[]>

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<SellerEntity>({
  immediate: true,
  initialFormData: {
    // status: '1'
    businessType: undefined,
    seller: undefined,
    currency: undefined,
    settlementMode: undefined,
    status: undefined
  },
  fetchDataApi: async () => {
    const res = await findSellerBankAccountList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const [sccvisible, handlescc] = useToggle()
const [sclvisible, handlescl] = useToggle()
const [olvisible, handleol] = useToggle()

const checkedSelection = () => {
  if (selection.value.length === 0) {
    ElMessage.error('请选择一条数据')
    return false
  }
  return true
}

const olRow = ref<SellerEntity>()
// 操作日志
const handleOpenOl = (row?: SellerEntity) => {
  olRow.value = row
  handleol()
}

const handleOpen = (type: 1 | 2 | 3, row?: SellerEntity) => {
  viewEntity.value = 'EDIT'

  if (type === 1) {
    if (!checkedSelection()) {
      return
    }
    viewEntity.record = selection.value
    handlescc()
  }
  if (type === 2) {
    if (!checkedSelection()) {
      return
    }
    viewEntity.record = selection.value
    handlescl()
  }
  if (type === 3) {
    viewEntity.record = [row]
    handlescc()
  }
}

const updataStatus = async (val: string, row: SellerEntity) => {
  await SellerChangeStatus({ id: row.id, status: val })

  tableMethods.getList()
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.seller"
              :options="sellerOptions"
              placeholder="请选择"
              :fields="{
                label: 'companyName',
                value: 'id'
              }"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型" prop="">
            <DSelect
              v-model="formData.businessType"
              :options="business_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <DSelect
              v-model="formData.currency"
              :options="currencyOption"
              :fields="{ label: 'currencyName', value: 'currencyCode' }"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算周期" prop="">
            <DSelect
              v-model="formData.settlementMode"
              :options="settlement_method"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="最后一次充值距今" prop="" label-width="140">
            <div class="flex w-full">
              <el-select
                v-model="formData.name"
                clearable
                placeholder=""
                class="!w-120"
              >
                <el-option value="1" label="大于"></el-option>
                <el-option value="2" label="小于"></el-option>
                <el-option value="3" label="等于"></el-option>
              </el-select>
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="最后一次充值时间" prop="" label-width="140">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="mt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增账户
      </el-button>
      <el-button @click="() => handleOpen(1)">批量编辑</el-button>
      <el-button @click="() => handleOpen(2)">设置信用额度</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column label="客户" prop="seller" min-width="180" />
      <el-table-column label="类型" prop="businessTypeName" width="80" />
      <el-table-column label="币种" prop="currencyName" width="100" />
      <el-table-column label="结算周期" prop="settlementMode" width="90" />
      <el-table-column label="结算日" prop="billGenerationDay" width="90" />
      <el-table-column
        label="待付款余额"
        prop="preDeductionAmount"
        width="120"
      />
      <el-table-column label="账户余额" prop="availableAmount" width="100" />
      <el-table-column label="已消耗额度" prop="consumedQuota" width="100" />
      <el-table-column label="信用额度" prop="quota" width="100" />
      <el-table-column min-width="180">
        <template #header>
          <div>最后一次充值时间</div>
          <div>最后一次充值距今</div>
        </template>
        <template #default="{ row }">
          <div>{{ row.rechargeTime }}</div>
          <div>{{ row.rechargeDays }}</div>
        </template>
      </el-table-column>
      <el-table-column label="账户状态" prop="status" width="100">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            active-value="1"
            inactive-value="0"
            @change="(val: any) => updataStatus(val, row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="210">
        <template #default="{ row }">
          <el-button type="primary" text @click="() => handleOpen(3, row)"
            >编辑</el-button
          >
          <el-button type="primary" text @click="handleOpenOl(row)"
            >操作日志</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <!-- 新增编辑 -->
  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.refresh()"
  ></CreateEditDialog>
  <!-- 结算周期配置 -->
  <SettlementCycleConfig
    v-model="sccvisible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.refresh()"
  ></SettlementCycleConfig>
  <!-- 设置信用额度 -->
  <SetCreditLimit
    v-model="sclvisible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.refresh()"
  ></SetCreditLimit>

  <OperationLog v-model="olvisible"></OperationLog>
</template>

<style lang="scss" scoped></style>
