<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import RechargeDialog from './components/RechargeDialog.vue'
// import AllocateWarehouse from './components/AllocateWarehouse.vue'
import type { StockistEntity } from '@/api/stockist/types'
import {
  changeSellerStatusStockist,
  getListStockist,
  resetSellerPassword
} from '@/api/stockist'
import { useDictItem } from '@/hooks/useDictItem'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'Stockist'
})

const country = useDictItem('country')
const customer_level = useDictItem('customer_level')
const customer_type = useDictItem('customer_type')
const status = useDictItem('status')
const business_type = useDictItem('business_type')

provide('country', country)
provide('customer_level', customer_level)
provide('customer_type', customer_type)

const { currencyOption } = useOptions(['currencyOption'])

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<StockistEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    companyName: undefined,
    companyCode: undefined,
    companyAbbr: undefined,
    contactsName: undefined,
    contactsPhone: undefined,
    email: undefined,
    level: undefined,
    type: undefined
  },
  fetchDataApi: async () => {
    const res = await getListStockist({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const rvisible = ref(false)
const rRecord = ref()

const handleReview = (row: StockistEntity) => {
  rvisible.value = true
  rRecord.value = row
}

// const awvisible = ref(false)
// const handleAW = () => {
//   awvisible.value = true
// }

const bvisible = ref(false)
const textinfo = ref('')
const handleResetPwd = async (row: StockistEntity) => {
  const _res = await resetSellerPassword(row.id as string)
  textinfo.value = `${row.companyAbbr} 密码重置成功，新密码为 ${_res.result}`
  bvisible.value = true
}

// const handleStatusChange = async (type: '0' | '1' | '2') => {
//   if (selection.value?.length === 0) {
//     ElMessage.error('请选择卖家')
//     return
//   }
//   const ids = selection.value.map(item => item.id?.toString())
//   if (type === '0') {
//     const result = selection.value.filter(
//       item => item.status?.toString() === '0'
//     )
//     if (result?.length > 0) {
//       ElMessage.error('选中的卖家中存在停用的卖家')
//       return
//     }
//     // await disableService(ids as string[])
//   } else if (type === '1') {
//     const result = selection.value.filter(
//       item => item.status?.toString() === '1'
//     )
//     if (result?.length > 0) {
//       ElMessage.error('选中的卖家中存在启用的卖家')
//       return
//     }
//     // await enableService(ids as string[])
//   }
//   // clearSelection
//   tableMethods.handleQuery()
// }

const handleStatusChange = async (
  type: '0' | '1' | '2',
  row?: StockistEntity
) => {
  await changeSellerStatusStockist({
    status: type,
    id: row?.id as string
  })

  tableMethods.handleQuery()
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="客户代码" prop="">
            <el-input
              v-model.trim="formData.companyCode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户全称/简称" prop="">
            <el-input
              v-model.trim="formData.companyName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系人" prop="">
            <el-input
              v-model.trim="formData.contactsName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <DSelect
              v-model="formData.level"
              :options="customer_level"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账户类型" prop="">
            <DSelect
              v-model="formData.accountType"
              :options="business_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <DSelect
              v-model="formData.currency"
              :options="currencyOption"
              :fields="{ label: 'currencyName', value: 'currencyCode' }"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账户余额" prop="">
            <div class="flex w-full">
              <el-input
                v-model.trim="formData.minAmount"
                clearable
                placeholder="最小金额"
                class="flex-1"
              ></el-input>
              <div class="mx-8">-</div>
              <el-input
                v-model.trim="formData.maxAmount"
                clearable
                placeholder="最大金额"
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <el-form-item label="" prop="" class="">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button @click="tableMethods.handleReset">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新建</el-button
      >
      <el-dropdown>
        <el-button class="ml-12">
          导出
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="卖家代码" prop="companyCode" width="180" />
      <el-table-column label="公司简称" prop="companyAbbr" width="180" />
      <el-table-column label="邮箱" prop="email" width="180" />
      <el-table-column label="手机号" prop="contactsPhone" width="180" />
      <el-table-column label="公司名称" prop="companyName" width="180" />
      <el-table-column label="状态" prop="statusText" width="180" />
      <el-table-column label="头程账户余额" prop="date" width="180" />
      <el-table-column label="海外仓账户余额" prop="date" width="180" />
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="登录时间" prop="loginTime" width="180" />
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <!-- <el-button type="primary">编辑</el-button>
          <el-button type="primary">登录备货端</el-button> -->
          <!-- @click="handleResetPwd" -->
          <!-- <el-button type="primary">重置密码</el-button>
          <el-button type="primary">启用</el-button>
          <el-button type="warning">冻结</el-button> -->
          <!-- @click="handleRecharge" -->
          <!-- <el-button type="primary">充值</el-button> -->

          <el-dropdown>
            <el-button>
              编辑
              <div class="i-ep:arrow-down ml-5"></div>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="() => tableMethods.handleEdit(row)"
                  >编辑</el-dropdown-item
                >
                <el-dropdown-item>登录备货端</el-dropdown-item>
                <el-dropdown-item @click="handleResetPwd(row)"
                  >重置密码</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="row.status === '0'"
                  @click="handleStatusChange('1', row)"
                  >启用</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="row.status === '1'"
                  @click="handleStatusChange('0', row)"
                  >停用</el-dropdown-item
                >
                <el-dropdown-item @click="handleReview(row)"
                  >充值</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <!-- 新增 编辑 -->
  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
  <!-- 充值 -->
  <RechargeDialog
    v-model="rvisible"
    :rRecord="rRecord"
    @refresh="tableMethods.handleQuery"
  ></RechargeDialog>
  <!-- 仓库分配 -->
  <!-- <AllocateWarehouse v-model="awvisible"></AllocateWarehouse> -->
  <!-- 重置密码 -->
  <ClipboardDialog v-model="bvisible" :textinfo="textinfo"></ClipboardDialog>
</template>

<style lang="scss" scoped></style>
