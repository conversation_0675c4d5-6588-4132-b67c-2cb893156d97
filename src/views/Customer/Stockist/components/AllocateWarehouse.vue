<script setup lang="ts">
defineOptions({
  name: 'AllocateWarehouse'
})
const visible = defineModel({ default: false })
interface Option {
  key: number
  label: string
  disabled: boolean
}

const generateData = () => {
  const data: Option[] = []
  for (let i = 1; i <= 15; i++) {
    data.push({
      key: i,
      label: `Option ${i}`,
      disabled: i % 4 === 0
    })
  }
  return data
}

const data = ref<Option[]>(generateData())
const value = ref([])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="分配仓库"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400">
      <div class="mb-16">
        <span>客户编号</span>
        <span class="ml-10">深圳市鸿隆科技有限公司</span>
      </div>

      <el-transfer
        v-model="value"
        :data="data"
        filterable
        :titles="['待分配仓库', '已分配仓库']"
        :button-texts="['取消', '分配']"
        filter-placeholder="请输入仓库名称"
      >
        <template #left-empty>
          <el-empty :image-size="60" description="No data" />
        </template>
        <template #right-empty>
          <el-empty :image-size="60" description="No data" />
        </template>
      </el-transfer>
    </el-scrollbar>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
