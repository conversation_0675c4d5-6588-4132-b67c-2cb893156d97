<script setup lang="ts">
import { baseWarehouseManageFindAll } from '@/api/common'
import { saveStockist, updateStockist } from '@/api/stockist'
import { useFormData } from '@/hooks/useFormData'
import { phoneReg } from '@/constants'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const country = inject('country') as DictItemEntity[]
const customer_level = inject('customer_level') as DictItemEntity[]
const customer_type = inject('customer_type') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    userName: undefined,
    level: undefined,
    type: undefined,
    companyName: undefined,
    companyCode: undefined,
    companyAbbr: undefined,
    legalPersonId: undefined,
    creditCode: undefined,
    businessLicense: undefined,
    contactsName: undefined,
    contactsPhone: undefined,
    email: undefined,
    detailedAddressOne: undefined,
    detailedAddressTwo: undefined,
    country: undefined,
    province: undefined,
    city: undefined,
    remarks: undefined,
    status: undefined,
    receivingWarehouse: undefined,
    overseasWarehouse: undefined,
    legalPersonName: undefined,
    legalPersonIdCard: undefined,
    companyPhone: undefined,
    registerPlaceId: undefined,
    contactPlaceId: undefined,
    contactsIdCard: undefined,
    contactsPosition: undefined,
    postalCode: undefined
  }
}

const { formData, formDataRef, rules, resetFormData } = useFormData(
  initialFormData(),
  {
    userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
    companyName: [
      { required: true, message: '请输入客户全称', trigger: 'blur' }
    ],
    companyCode: [
      { required: true, message: '请输入客户编码', trigger: 'blur' }
    ],
    companyAbbr: [
      { required: true, message: '请输入客户简称', trigger: 'blur' }
    ],
    contactsName: [
      { required: true, message: '请输入联系人', trigger: 'blur' }
    ],
    contactsPhone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          if (!value) {
            callback()
            return
          }
          const reg = phoneReg
          if (reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入有效的手机号'))
          }
        },
        trigger: 'blur'
      }
    ],
    detailedAddressOne: [
      { required: true, message: '请输入地址一', trigger: 'blur' }
    ],
    city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
    country: [{ required: true, message: '请选择国家', trigger: 'change' }],
    postalCode: [
      { required: true, message: '请输入邮编', trigger: 'blur' }
      // {
      //   validator: (_rule, value, callback) => {
      //     if (!value) {
      //       callback()
      //       return
      //     }
      //     const reg = /^\d{6}$/
      //     if (reg.test(value)) {
      //       callback()
      //     } else {
      //       callback(new Error('请输入6位数字邮编'))
      //     }
      //   },
      //   trigger: 'blur'
      // }
    ]
  }
)

const titleMap: Record<DlViewType, string> = {
  ADD: '新增卖家',
  EDIT: '编辑卖家',
  DETAIL: '卖家详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const fileList = ref<any[]>([])

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveStockist,
  EDIT: updateStockist,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const overseasDeliveryWarehouseOptions = ref<any[]>([])
const receivingWarehouseOptions = ref<any[]>([])

const getWarehouseList = async (type: string) => {
  const res = await baseWarehouseManageFindAll(type)
  // 海外仓
  if (type === 'overseasDeliveryWarehouse') {
    overseasDeliveryWarehouseOptions.value = res.result
  } else if (type === 'receivingWarehouse') {
    // 收货仓库
    receivingWarehouseOptions.value = res.result
  }
}

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
    formData.value.level = String(formData.value.level) as any
  }
  if (formData.value.businessLicense) {
    fileList.value = [{ url: formData.value.businessLicense }]
  }
  getWarehouseList('overseasDeliveryWarehouse')
  getWarehouseList('receivingWarehouse')
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
  // 清除表单校验状态
  formDataRef.value?.clearValidate()
}
const handleCancel = () => {
  viewVisible.value = false
  // 清除表单校验状态
  formDataRef.value?.clearValidate()
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      if (fileList.value.length > 0) {
        formData.value.businessLicense = fileList.value[0].url
      }

      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar>
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="8">
            <el-form-item label="用户名" prop="userName">
              <el-input
                v-model.trim="formData.userName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户等级" prop="level">
              <DSelect
                v-model="formData.level"
                :options="customer_level"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户类型" prop="type">
              <DSelect
                v-model="formData.type"
                :options="customer_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户全称" prop="companyName">
              <el-input
                v-model.trim="formData.companyName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户编码" prop="companyCode">
              <el-input
                v-model.trim="formData.companyCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户简称" prop="companyAbbr">
              <el-input
                v-model.trim="formData.companyAbbr"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法人证件号" prop="legalPersonId">
              <el-input
                v-model.trim="formData.legalPersonId"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="营业执照号码" prop="creditCode">
              <el-input
                v-model.trim="formData.creditCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="附件" prop="businessLicense">
              <!-- businessLicense -->
              <DUpload
                v-if="viewEntity.type === 'ADD'"
                v-model="fileList"
                :limit="1"
              ></DUpload>
              <DImage v-else :url="formData.businessLicense || ''"></DImage>
              <!-- <el-upload action="#" multiple>
                <el-button type="primary">上传附件</el-button>
              </el-upload> -->
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="联系人" prop="contactsName">
              <el-input
                v-model.trim="formData.contactsName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号" prop="contactsPhone">
              <el-input
                v-model.trim="formData.contactsPhone"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model.trim="formData.email"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="地址一" prop="detailedAddressOne">
              <el-input
                v-model.trim="formData.detailedAddressOne"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地址二" prop="detailedAddressTwo">
              <el-input
                v-model.trim="formData.detailedAddressTwo"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市" prop="city">
              <el-input
                v-model.trim="formData.city"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省/州" prop="province">
              <el-input
                v-model.trim="formData.province"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="国家/地址" prop="country">
              <DSelect
                v-model="formData.country"
                :options="country"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮编" prop="postalCode">
              <el-input
                v-model.trim="formData.postalCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model.trim="formData.remarks"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="text-18">仓库分配</div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择可用收货仓库" prop="receivingWarehouse">
              <DCheckboxGroup
                v-model="formData.receivingWarehouse"
                :options="receivingWarehouseOptions"
                :fields="{ label: 'warehouseName', value: 'id' }"
              ></DCheckboxGroup>

              <!-- <el-checkbox-group v-model="formData.receivingWarehouse">
                <el-checkbox label="1" :value="1" />
              </el-checkbox-group> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择可用海外仓" prop="overseasWarehouse">
              <DCheckboxGroup
                v-model="formData.overseasWarehouse"
                :options="overseasDeliveryWarehouseOptions"
                :fields="{ label: 'warehouseName', value: 'id' }"
              ></DCheckboxGroup>
              <!-- <el-checkbox-group v-model="formData.overseasWarehouse">
                <el-checkbox label="1" :value="1" />
              </el-checkbox-group> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
