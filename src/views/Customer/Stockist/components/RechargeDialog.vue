<script setup lang="ts">
import { sellerRechargeStockist } from '@/api/stockist'
import type { StockistEntity } from '@/api/stockist/types'

defineOptions({
  name: 'RechargeDialog'
})
const visible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { rRecord } = defineProps<{
  rRecord: StockistEntity | undefined
}>()

const initialFormData = () => {
  return {
    id: undefined,
    sellerId: undefined,
    rechargeType: undefined,
    bankCard: undefined,
    bankCardName: undefined,
    rechargeAmount: undefined,
    rechargeAccount: undefined,
    bankName: undefined,
    paymentSerialNumber: undefined,
    remark: undefined,
    rechargeVoucher: undefined,
    currency: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})
const fileList = ref([])

const handleOpen = () => {
  formData.value.sellerId = rRecord?.id as any
}
const handleClose = () => {
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await sellerRechargeStockist(formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="充值"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="left"
        label-width="110"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="客户" prop="subsidiaryName">
              <el-select v-model="formData.currency" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账户" prop="subsidiaryName">
              <el-select v-model="formData.currency" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款方式" prop="subsidiaryName">
              <el-select v-model="formData.currency" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款币种" prop="currency">
              <span>人民币RMB</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值账户" prop="subsidiaryName">
              <el-select v-model="formData.currency" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="户名" prop="currency">
              <span>人民币RMB</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款账号" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号" prop="currency">
              <span>人民币RMB</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值金额" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="currency">
              <span>人民币RMB</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计到账金额" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联行号" prop="currency">
              <span>人民币RMB</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="bankName">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款码" prop="bankNumber">
              <DUpload :fileList="fileList"></DUpload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值凭证" prop="bankNumber">
              <DUpload :fileList="fileList"></DUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
