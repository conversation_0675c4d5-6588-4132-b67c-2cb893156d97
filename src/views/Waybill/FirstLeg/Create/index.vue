<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'
import FormEntity from '../components/FormEntity.vue'
import SkuTable from '../components/SkuTable.vue'
import { saveFirstVessel } from '@/api/firstLeg'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'FirstLegCreate'
})
const formRef = ref()
const skuTableRef = ref()
const router = useRouter()
const loading = ref(false)

const destination_type = useDictItem('destination_type')
const declaration_method = useDictItem('declaration_method')
const taxation_method = useDictItem('taxation_method')
const storage_type = useDictItem('storage_type')
const purchase_insurance = useDictItem('purchase_insurance')
const container_type = useDictItem('container_type')
const country = useDictItem('country')

provide('destination_type', destination_type)
provide('declaration_method', declaration_method)
provide('taxation_method', taxation_method)
provide('storage_type', storage_type)
provide('purchase_insurance', purchase_insurance)
provide('container_type', container_type)
provide('country', country)

/**
 * 校验箱数总和
 * @returns {boolean} 校验是否通过
 */
const validateBoxCount = (): boolean => {
  if (
    !skuTableRef.value?.tableData ||
    !Array.isArray(skuTableRef.value.tableData)
  ) {
    ElMessage.warning('请先添加商品信息')
    return false
  }

  const totalBoxCount = skuTableRef.value.tableData.reduce(
    (sum: number, item: any) => {
      const boxNum = Number(item.boxNum) || 0
      return sum + boxNum
    },
    0
  )

  if (totalBoxCount > 900) {
    ElMessage.error(`箱数总和不能超过900，当前总箱数：${totalBoxCount}`)
    return false
  }

  return true
}

const handleCreate = async () => {
  // 校验箱数总和
  if (!validateBoxCount()) {
    return
  }

  loading.value = true
  try {
    const res = await saveFirstVessel({
      ...formRef.value.formData,
      boxInfo: skuTableRef.value.tableData,
      onlyVessel: true
    })

    if (res.code === '200') {
      ElMessage.success('创建头程运单成功')
      // 跳转到列表页
      router.push({ path: '/waybill/firstLeg' })
    } else {
      ElMessage.error(res.message || '创建失败')
    }
  } catch (error) {
    console.error('创建头程运单失败:', error)
    ElMessage.error('系统异常，请稍后重试')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.push({ path: '/waybill/firstLeg' })
}
</script>

<template>
  <ContentWrap title="创建运单">
    <FormEntity ref="formRef"></FormEntity>
  </ContentWrap>
  <ContentWrap class="mt-20">
    <SkuTable ref="skuTableRef"></SkuTable>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="h-60 flex items-center justify-center">
      <el-button type="primary" plain @click="handleCreate" :loading="loading"
        >仅创建头程运单</el-button
      >
      <el-button type="primary" :loading="loading"
        >仅创建头程运单和海外仓入库单</el-button
      >
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
