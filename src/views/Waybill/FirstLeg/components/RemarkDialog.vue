<script setup lang="ts">
import { batchAddRemarks } from '@/api/firstLeg'
import type { FirstVesselEntity } from '@/api/firstLeg/types'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'RemarkDialog'
})

const visible = defineModel({ default: false })

// 从父组件获取选中的运单ID
const props = defineProps<{
  selection?: any[]
}>()

/**
 * 表单数据
 * customerRemarks: 客户备注
 * customerOperate: 客户备注类型 新增:0, 覆盖: 1
 * internalRemarks: 内部备注
 * systemOperate: 内部备注类型 新增:0, 覆盖: 1
 */
const formData = ref({
  customerRemarks: '',
  customerOperate: 0, // 默认为新增
  internalRemarks: '',
  systemOperate: 0 // 默认为新增
})

// 表单验证规则
const rules = ref({
  customerRemarks: [
    { max: 150, message: '客户备注不能超过150个字符', trigger: 'blur' }
  ],
  internalRemarks: [
    { max: 150, message: '内部备注不能超过150个字符', trigger: 'blur' }
  ]
})

// 表单引用
const formDataRef = ref()

// 弹窗打开时初始化表单
const handleOpen = () => {
  resetForm()
}

// 重置表单
const resetForm = () => {
  formData.value = {
    customerRemarks: '',
    customerOperate: 0,
    internalRemarks: '',
    systemOperate: 0
  }
  // 重置表单验证状态
  formDataRef.value?.resetFields()
}

// 弹窗关闭
const handleClose = () => {
  resetForm()
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 验证表单是否有填写内容
const validateFormContent = () => {
  return (
    formData.value.customerRemarks.trim() !== '' ||
    formData.value.internalRemarks.trim() !== ''
  )
}

// 提交操作
const handleConfirm = async () => {
  // 表单验证
  if (!formDataRef.value) return

  await formDataRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    // 验证是否有选中项
    if (!props.selection || props.selection.length === 0) {
      ElMessage.warning('请至少选择一条运单')
      return
    }

    // 验证是否填写了备注内容
    if (!validateFormContent()) {
      ElMessage.warning('请填写客户备注或内部备注')
      return
    }

    try {
      // 准备API请求数据
      const data = {
        ids: props.selection.map(item => item.id),
        customerRemarks: formData.value.customerRemarks,
        customerOperate: formData.value.customerOperate,
        internalRemarks: formData.value.internalRemarks,
        systemOperate: formData.value.systemOperate
      } as unknown as FirstVesselEntity

      // 调用添加备注API
      await batchAddRemarks(data)
      ElMessage.success('添加备注成功')
      visible.value = false

      // 触发刷新事件
      emit('refresh')
    } catch (error) {
      console.error(error)
      ElMessage.error('添加备注失败')
    }
  })
}

// 定义事件
const emit = defineEmits(['refresh'])
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑备注"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-width="110px"
      @submit.prevent
    >
      <el-form-item label="客户备注" prop="customerRemarks">
        <el-input
          v-model.trim="formData.customerRemarks"
          clearable
          placeholder="请输入客户备注"
          type="textarea"
          :rows="5"
          :maxlength="150"
          resize="none"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="客户备注类型" prop="customerOperate">
        <el-radio-group v-model="formData.customerOperate">
          <el-radio :label="0">新增</el-radio>
          <el-radio :label="1">覆盖</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="内部备注" prop="internalRemarks">
        <el-input
          v-model.trim="formData.internalRemarks"
          clearable
          placeholder="请输入内部备注"
          type="textarea"
          :rows="5"
          :maxlength="150"
          resize="none"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="内部备注类型" prop="systemOperate">
        <el-radio-group v-model="formData.systemOperate">
          <el-radio :label="0">新增</el-radio>
          <el-radio :label="1">覆盖</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
