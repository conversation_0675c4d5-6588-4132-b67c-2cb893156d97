<script setup lang="ts">
import type { BoxInfo, CommodityVo } from '@/api/firstLeg/types'
import AddProduct from '@/components/AddProduct/index.vue'
import type { BasicEntity, SpecificationsBasicEntity } from '@/api/goods/types'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'SkuTable'
})

type BoxInfoWithProductName = BoxInfo

const boxBarCodeRuleOptions = useDictItem('boxBarCodeRule')

const tableData = ref<BoxInfoWithProductName[]>([
  {
    boxNum: undefined,
    boxBarCode: undefined,
    boxBarCodeRule: 'DefaultBoxMarkingRules',
    systemBoxLength: undefined,
    systemBoxWidth: undefined,
    systemBoxHeight: undefined,
    systemBoxWeight: undefined,
    commodity: [
      // {
      //   commodityId: undefined,
      //   commodityCount: undefined
      // }
    ]
  }
])

// 添加箱子的方法
const handleAddBox = () => {
  const newBox: BoxInfo = {
    boxNum: undefined,
    boxBarCode: undefined,
    boxBarCodeRule: 'DefaultBoxMarkingRules',
    systemBoxLength: undefined,
    systemBoxWidth: undefined,
    systemBoxHeight: undefined,
    systemBoxWeight: undefined,
    commodity: [
      // {
      //   commodityId: undefined,
      //   commodityCount: undefined
      // }
    ]
  }
  tableData.value.push(newBox)
}

// 移除箱子的方法
const handleRemoveBox = (index: number) => {
  if (tableData.value.length === 1) {
    ElMessage.warning('至少保留一个箱子')
    return
  }
  tableData.value.splice(index, 1)

  // 重新计算序号和更新视图
  nextTick(() => {
    // 确保所有未定义的boxNum设为1
    tableData.value.forEach(item => {
      if (!item.boxNum) {
        item.boxNum = 1
      }
    })
  })
}

// 移除商品
const handleRemovecommodityItem = (index: number, idx: number) => {
  tableData.value[index].commodity?.splice(idx, 1)
}

// 记录当前操作的箱子索引
const currentBoxIndex = ref(0)

const apVisible = ref(false)
const handleAddProduct = (index: number) => {
  currentBoxIndex.value = index
  apVisible.value = true
}

type BoxInfoWithSpecificationsBasicEntity = CommodityVo &
  SpecificationsBasicEntity &
  BasicEntity

// 处理添加商品确认事件
const handleProductConfirm = (
  products: BoxInfoWithSpecificationsBasicEntity[]
) => {
  if (!products || products.length === 0) {
    return
  }

  // 获取当前箱子
  const currentBox = tableData.value[currentBoxIndex.value]
  if (!currentBox.commodity) {
    currentBox.commodity = []
  }

  // 将选择的商品添加到当前箱子
  products.forEach(product => {
    // 检查商品是否已经存在于当前箱子
    const exists = currentBox.commodity!.some(
      item => item.commodityId === product.commodityId
    )
    if (!exists) {
      currentBox.commodity!.push(product)
    }
  })

  ElMessage.success(`成功添加${products.length}个商品到箱子`)
}

// 计算总箱数
const totalBoxes = computed(() => {
  return tableData.value.reduce((total, box) => {
    // 确保箱数是有效数字，默认为0
    const boxNum = Number(box.boxNum) || 0
    return total + boxNum
  }, 0)
})

// 计算产品数和SKU种类数
const totalStats = computed(() => {
  let skuTypes = 0
  let totalProducts = 0

  // 使用Set来统计不重复的SKU数量
  const skuSet = new Set<string>()

  tableData.value.forEach(box => {
    if (box.commodity && box.commodity.length > 0) {
      box.commodity.forEach(item => {
        if (item.commodityId) {
          skuSet.add(item.commodityId)
        }
      })

      // 计算单箱总数量
      const boxTotalQuantity = box.commodity.reduce((total, item) => {
        const count = Number(item.commodityCount) || 0
        return total + count
      }, 0)

      // 产品数 = 箱数 × 单箱总数量
      const boxNum = Number(box.boxNum) || 1
      totalProducts += boxNum * boxTotalQuantity
    }
  })

  skuTypes = skuSet.size

  return {
    skuTypes,
    totalProducts
  }
})

// 计算每行的序号范围
const getIndexRange = (index: number) => {
  if (index === 0) {
    return tableData.value[0].boxNum
      ? { start: 1, end: Number(tableData.value[0].boxNum) }
      : { start: 1, end: 1 }
  }

  let start = 1
  for (let i = 0; i < index; i++) {
    if (tableData.value[i].boxNum) {
      start += Number(tableData.value[i].boxNum)
    } else {
      start += 1
    }
  }

  const end = tableData.value[index].boxNum
    ? start + Number(tableData.value[index].boxNum) - 1
    : start

  return { start, end }
}

// 计算单箱商品总数量
const calculateBoxTotalQuantity = (index: number) => {
  if (
    !tableData.value[index].commodity ||
    tableData.value[index].commodity.length === 0
  ) {
    return 0
  }

  // 累加商品数量
  return tableData.value[index].commodity.reduce((total, item) => {
    // 确保数量是有效数字
    const count = Number(item.commodityCount) || 0
    return total + count
  }, 0)
}

defineExpose({
  tableData
})
</script>

<template>
  <el-table :data="tableData" :border="true" style="width: 100%">
    <el-table-column type="index" label="序号" prop="" width="80">
      <template #default="{ $index }">
        <div class="flex-center py-5 w-full">
          <span>
            {{ getIndexRange($index).start }}
            <template
              v-if="getIndexRange($index).start !== getIndexRange($index).end"
            >
              ~ {{ getIndexRange($index).end }}
            </template>
          </span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="箱数" prop="boxNum" width="80">
      <template #default="{ row }">
        <div class="flex-center py-5 w-full">
          <el-input v-model="row.boxNum" class="w-16 text-center" />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="SKU" prop="date">
      <template #default="{ $index }">
        <div
          v-if="
            tableData[$index].commodity &&
            tableData[$index].commodity!.length > 0
          "
          class="text-center"
        >
          <template
            v-for="(item, idx) in tableData[$index].commodity"
            :key="idx"
          >
            <div class="flex-center py-5 w-full" v-if="item.commodityId">
              <span>{{ item.productSkuCode }}</span>
            </div>
          </template>
        </div>
        <div
          class="color-#3489ff flex-center cursor-pointer w-full"
          @click="handleAddProduct($index)"
        >
          <div class="i-ep:circle-plus"></div>
          <div class="ml-5">添加产品</div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="产品名称" prop="date">
      <template #default="{ $index }">
        <div
          v-for="(item, idx) in tableData[$index].commodity"
          :key="idx"
          class="flex-center py-5 w-full"
        >
          <span
            v-if="
              item.productAttrOne && item.productAttrTwo && item.productName
            "
            >{{
              item.productAttrOne +
              ' ' +
              item.productAttrTwo +
              ' ' +
              item.productName
            }}</span
          >
        </div>
      </template>
    </el-table-column>
    <el-table-column label="单箱数量" prop="date" width="100">
      <template #default="{ $index }">
        <div
          v-for="(item, idx) in tableData[$index].commodity"
          :key="idx"
          class="flex-center py-5 w-full"
        >
          <el-input v-model="item.commodityCount" />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="单箱总数量" prop="date" width="100">
      <template #default="{ $index }">
        <div class="flex-center py-5 w-full">
          <span>{{ calculateBoxTotalQuantity($index) }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="自定义箱条码" prop="date" width="120">
      <template #default="{ row }">
        <el-input v-model="row.boxBarCode" />
      </template>
    </el-table-column>
    <el-table-column label="箱条码规则" prop="date" width="180">
      <template #default="{ row }">
        <!-- <el-input v-model="row.boxBarCodeRule" /> -->
        <DSelect
          v-model="row.boxBarCodeRule"
          :options="boxBarCodeRuleOptions"
          placeholder="请选择"
        ></DSelect>
      </template>
    </el-table-column>
    <el-table-column label="箱子尺寸(CM)" prop="date" width="240">
      <template #default="{ row }">
        <div class="flex">
          <el-input v-model="row.systemBoxLength" class="w-60" />
          <el-input v-model="row.systemBoxWidth" class="w-60" />
          <el-input v-model="row.systemBoxHeight" class="w-60" />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="箱子重量(KG)" prop="date" width="120">
      <template #default="{ row }">
        <el-input v-model="row.systemBoxWeight" />
      </template>
    </el-table-column>
    <el-table-column label="操作" fixed="right" width="90">
      <template #default="{ $index, row }">
        <div class="flex-center py-5 w-full">
          <div v-if="row.commodity?.length">
            <div v-for="(_item, idx) in tableData[$index].commodity" :key="idx">
              <el-button
                type="danger"
                text
                @click="handleRemovecommodityItem($index, idx)"
                >移除</el-button
              >
            </div>
          </div>
          <el-button v-else type="danger" text @click="handleRemoveBox($index)"
            >移除</el-button
          >
        </div>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-between lh-24 my-20">
    <div>
      <span>合计:</span>
    </div>
    <div class="flex">
      <div>
        <span>SKU种类数: {{ totalStats.skuTypes }}</span>
      </div>
      <div class="ml-20">
        <span>产品数: {{ totalStats.totalProducts }}</span>
      </div>
      <div class="ml-20">
        <span>箱数: {{ totalBoxes }}</span>
      </div>
    </div>
  </div>
  <div>
    <el-button type="primary" @click="handleAddBox">添加箱子</el-button>
    <el-button>批量导入</el-button>
  </div>

  <AddProduct v-model="apVisible" @confirm="handleProductConfirm"></AddProduct>
</template>

<style lang="scss" scoped></style>
