<script setup lang="ts">
import { batchEditExpressMainOrderNumber } from '@/api/firstLeg'
import type { FirstVesselEntity } from '@/api/firstLeg/types'

defineOptions({
  name: 'EditMainOrder'
})

const visible = defineModel({ default: false })

const { selection } = defineProps<{
  selection?: FirstVesselEntity[]
}>()

const emit = defineEmits(['refresh'])

const formData = ref({
  carrier: undefined,
  expressMainOrder: undefined
})
const rules = ref({})

const handleOpen = () => {
  console.log('selection', selection)
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = async () => {
  const params = selection?.map(item => ({
    id: item.id,
    carrier: formData.value.carrier,
    expressMainOrder: formData.value.expressMainOrder
  }))
  const res = await batchEditExpressMainOrderNumber(params!)
  if (res.success) {
    ElMessage.success('编辑成功')
    emit('refresh')
    handleClose()
  } else {
    ElMessage.error('编辑失败')
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑快递主单号"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form :model="formData" :rules="rules" ref="formDataRef" @submit.prevent>
      <el-form-item label="服务商主单号" prop="">
        <div v-for="item in selection" :key="item.id">
          <div>{{ item.carrier }}</div>
          <div class="ml-16">{{ item.expressMainOrder }}</div>
        </div>
      </el-form-item>
      <el-form-item label="系统主单号" prop="">
        <el-input
          v-model.trim="formData.carrier"
          clearable
          placeholder="承运商"
          class="!w-150"
        >
        </el-input>
        <el-input
          v-model.trim="formData.expressMainOrder"
          clearable
          placeholder="单号"
          class="!w-280 ml-16"
        >
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
