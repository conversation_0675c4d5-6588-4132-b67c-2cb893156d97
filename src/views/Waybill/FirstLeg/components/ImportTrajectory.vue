<script setup lang="ts">
defineOptions({
  name: 'ImportTrajectory'
})

const visible = defineModel({ default: false })

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}

const fileList = ref([])
const handlePreview = (file: any) => {
  console.log(file)
}
const handleRemove = () => {}
const beforeRemove = () => {
  return true
}
const handleExceed = () => {}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="导入轨迹"
    width="500"
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <div class="mb-18">
        <span>下载数据导入模板:</span>
        <span class="text-blue ml-5">点击下载模板</span>
      </div>
      <div class="mb-18">
        <div class="mb-18">选择文件上传:</div>
        <el-upload
          v-model:file-list="fileList"
          action="#"
          multiple
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">选择文件</el-button>
          <span class="ml-10 text-bluegray">未选择任何文件</span>
        </el-upload>
      </div>
      <div class="text-14 text-bluegray">
        文件最大10M，格式限制： .xls,.xlsx,.doc,.docx,.pdf
      </div>
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 导入 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
