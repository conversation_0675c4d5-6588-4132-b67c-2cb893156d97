<script setup lang="ts">
import {
  baseLocationManageFindAll,
  baseWarehouseManageFindAll,
  getFirstVesselSettingListByDestinationType
} from '@/api/common'
import type { FirstVesselEntity } from '@/api/firstLeg/types'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'FormEntity'
})

const destination_type = inject('destination_type') as Ref<DictItemEntity[]>
const declaration_method = inject('declaration_method') as Ref<DictItemEntity[]>
const taxation_method = inject('taxation_method') as Ref<DictItemEntity[]>
const storage_type = inject('storage_type') as Ref<DictItemEntity[]>
const purchase_insurance = inject('purchase_insurance') as Ref<DictItemEntity[]>
const container_type = inject('container_type') as Ref<DictItemEntity[]>
const country = inject('country') as Ref<DictItemEntity[]>

const formData = ref<FirstVesselEntity>({
  customer: undefined,
  customerWaybillNumber: undefined,
  destination: undefined,
  destinationType: undefined,
  taxationMethod: 'taxIncluded',
  storageType: 'bulkCargo',
  purchaseInsurance: 'notToPurchase',
  customerRemarks: undefined,
  internalRemarks: undefined,
  declarationMethod: 'ordinaryDeclaration',
  eoriNo: undefined,
  eoriAddress: undefined,
  vatNo: undefined,
  vatAddress: undefined,
  containerType: undefined,
  recipient: undefined,
  addressOne: undefined,
  addressTwo: undefined,
  city: undefined,
  state: undefined,
  zipCode: undefined,
  country: undefined,
  phone: undefined,
  email: undefined
})

const warehouseList = ref<any[]>([])
const destinationList = ref<any[]>([])
const { sellerOptions } = useOptions(['sellerOptions'])

// 默认不传参数获取交货仓库。 传入warehouseType获取目的地
const getbaseWarehouseManageFindAll = async (warehouseType?: string) => {
  try {
    const res3 = await baseWarehouseManageFindAll(warehouseType)
    if (warehouseType) {
      // 目的地
      destinationList.value = res3.result
    } else {
      // 交货仓库
      warehouseList.value = res3.result
    }
  } catch (error) {
    console.log('error', error)
  }
}

const init = async () => {
  // 获取交货仓库
  getbaseWarehouseManageFindAll()
}

init()

// 目的地类型为电商平台时，目的地下拉选项
const getbaseLocationManageFindAll = async (warehouseType?: string) => {
  try {
    const res3 = await baseLocationManageFindAll(warehouseType)
    destinationList.value = res3.result?.map((item: any) => ({
      warehouseName: item.locationName,
      warehouseCode: item.locationName
    }))
  } catch (error) {
    console.log('error', error)
  }
}

// 目的地类型为电商平台时，运输服务下拉选项
const firstVesselOptions = ref<any[]>([])
const getFirstVesselSettingListByDestinationTypeOptions = async (
  destinationType: string
) => {
  try {
    const res3 =
      await getFirstVesselSettingListByDestinationType(destinationType)

    //  label: 'transportServiceName',
    // value: 'transportServiceCode'
    firstVesselOptions.value = res3.result
    // ?.map((item: any) => ({
    //   transportServiceName: item.transportServiceName,
    //   transportServiceCode: item.transportServiceCode
    // }))
  } catch (error) {
    console.log('error', error)
  }
}

// 标志位：记录是否是第一次 watch 触发
const isFirstWatch = ref(true)

// 监听目的地类型变化，用于编辑状态下的初始化和用户手动选择
watch(
  () => formData.value.destinationType,
  (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      // 只有在非第一次触发时才清空相关字段（即用户手动选择时）
      if (!isFirstWatch.value) {
        formData.value.shippingService = undefined
        formData.value.destination = undefined
      }

      // 根据目的地类型获取对应的运输服务和目的地选项
      if (newValue === 'overseasWarehouse') {
        getbaseWarehouseManageFindAll(newValue)
      } else if (newValue === 'platformWarehouse') {
        getbaseLocationManageFindAll(newValue)
      }
      getFirstVesselSettingListByDestinationTypeOptions(newValue)

      // 第一次触发后设置标志为 false
      isFirstWatch.value = false
    }
  },
  { immediate: true }
)

defineExpose({
  formData
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="right"
    label-width="100px"
    @submit.prevent
  >
    <el-row :gutter="8">
      <el-col :span="12">
        <el-form-item label="客户" prop="">
          <DSelect
            v-model="formData.customer"
            :options="sellerOptions"
            placeholder="请选择"
            :fields="{
              label: 'companyName',
              value: 'companyCode'
            }"
          ></DSelect>
        </el-form-item>
        <el-form-item label="客户运单号" prop="">
          <el-input
            v-model.trim="formData.customerWaybillNumber"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item label="交货仓库" prop="">
          <DSelect
            v-model="formData.deliveryWarehouse"
            :options="warehouseList"
            placeholder="请选择"
            :fields="{
              label: 'warehouseName',
              value: 'warehouseCode'
            }"
          ></DSelect>
        </el-form-item>
        <el-form-item label="目的地类型" prop="">
          <DSelect
            v-model="formData.destinationType"
            :options="destination_type"
            placeholder="请选择"
          ></DSelect>
        </el-form-item>
        <el-form-item label="运输服务" prop="">
          <DSelect
            v-model="formData.shippingService"
            :options="firstVesselOptions"
            placeholder="请选择"
            :fields="{
              label: 'transportServiceName',
              value: 'transportServiceCode'
            }"
          ></DSelect>
        </el-form-item>
        <el-form-item
          v-if="formData.destinationType !== 'otherAddress'"
          label="目的地"
          prop=""
        >
          <DSelect
            v-model="formData.destination"
            :options="destinationList"
            placeholder="请选择"
            :fields="{
              label: 'warehouseName',
              value: 'warehouseCode'
            }"
          ></DSelect>
        </el-form-item>

        <template v-if="formData.destinationType === 'otherAddress'">
          <el-form-item label="收件人" prop="">
            <el-input
              v-model.trim="formData.recipient"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="地址一" prop="">
            <el-input
              v-model.trim="formData.addressOne"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="地址二" prop="">
            <el-input
              v-model.trim="formData.addressTwo"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="城市/州/邮编" prop="">
            <div class="flex w-full">
              <el-input
                v-model.trim="formData.city"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
              <el-input
                v-model.trim="formData.state"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
              <el-input
                v-model.trim="formData.zipCode"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="国家" prop="">
            <DSelect
              v-model="formData.country"
              :options="country"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
          <el-form-item label="电话" prop="">
            <el-input
              v-model.trim="formData.phone"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="">
            <el-input
              v-model.trim="formData.email"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </template>
      </el-col>

      <el-col :span="12">
        <el-form-item label="报关方式" prop="">
          <DRadioGroup
            v-model="formData.declarationMethod"
            :options="declaration_method"
          ></DRadioGroup>
        </el-form-item>

        <el-form-item label="交税方式" prop="">
          <DRadioGroup
            v-model="formData.taxationMethod"
            :options="taxation_method"
          ></DRadioGroup>
        </el-form-item>

        <template v-if="formData.taxationMethod === 'autonomousTax'">
          <el-form-item label="VAT号" prop="">
            <el-input
              v-model.trim="formData.vatNo"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="VAT地址" prop="">
            <el-input
              v-model.trim="formData.vatAddress"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="EORI号" prop="">
            <el-input
              v-model.trim="formData.eoriNo"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
          <el-form-item label="EORI地址" prop="">
            <el-input
              v-model.trim="formData.eoriAddress"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </template>
        <el-form-item label="入库类型" prop="">
          <DRadioGroup
            v-model="formData.storageType"
            :options="storage_type"
          ></DRadioGroup>
        </el-form-item>
        <template v-if="formData.storageType === 'wholeCabinet'">
          <el-form-item label="货柜类型" prop="">
            <DSelect
              v-model="formData.containerType"
              :options="container_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </template>
        <el-form-item label="购买保险" prop="">
          <DRadioGroup
            v-model="formData.purchaseInsurance"
            :options="purchase_insurance"
          ></DRadioGroup>
        </el-form-item>
        <el-form-item label="客户备注" prop="">
          <el-input
            v-model.trim="formData.customerRemarks"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item label="内部备注" prop="">
          <el-input
            v-model.trim="formData.internalRemarks"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
