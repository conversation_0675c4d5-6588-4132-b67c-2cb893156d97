<script setup lang="ts">
import { batchPushFirstVessel } from '@/api/firstLeg'
import { ElMessage } from 'element-plus'
import type { FirstVesselEntity } from '@/api/firstLeg/types'

defineOptions({
  name: 'PushWaybill'
})

const visible = defineModel({ default: false })
const props = defineProps<{
  selection: FirstVesselEntity[]
}>()
const emits = defineEmits(['refresh'])
const serviceProviderOptions = inject('serviceProviderOptions') as any

const formData = ref({
  shippingService: [] as string[]
})
const rules = ref({})

const handleOpen = () => {
  formData.value.shippingService = []
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}

// const handleOptions = computed(() => {
//   if (!serviceProviderOptions.value) return []
//   return serviceProviderOptions.value.map((item: any) => {
//     return {
//       name: item.serviceName,
//       code: item.serviceCode,
//       children: item.serviceProviderList
//     }
//   })
// })

const getServiceData = (values: string[]) => {
  /**
   * serviceProvider 服务商
   * serviceProviderCode 服务商编码
   * serviceProviderTransportChannel 服务商运输渠道
   * serviceProviderTransportChannelName 服务商运输渠道名称
   */
  const result = {
    serviceProvider: undefined,
    serviceProviderCode: undefined,
    serviceProviderTransportChannel: undefined,
    serviceProviderTransportChannelName: undefined
  }

  // 使用for循环以便在找到匹配值后立即退出
  for (const item of serviceProviderOptions.value) {
    if (item.code === values[0]) {
      result.serviceProvider = item.name
      result.serviceProviderCode = item.code

      // 在内层循环中找到匹配值后立即退出
      for (const child of item.children) {
        if (child.code === values[1]) {
          result.serviceProviderTransportChannel = child.code
          result.serviceProviderTransportChannelName = child.name
          return result // 找到匹配值后立即返回结果
        }
      }
      break // 如果只找到外层匹配但内层没找到，也退出外层循环
    }
  }

  return result
}

const handleConfirm = async () => {
  if (
    !formData.value.shippingService ||
    !formData.value.shippingService.length
  ) {
    ElMessage.warning('请选择服务商服务')
    return
  }

  try {
    // 获取完整的服务商信息
    const serviceData = getServiceData(formData.value.shippingService)

    const data = {
      ids: props.selection.map(item => item.id),
      // shippingService: formData.value.shippingService,
      ...serviceData // 合并服务商详细信息
    } as unknown as FirstVesselEntity

    const res = await batchPushFirstVessel(data)
    if (res.success) {
      ElMessage.success('推送运单成功')
      visible.value = false
      emits('refresh')
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('推送运单失败')
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="推送运单"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="服务商服务" prop="shippingService">
        <el-cascader
          v-model="formData.shippingService"
          :options="serviceProviderOptions"
          clearable
          :props="{
            label: 'name',
            value: 'code',
            children: 'children'
          }"
          class="w-full"
        />
      </el-form-item>
    </el-form>
    <el-table :data="selection" :border="true" class="w-full">
      <el-table-column label="运单号" prop="systemWaybillNumber" width="180" />
      <el-table-column label="运输服务" prop="shippingService" width="180" />
      <el-table-column label="目的地" prop="destination" width="180" />
      <el-table-column label="国家" prop="country" width="180" />
      <el-table-column label="物品属性" prop="commodityAttribute" width="180" />
    </el-table>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
