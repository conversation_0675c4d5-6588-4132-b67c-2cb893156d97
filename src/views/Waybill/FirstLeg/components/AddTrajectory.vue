<script setup lang="ts">
import { ref, computed } from 'vue'
import { batchAddTrack, batchUpdateTrack } from '@/api/firstLeg'
import { dayjs, ElMessage } from 'element-plus'
import type { FirstVesselTrajectoryVo } from '@/api/firstLeg/types'

defineOptions({
  name: 'AddTrajectory'
})

const visible = defineModel({ default: false })

// 从父组件获取选中的运单ID和当前轨迹
const props = defineProps<{
  selection?: any[]
  trajectory?: FirstVesselTrajectoryVo | null
  type?: 'add' | 'edit'
}>()

// 默认类型为add
const type = computed(() => props.type || 'add')

/**
 * 表单数据
 * track: 运输轨迹
 * trackTime: 轨迹时间
 */
const formData = ref({
  track: '',
  trackTime: '',
  id: undefined
})

const rules = ref({
  track: [
    { required: true, message: '请输入运输轨迹', trigger: 'blur' },
    { max: 150, message: '轨迹内容不能超过150个字符', trigger: 'blur' }
  ],
  trackTime: [{ required: true, message: '请选择轨迹时间', trigger: 'blur' }]
})

// 表单引用
const formDataRef = ref()

// 使用当前时间
const useCurrentTime = () => {
  formData.value.trackTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
  // 清除时间字段的校验状态
  formDataRef.value?.clearValidate('trackTime')
}

// 弹窗打开时初始化表单
const handleOpen = () => {
  resetForm()
  // 如果是编辑模式，填充表单数据
  if (props.trajectory) {
    formData.value = {
      id: props.trajectory.id || ('' as any),
      track: props.trajectory.track || '',
      trackTime: props.trajectory.trackTime || ''
    }
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    track: '',
    trackTime: '',
    id: undefined
  }
  // 重置表单验证状态
  formDataRef.value?.resetFields()
}

// 弹窗关闭
const handleClose = () => {
  resetForm()
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 定义事件
const emit = defineEmits<{
  success: [
    { type: 'add' | 'edit'; formData: { track: string; trackTime: string } }
  ]
}>()

// 提交操作
const handleConfirm = async () => {
  // 表单验证
  if (!formDataRef.value) return

  await formDataRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    // 验证是否有选中项
    if (!props.selection || props.selection.length === 0) {
      ElMessage.warning('请至少选择一条运单')
      return
    }

    try {
      // 准备API请求数据
      const data = props.selection.map(item => ({
        id: item.id,
        track: formData.value.track,
        trackTime: formData.value.trackTime
      }))

      if (type.value === 'edit' && props.trajectory) {
        // 编辑轨迹
        const { success } = await batchUpdateTrack([formData.value])
        if (success) {
          ElMessage.success('更新轨迹成功')
          visible.value = false
          // 触发成功事件
          emit('success', {
            type: type.value,
            formData: {
              track: formData.value.track,
              trackTime: formData.value.trackTime
            }
          })
        }
      } else {
        // 添加轨迹
        const { success } = await batchAddTrack(data)
        if (success) {
          ElMessage.success('添加轨迹成功')
          visible.value = false
          // 触发成功事件
          emit('success', {
            type: type.value,
            formData: {
              track: formData.value.track,
              trackTime: formData.value.trackTime
            }
          })
        }
      }
    } catch (error) {
      console.error(error)
      ElMessage.error(type.value === 'edit' ? '更新轨迹失败' : '添加轨迹失败')
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="type === 'edit' ? '编辑轨迹' : '添加轨迹'"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      label-position="top"
      :rules="rules"
      ref="formDataRef"
      @submit.prevent
    >
      <el-form-item label="时间" prop="trackTime">
        <div class="flex items-center">
          <el-date-picker
            v-model="formData.trackTime"
            type="datetime"
            placeholder="请选择轨迹时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          <div class="ml-16 color-blue cursor-pointer" @click="useCurrentTime">
            使用当前时间
          </div>
        </div>
      </el-form-item>
      <el-form-item label="运输轨迹" prop="track">
        <el-input
          v-model.trim="formData.track"
          clearable
          placeholder="请输入运输轨迹内容"
          type="textarea"
          :rows="5"
          :maxlength="150"
          resize="none"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
