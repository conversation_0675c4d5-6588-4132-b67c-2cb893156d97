<script setup lang="ts">
import { batchViewReceiptData } from '@/api/firstLeg'
import type { FirstVesselEntity } from '@/api/firstLeg/types'

defineOptions({
  name: 'ViewReceivingData'
})

const visible = defineModel({ default: false })

const { selection } = defineProps<{
  selection: FirstVesselEntity[]
}>()

const tableData = ref([])
const countData = ref<any>({
  totalNum: 0,
  totalActualWeight: 0,
  totalVolume: 0,
  totalMaterialWeight: 0,
  totalHeavyCharges: 0
})

// 格式化空值显示
const formatEmptyValue = (value: any) => {
  return value === null || value === undefined || value === '' ? '-' : value
}

const handleOpen = () => {
  if (selection.length > 0) {
    batchViewReceiptData({
      ids: selection.map(item => item.id) as string[]
    }).then(res => {
      console.log(res)
      tableData.value = res.result.box
      countData.value = {
        totalNum: res.result.totalNum,
        totalActualWeight: res.result.totalActualWeight,
        totalVolume: res.result.totalVolume,
        totalMaterialWeight: res.result.totalMaterialWeight,
        totalHeavyCharges: res.result.totalHeavyCharges
      }
    })
  }
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
// const handleConfirn = () => {
//   visible.value = false
// }
</script>

<template>
  <el-dialog
    v-model="visible"
    title="拣货"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-row :gutter="16">
      <el-col :span="18">
        <div class="mb-8 font-600">仓库收货数据</div>
        <el-table
          :data="tableData"
          :border="true"
          max-height="400"
          style="width: 100%"
        >
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="箱号" prop="boxNo" min-width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.boxNo) }}
            </template>
          </el-table-column>
          <el-table-column label="收费重" prop="heavyCharges" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.heavyCharges) }}
            </template>
          </el-table-column>
          <el-table-column label="长(cm)" prop="thirdBoxLength" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.thirdBoxLength) }}
            </template>
          </el-table-column>
          <el-table-column label="宽(cm)" prop="thirdBoxWidth" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.thirdBoxWidth) }}
            </template>
          </el-table-column>
          <el-table-column label="高(cm)" prop="thirdBoxHeight" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.thirdBoxHeight) }}
            </template>
          </el-table-column>
          <el-table-column label="实重(kg)" prop="actualWeight" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.actualWeight) }}
            </template>
          </el-table-column>
          <el-table-column label="体积(m³)" prop="volume" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.volume) }}
            </template>
          </el-table-column>
          <el-table-column label="材积重(kg)" prop="materialWeight" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.materialWeight) }}
            </template>
          </el-table-column>
          <el-table-column label="围长(cm)" prop="name" width="100">
            <template #default="{ row }">
              {{ formatEmptyValue(row.name) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="lh-18 flex items-center justify-between mt-8">
          <div>
            <span>总件数：</span>
            <span class="color-[#f05459]">{{
              formatEmptyValue(countData.totalNum)
            }}</span>
          </div>
          <div>
            <span>总实重：</span>
            <span class="color-[#f05459]">{{
              formatEmptyValue(countData.totalActualWeight)
            }}</span>
          </div>
          <div>
            <span>总材积重：</span>
            <span class="color-[#f05459]">{{
              formatEmptyValue(countData.totalMaterialWeight)
            }}</span>
          </div>
          <div>
            <span>总体积：</span>
            <span class="color-[#f05459]">{{
              formatEmptyValue(countData.totalVolume)
            }}</span>
          </div>
          <div>
            <span>总收费重：</span>
            <span class="color-[#f05459]">{{
              formatEmptyValue(countData.totalHeavyCharges)
            }}</span>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="mb-8 font-600">客户数据</div>
        <el-table
          :data="tableData"
          :border="true"
          max-height="400"
          style="width: 100%"
        >
          <el-table-column label="长(cm)" prop="systemBoxLength" width="80">
            <template #default="{ row }">
              {{ formatEmptyValue(row.systemBoxLength) }}
            </template>
          </el-table-column>
          <el-table-column label="宽(cm)" prop="systemBoxWidth" width="80">
            <template #default="{ row }">
              {{ formatEmptyValue(row.systemBoxWidth) }}
            </template>
          </el-table-column>
          <el-table-column label="高(cm)" prop="systemBoxHeight" width="80">
            <template #default="{ row }">
              {{ formatEmptyValue(row.systemBoxHeight) }}
            </template>
          </el-table-column>
          <el-table-column label="重量(kg)" prop="systemBoxWeight" width="80">
            <template #default="{ row }">
              {{ formatEmptyValue(row.systemBoxWeight) }}
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <div></div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <!-- <el-button type="primary" @click="handleConfirn"> 确认 </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
