<script setup lang="ts">
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'WaybillStatus'
})

const visible = defineModel({ default: false })

const props = defineProps<{
  type: 1 | 2 | 3 | 4 | 5
  selection: any[]
}>()

const emit = defineEmits(['update'])

const formData = ref({
  name: '',
  num: 0
})

const handleOpen = () => {
  formData.value.name = ''
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  if (props.type === 2) {
    if (formData.value.name !== '确认调整') {
      ElMessage.warning('请输入"确认调整"')
      return
    }
  }
  emit('update', props.type)
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    width="400"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex">
      <div>
        <div
          v-if="type === 1"
          class="i-ep:circle-check-filled text-20 color-[#58ba2d]"
        ></div>

        <div
          v-if="[2, 4, 5].includes(type)"
          class="i-ep:warning-filled text-20 color-[#e8b164]"
        ></div>
      </div>
      <div>
        <div v-if="type === 1" class="ml-8">确定要变更运单状态吗?</div>
        <div v-if="type === 2" class="ml-8">
          <div>
            确认要对以下<span class="color-red">{{ selection.length }}</span
            >个订单进行此操作?
          </div>
          <div class="mt-8">
            如确认请输入<span class="color-red">"确认调整"</span>
          </div>
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
            class="mt-8"
          ></el-input>
        </div>

        <div v-if="type === 4" class="ml-8">确认要取消运单吗?</div>
        <div v-if="type === 5" class="ml-8">确认要删除运单费用吗?</div>
      </div>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
