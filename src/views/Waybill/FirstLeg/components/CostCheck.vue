<script setup lang="ts">
defineOptions({
  name: 'CostCheck'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    width="400"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex">
      <div>
        <div class="i-ep:warning-filled text-20 color-[#e8b164]"></div>
        <div class="i-ep:warning-filled text-20 color-[#f05459]"></div>
        <div class="i-ep:warning-filled text-20 color-[#f05459]"></div>
      </div>
      <div>
        <div class="ml-8">
          <div>以下运单已添加费用</div>
          <div>KATxxxxxxx</div>
          <div>请先取消账单后进行操作</div>
        </div>

        <div class="ml-8">
          <div>以下运单已生成账单</div>
          <div>KATxxxxxxx</div>
          <div>请先取消账单后进行操作</div>
        </div>

        <div class="ml-8">
          <div>以下运单已核销</div>
          <div>KATxxxxxxx</div>
          <div>请再结算单管理中添加费用</div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
