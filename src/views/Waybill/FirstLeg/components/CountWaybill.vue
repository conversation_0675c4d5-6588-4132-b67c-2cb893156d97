<script setup lang="ts">
import { batchViewStatisticsData } from '@/api/firstLeg'
import type { FirstVesselEntity } from '@/api/firstLeg/types'

defineOptions({
  name: 'CountWaybill'
})

const visible = defineModel({ default: false })

const { selection } = defineProps<{
  selection: FirstVesselEntity[]
}>()

const emit = defineEmits(['refresh'])

const countData = ref<any>({})

const handleOpen = () => {
  batchViewStatisticsData({
    ids: selection.map(item => item.id) as string[]
  }).then(res => {
    countData.value = res.result
  })
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
  emit('refresh')
}
// const handleConfirn = () => {
//   visible.value = false
// }
</script>

<template>
  <el-dialog
    v-model="visible"
    title="统计"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div border="1px solid #b6b6b6" class="w-full rounded-8 p-10">
      <el-row :gutter="16">
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">客户数</div>
          <div class="fw-600 lh-20">{{ countData.customerNum }}</div>
        </el-col>
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">运单数</div>
          <div class="fw-600 lh-20">{{ countData.waybillNum }}</div>
        </el-col>
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">客户箱数</div>
          <div class="fw-600 lh-20">{{ countData.customerBoxNum }}</div>
        </el-col>
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">客户实重</div>
          <div class="fw-600 lh-20">{{ countData.customerActualWeight }}</div>
        </el-col>
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">客户材积重</div>
          <div class="fw-600 lh-20">{{ countData.customerMaterialWeight }}</div>
        </el-col>
        <el-col :span="4" class="text-center mb-16">
          <div class="lh-20">客户体积</div>
          <div class="fw-600 lh-20">{{ countData.customerVolume }}</div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收货箱数</div>
          <div class="fw-600 lh-20">{{ countData.receiveBoxNum }}</div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收货实重</div>
          <div class="fw-600 lh-20">{{ countData.receiveActualWeight }}</div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收货材积重</div>
          <div class="fw-600 lh-20">{{ countData.receiveMaterialWeight }}</div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收货体积</div>
          <div class="fw-600 lh-20">{{ countData.receiveMaterialWeight }}</div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收费重(kg)</div>
          <div class="fw-600 lh-20">
            {{ countData.receiveTotalKilogramHeavyCharges }}
          </div>
        </el-col>
        <el-col :span="4" class="text-center">
          <div class="lh-20">收费重(m³)</div>
          <div class="fw-600 lh-20">
            {{ countData.receiveTotalCubicMetersHeavyCharges }}
          </div>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">关闭</el-button>
        <!-- <el-button type="primary" @click="handleConfirn"> 确认 </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
