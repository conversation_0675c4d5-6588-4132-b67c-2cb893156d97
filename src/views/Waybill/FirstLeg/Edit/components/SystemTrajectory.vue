<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { FirstVesselTrajectoryVo } from '@/api/firstLeg/types'
import {
  getFirstVesselSystemTrajectoryList,
  batchRemoveTrack
} from '@/api/firstLeg'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddTrajectory from '../../components/AddTrajectory.vue'

defineOptions({
  name: 'SystemTrajectory'
})

const detail = inject('detail') as Ref<any>

const props = defineProps<{
  id?: string
}>()

const loading = ref(false)
const systemTrajectoryList = ref<FirstVesselTrajectoryVo[]>([])
const showAddTrajectoryDialog = ref(false)
const currentTrajectory = ref<FirstVesselTrajectoryVo | null>(null)

// 格式化轨迹列表为timeline格式
const formatTrajectory = (list: FirstVesselTrajectoryVo[]) => {
  return list.map(item => ({
    ...item,
    content: item.trackMessage || '',
    timestamp: item.createTime || ''
  }))
}

// 系统轨迹列表
const activities = computed(() => {
  return formatTrajectory(systemTrajectoryList.value)
})

/**
 * 获取系统轨迹列表
 */
const getSystemTrajectory = async () => {
  if (!props.id) return

  try {
    loading.value = true
    const { success, result } = await getFirstVesselSystemTrajectoryList({
      id: String(props.id)
    })
    if (success && result) {
      systemTrajectoryList.value = result
    }
  } catch (error) {
    console.error('获取系统轨迹列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 实时同步新轨迹
 */
const handleSyncNewTrajectory = async () => {
  await getSystemTrajectory()
  ElMessage.success('轨迹同步成功')
}

/**
 * 处理添加轨迹
 */
const handleAddTrajectory = () => {
  currentTrajectory.value = null
  showAddTrajectoryDialog.value = true
}

/**
 * 处理编辑轨迹
 */
const handleEditTrajectory = async (trajectory: FirstVesselTrajectoryVo) => {
  console.log('trajectory', trajectory)

  currentTrajectory.value = {
    id: trajectory.id || '',
    trackTime: trajectory.createTime || '',
    track: trajectory.trackMessage || ''
  }

  console.log('currentTrajectory', currentTrajectory.value)

  showAddTrajectoryDialog.value = true
}

/**
 * 处理删除轨迹
 */
const handleDeleteTrajectory = async (trajectory: FirstVesselTrajectoryVo) => {
  try {
    await ElMessageBox.confirm('确认删除该轨迹吗？', '提示', {
      type: 'warning'
    })

    const { success } = await batchRemoveTrack({
      ids: trajectory.id ? [trajectory.id] : []
    })

    if (success) {
      ElMessage.success('删除成功')
      await getSystemTrajectory()
    }
  } catch (error) {
    console.error('删除轨迹失败:', error)
  }
}

/**
 * 处理轨迹添加/编辑成功
 */
const handleTrajectorySuccess = async () => {
  try {
    // 刷新系统轨迹
    await getSystemTrajectory()
  } catch (error) {
    console.error('刷新轨迹失败:', error)
    ElMessage.error('刷新轨迹失败')
  }
}

// 初始化数据
onMounted(() => {
  if (props.id) {
    getSystemTrajectory()
  }
})
</script>

<template>
  <ContentWrap title="系统运输轨迹" class="h-50%">
    <template #header>
      <div class="w-full flex justify-end">
        <el-button
          size="small"
          type="primary"
          plain
          @click="handleSyncNewTrajectory"
          >实时同步新轨迹</el-button
        >
        <el-button size="small" type="primary" @click="handleAddTrajectory"
          >添加</el-button
        >
      </div>
    </template>

    <div class="h-full flex mb-20">
      <div>{{ detail?.carrier }}</div>
      <div class="ml-5">{{ detail?.expressMainOrder }}</div>
    </div>

    <el-timeline v-loading="loading">
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :timestamp="activity.timestamp"
        placement="top"
        type="primary"
      >
        <div class="flex items-center justify-between">
          <div>{{ activity.content }}</div>
          <div class="flex">
            <div
              class="i-ep:edit cursor-pointer"
              @click="handleEditTrajectory(activity)"
            ></div>
            <div
              class="i-ep:delete ml-8 cursor-pointer"
              @click="handleDeleteTrajectory(activity)"
            ></div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>

    <!-- 添加/编辑轨迹弹窗 -->
    <AddTrajectory
      v-model="showAddTrajectoryDialog"
      :selection="props.id ? [{ id: props.id }] : []"
      :trajectory="currentTrajectory"
      :type="currentTrajectory ? 'edit' : 'add'"
      @success="handleTrajectorySuccess"
    />
  </ContentWrap>
</template>
