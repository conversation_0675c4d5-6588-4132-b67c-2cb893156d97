<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getThridBoxList,
  batchUpdateServiceProviderMeasureData
} from '@/api/firstLeg'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ThirdPartyBoxData'
})

const props = defineProps<{
  id?: string
}>()

const loading = ref(false)
const tableData = ref([])

/**
 * 获取服务商收货数据列表
 */
const getThirdBoxList = async () => {
  if (!props.id) return

  try {
    loading.value = true
    const { success, result } = await getThridBoxList({
      id: String(props.id)
    })
    if (success && result) {
      tableData.value = result
    }
  } catch (error) {
    console.error('获取服务商收货数据失败:', error)
    ElMessage.error('获取服务商收货数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 更新服务商收货数据
 */
const handleUpdateThirdBox = async () => {
  if (!props.id) return

  try {
    loading.value = true
    await batchUpdateServiceProviderMeasureData({
      id: String(props.id)
    })
    ElMessage.success('数据更新成功')
    await getThirdBoxList()
  } catch (error) {
    console.error('更新服务商收货数据失败:', error)
    ElMessage.error('更新服务商收货数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(() => {
  if (props.id) {
    getThirdBoxList()
  }
})
</script>

<template>
  <ContentWrap title="服务商收货数据">
    <template #header>
      <div class="w-full flex justify-end">
        <el-button size="small" @click="handleUpdateThirdBox"
          >更新数据</el-button
        >
      </div>
    </template>
    <el-table
      :data="tableData"
      :border="true"
      style="width: 100%; height: 400px"
      v-loading="loading"
    >
      <el-table-column label="箱号" prop="boxBarCode" />
      <el-table-column label="客户数据">
        <template #default="{ row }">
          <div>{{ row.systemBoxWeight }}kg</div>
          <div>
            {{
              `${row.systemBoxLength}*${row.systemBoxWidth}*${row.systemBoxHeight}`
            }}cm
          </div>
        </template>
      </el-table-column>
      <el-table-column label="拣货数据">
        <template #default="{ row }">
          <div>
            {{ row.thirdBoxWeight ? `${row.thirdBoxWeight}kg` : '-' }}
          </div>
          <div>
            {{
              row.thirdBoxLength && row.thirdBoxWidth && row.thirdBoxHeight
                ? `${row.thirdBoxLength}*${row.thirdBoxWidth}*${row.thirdBoxHeight}cm`
                : '-'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #header>
          <div>快递承运商</div>
          <div>单号</div>
        </template>
        <template #default="{ row }">
          <div>
            {{ row.carrier ?? '-' }}
          </div>
          <div>
            {{ row.expressMainOrder ?? '-' }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>
