<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  batchEditExpressMainOrderNumber,
  pickingGoods,
  savePickingGoodsData
} from '@/api/firstLeg'

/**
 * 拣货弹窗组件
 * 用于管理箱子的长宽高重量等信息
 */
defineOptions({ name: 'PickBoxDialog' })

const detail = inject('detail') as Ref<any>

// 接收父组件传入的箱子列表数据
const { boxList = [], id } = defineProps<{
  boxList?: any[]
  id?: string
}>()

// 定义emit
const emit = defineEmits(['update'])

// 控制弹窗显示状态
const dialogVisible = defineModel({ default: false })

// 表单数据，用于手动输入长宽高重量
const form = ref({
  length: '',
  width: '',
  height: '',
  weight: '',
  boxCount: ''
})

// 承运商和单号表单数据
const carrierForm = ref({
  carrier: '',
  expressMainOrder: ''
})

// 用于增加箱数的输入框数据
const boxCount = ref('')

// 表格数据
const tableData = ref<any[]>([])

/**
 * 为所有行计算材积重和收费重
 */
const calculateAllRows = async () => {
  const rowsToCalculate = tableData.value.filter(
    item => item.length && item.width && item.height && item.weight
  )
  if (rowsToCalculate.length === 0) return

  try {
    const res = await pickingGoods(
      rowsToCalculate.map(item => ({
        id: item.id,
        shippingService: detail.value.shippingService,
        length: item.length,
        width: item.width,
        height: item.height,
        weight: item.weight,
        carrier: item.carrier,
        waybillNumber: item.expressMainOrder
      }))
    )
    if (res.success && res.result) {
      res.result.forEach((calculatedItem: any) => {
        const correspondingRow = tableData.value.find(
          row => row.id === calculatedItem.id
        )
        if (correspondingRow) {
          correspondingRow.materialWeight = calculatedItem.materialWeight
          correspondingRow.heavyCharges = calculatedItem.heavyCharges
          correspondingRow.actualWeight = calculatedItem.actualWeight
          correspondingRow.weightProduct = calculatedItem.weightProduct
          correspondingRow.volume = calculatedItem.volume
          correspondingRow.totalHeavyCharges = calculatedItem.totalHeavyCharges
        }
      })
      ElMessage.success('费用计算成功')
    } else {
      ElMessage.error(res.message || '费用计算失败')
    }
  } catch (error) {
    console.error('费用计算失败:', error)
    ElMessage.error('费用计算失败')
  }
}

/**
 * 计算单行
 * @param row
 */
const handleCalculate = async (row: any) => {
  if (!row.length || !row.width || !row.height || !row.weight) {
    return
  }
  try {
    const res = await pickingGoods([
      {
        id: row.id,
        shippingService: detail.value.shippingService,
        length: row.length,
        width: row.width,
        height: row.height,
        weight: row.weight,
        carrier: row.carrier,
        waybillNumber: row.expressMainOrder
      }
    ])
    if (res.success && res.result && res.result.length > 0) {
      const calculatedItem = res.result[0]
      row.materialWeight = calculatedItem.materialWeight
      row.heavyCharges = calculatedItem.heavyCharges
      row.actualWeight = calculatedItem.actualWeight
      row.weightProduct = calculatedItem.weightProduct
      row.volume = calculatedItem.volume
      row.totalHeavyCharges = calculatedItem.totalHeavyCharges
    } else {
      ElMessage.error(res.message || '计算失败')
    }
  } catch (error) {
    console.error('计算失败:', error)
  }
}

/**
 * 填充服务商数据
 * 将boxList中的thirdBoxLength、thirdBoxWidth、thirdBoxHeight、thirdBoxWeight填充到表格中
 */
const handleFillServiceProvider = () => {
  tableData.value = tableData.value.map(item => ({
    ...item,
    length: item.thirdBoxLength || '',
    width: item.thirdBoxWidth || '',
    height: item.thirdBoxHeight || '',
    weight: item.thirdBoxWeight || '',
    carrier: item.carrier || '',
    expressMainOrder: item.expressMainOrder || '',
    materialWeight: item.materialWeight || '',
    heavyCharges: item.heavyCharges || '',
    actualWeight: item.actualWeight || '',
    weightProduct: item.weightProduct || '',
    volume: item.volume || '',
    totalHeavyCharges: item.totalHeavyCharges || ''
  }))
  calculateAllRows()
}

/**
 * 填充客户数据
 * 将boxList中的systemBoxLength、systemBoxWidth、systemBoxHeight、systemBoxWeight填充到表格中
 */
const handleFillCustomer = () => {
  tableData.value = tableData.value.map(item => ({
    ...item,
    length: item.systemBoxLength || '',
    width: item.systemBoxWidth || '',
    height: item.systemBoxHeight || '',
    weight: item.systemBoxWeight || '',
    carrier: item.carrier || '',
    expressMainOrder: item.expressMainOrder || '',
    materialWeight: item.materialWeight || '',
    heavyCharges: item.heavyCharges || '',
    actualWeight: item.actualWeight || '',
    weightProduct: item.weightProduct || '',
    volume: item.volume || '',
    totalHeavyCharges: item.totalHeavyCharges || ''
  }))
  calculateAllRows()
}

/**
 * 手动填充数据
 * 将表单中输入的长宽高重量填充到表格中
 */
const handleFill = () => {
  // 校验箱数必填
  if (
    !form.value.boxCount ||
    isNaN(Number(form.value.boxCount)) ||
    Number(form.value.boxCount) <= 0
  ) {
    ElMessage.warning('请输入箱数')
    return
  }
  const count = parseInt(form.value.boxCount)

  // 获取所有"长/宽/高/重量"都为空的行的索引
  const emptyRows = tableData.value
    .map((item, idx) =>
      !item.length && !item.width && !item.height && !item.weight ? idx : -1
    )
    .filter(idx => idx !== -1)

  if (emptyRows.length === 0) {
    ElMessage.warning('没有可填充的空行')
    return
  }
  // 只填充前count个空行
  for (let i = 0; i < count && i < emptyRows.length; i++) {
    const row = tableData.value[emptyRows[i]]
    row.length = form.value.length
    row.width = form.value.width
    row.height = form.value.height
    row.weight = form.value.weight
  }
  // 清空表单
  form.value.length = ''
  form.value.width = ''
  form.value.height = ''
  form.value.weight = ''
  form.value.boxCount = ''
  calculateAllRows()
}

/**
 * 清空指定行的长宽高重量数据
 */
const handleClearDimensions = (index: number) => {
  const row = tableData.value[index]
  row.length = ''
  row.width = ''
  row.height = ''
  row.weight = ''
  row.carrier = ''
  row.expressMainOrder = ''
  row.materialWeight = ''
  row.heavyCharges = ''
}

/**
 * 清空指定行的承运商和单号数据
 */
const handleClearCarrier = (index: number) => {
  const row = tableData.value[index]
  row.carrier = ''
  row.expressMainOrder = ''
}

/**
 * 清空所有数据
 * 清空表格中所有行的长宽高重量、承运商和单号数据
 */
const handleClearAll = () => {
  tableData.value = tableData.value.map(item => ({
    ...item,
    length: '',
    width: '',
    height: '',
    weight: '',
    carrier: '',
    expressMainOrder: '',
    materialWeight: '',
    heavyCharges: '',
    actualWeight: '',
    weightProduct: '',
    volume: '',
    totalHeavyCharges: ''
  }))
}

/**
 * 添加新的箱子数据
 */
const handleAddBox = () => {
  const count = parseInt(boxCount.value) || 1
  for (let i = 0; i < count; i++) {
    tableData.value.push({
      boxBarCode: '',
      length: '',
      width: '',
      height: '',
      weight: '',
      volumeWeight: '',
      chargeWeight: '',
      carrier: '',
      expressMainOrder: '',
      materialWeight: '0',
      heavyCharges: '0',
      isNew: true,
      actualWeight: '',
      weightProduct: '',
      volume: '',
      totalHeavyCharges: ''
    })
  }
  boxCount.value = '' // 添加完成后清空输入框
}

/**
 * 移除指定行的数据
 */
const handleRemoveRow = (index: number) => {
  tableData.value.splice(index, 1)
}

/**
 * 确认按钮点击事件
 */
const handleConfirm = async () => {
  try {
    const res = await savePickingGoodsData(
      tableData.value.map(item => ({
        boxId: item.id, // 箱id
        pickBoxLength: item.length,
        pickBoxWidth: item.width,
        pickBoxHeight: item.height,
        pickBoxWeight: item.weight,
        materialWeight: item.materialWeight,
        heavyCharges: item.heavyCharges,
        waybillId: id, // 运单id
        actualWeight: item.actualWeight,
        weightProduct: item.weightProduct,
        volume: item.volume,
        totalHeavyCharges: item.totalHeavyCharges,
        carrier: item.carrier,
        expressMainOrder: item.waybillNumber
      }))
    )
    if (res.success) {
      ElMessage.success('保存成功')
      // 关闭弹窗
      dialogVisible.value = false
      // 通知父组件更新数据
      emit('update')
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

/**
 * 批量导入功能
 */
const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中')
}

/**
 * 弹窗打开时初始化表单
 */
const handleOpen = () => {
  console.log(boxList)
  tableData.value = boxList.map(item => {
    return {
      ...item,
      boxNo: item.boxNo || '',
      length: item.pickBoxLength || '',
      width: item.pickBoxWidth || '',
      height: item.pickBoxHeight || '',
      weight: item.pickBoxWeight || '',
      volumeWeight: item.volumeWeight || '',
      chargeWeight: item.chargeWeight || '',
      carrier: item.carrier || '',
      expressMainOrder: item.expressMainOrder || '',
      waybillNumber: item.expressMainOrder || '',
      materialWeight: item.materialWeight || '',
      heavyCharges: item.heavyCharges || '',
      isNew: false, // 标记为原有数据
      actualWeight: item.actualWeight || '',
      weightProduct: item.weightProduct || '',
      volume: item.volume || '',
      totalHeavyCharges: item.totalHeavyCharges || ''
    }
  })
}

/**
 * 弹窗关闭事件
 */
const handleClose = () => {}

/**
 * 添加承运商主单号
 * 调用接口批量更新承运商和主单号
 */
const handleAddCarrierMainOrder = async () => {
  try {
    if (!id) {
      ElMessage.warning('未找到运单ID')
      return
    }

    if (!carrierForm.value.carrier || !carrierForm.value.expressMainOrder) {
      ElMessage.warning('请填写承运商和主单号')
      return
    }

    await batchEditExpressMainOrderNumber([
      {
        id,
        carrier: carrierForm.value.carrier,
        expressMainOrder: carrierForm.value.expressMainOrder
      }
    ])

    ElMessage.success('添加成功')
    // 清空表单
    carrierForm.value.carrier = ''
    carrierForm.value.expressMainOrder = ''
    emit('update')
    dialogVisible.value = false
  } catch (error) {
    console.error('添加承运商主单号失败:', error)
    ElMessage.error('添加失败')
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="拣货"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="mb-16 flex items-center gap-8">
      <el-input v-model="form.length" placeholder="长" style="width: 80px" />
      <el-input v-model="form.width" placeholder="宽" style="width: 80px" />
      <el-input v-model="form.height" placeholder="高" style="width: 80px" />
      <el-input v-model="form.weight" placeholder="重量" style="width: 100px" />
      <el-input
        v-model="form.boxCount"
        placeholder="箱数"
        style="width: 100px"
      />
      <el-button type="primary" @click="handleFill">填充</el-button>
      <div>
        <el-button type="primary" plain @click="handleFillServiceProvider"
          >填充服务商数据</el-button
        >
      </div>
      <el-button type="primary" plain @click="handleFillCustomer"
        >填充客户数据</el-button
      >
    </div>
    <div class="mb-16 flex items-center gap-8">
      <el-input v-model="boxCount" placeholder="箱数" style="width: 80px" />
      <el-button type="primary" plain @click="handleAddBox">增加箱数</el-button>
      <div>
        <el-button type="primary" plain @click="handleClearAll"
          >清除数据</el-button
        >
      </div>
      <el-input
        v-model="carrierForm.carrier"
        placeholder="承运商"
        style="width: 80px"
      />
      <el-input
        v-model="carrierForm.expressMainOrder"
        placeholder="主单号"
        style="width: 80px"
      />
      <el-button type="primary" plain @click="handleAddCarrierMainOrder"
        >添加承运商主单号</el-button
      >
    </div>
    <el-table :data="tableData" border style="width: 100%; height: 400px">
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column label="系统箱号" prop="boxNo" width="180">
        <template #default="{ row }">
          <template v-if="row.isNew">
            <el-input v-model="row.boxNo" clearable placeholder="请输入" />
          </template>
          <template v-else>
            {{ row.boxNo }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="长(cm)" width="100">
        <template #default="{ row }">
          <el-input
            v-model="row.length"
            placeholder="长"
            @change="handleCalculate(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="宽(cm)" width="100">
        <template #default="{ row }">
          <el-input
            v-model="row.width"
            placeholder="宽"
            @change="handleCalculate(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="高(cm)" width="100">
        <template #default="{ row }">
          <el-input
            v-model="row.height"
            placeholder="高"
            @change="handleCalculate(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="重量(kg)" width="100">
        <template #default="{ row }">
          <el-input
            v-model="row.weight"
            placeholder="重量"
            @change="handleCalculate(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="材积重" prop="materialWeight" width="100">
        <!-- <template #default="{ row }">
          <el-input v-model="row.volumeWeight" />
        </template> -->
      </el-table-column>
      <el-table-column label="收费重" prop="heavyCharges" width="100">
        <!-- <template #default="{ row }">
          <el-input v-model="row.chargeWeight" />
        </template> -->
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row, $index }">
          <el-button type="primary" link @click="handleClearDimensions($index)"
            >清空</el-button
          >
          <el-button
            v-if="row.isNew"
            type="danger"
            link
            @click="handleRemoveRow($index)"
            >移除</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="承运商">
        <template #default="{ row }">
          <el-input v-model="row.carrier" clearable placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="单号">
        <template #default="{ row }">
          <el-input
            v-model="row.waybillNumber"
            clearable
            placeholder="请输入"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template #default="{ $index }">
          <el-button type="primary" link @click="handleClearCarrier($index)"
            >清空</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="flex justify-between mt-16">
      <el-button @click="handleBatchImport">批量导入</el-button>
      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.mb-16 {
  margin-bottom: 16px;
}
.mt-16 {
  margin-top: 16px;
}
</style>
