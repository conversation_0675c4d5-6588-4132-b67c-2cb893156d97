<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { FirstVesselTrajectoryVo } from '@/api/firstLeg/types'
import { getFirstVesselThirdTrajectoryList } from '@/api/firstLeg'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ThirdPartyTrajectory'
})

const props = defineProps<{
  id?: string
}>()

const loading = ref(false)
const thirdTrajectoryList = ref<FirstVesselTrajectoryVo[]>([])

// 格式化轨迹列表为timeline格式
const formatTrajectory = (list: FirstVesselTrajectoryVo[]) => {
  return list.map(item => ({
    ...item,
    content: item.trackMessage || '',
    timestamp: item.createTime || ''
  }))
}

// 第三方轨迹列表
const activities = computed(() => {
  return formatTrajectory(thirdTrajectoryList.value)
})

/**
 * 获取第三方轨迹列表
 */
const getThirdTrajectory = async () => {
  if (!props.id) return

  try {
    loading.value = true
    const { success, result } = await getFirstVesselThirdTrajectoryList({
      id: String(props.id)
    })
    if (success && result) {
      thirdTrajectoryList.value = result
    }
  } catch (error) {
    console.error('获取第三方轨迹列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 更新第三方轨迹
 */
const handleUpdateThirdTrajectory = async () => {
  await getThirdTrajectory()
  ElMessage.success('轨迹更新成功')
}

// 初始化数据
onMounted(() => {
  if (props.id) {
    getThirdTrajectory()
  }
})
</script>

<template>
  <ContentWrap title="服务商运输轨迹" class="h-50%">
    <template #header>
      <div class="w-full flex justify-end">
        <el-button size="small" @click="handleUpdateThirdTrajectory"
          >更新轨迹</el-button
        >
        <el-button size="small" type="primary">全选</el-button>
        <el-button size="small" type="primary">复制轨迹</el-button>
      </div>
    </template>
    <el-timeline v-loading="loading">
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :timestamp="activity.timestamp"
        placement="top"
        type="primary"
      >
        <div class="flex items-center justify-between">
          <div>{{ activity.content }}</div>
          <div class="flex">
            <div class="i-ep:edit"></div>
            <div class="i-ep:delete ml-8"></div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </ContentWrap>
</template>
