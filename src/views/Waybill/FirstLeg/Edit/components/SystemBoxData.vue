<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getSystemBoxList as getSystemBoxListApi,
  batchUpdateFirstVesselAndUseServiceProviderMeasureData
} from '@/api/firstLeg'
import { ElMessage } from 'element-plus'
import PickBoxDialog from './PickBoxDialog.vue'

defineOptions({
  name: 'SystemBoxData'
})

const props = defineProps<{
  id?: string
}>()

const emit = defineEmits(['refresh-detail'])

const loading = ref(false)
const tableData = ref([])
const pickBoxDialogVisible = ref(false)

/**
 * 获取系统收货数据列表
 */
const getSystemBoxList = async () => {
  if (!props.id) return

  try {
    loading.value = true
    const { success, result } = await getSystemBoxListApi({
      id: String(props.id)
    })
    if (success && result) {
      tableData.value = result
    }
  } catch (error) {
    console.error('获取系统收货数据失败:', error)
    ElMessage.error('获取系统收货数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 复制服务商数据
 */
const handleCopyServiceProviderMeasureData = async () => {
  if (!props.id) return

  try {
    loading.value = true
    await batchUpdateFirstVesselAndUseServiceProviderMeasureData({
      id: String(props.id)
    })
    ElMessage.success('数据更新成功')
    await getSystemBoxList()
  } catch (error) {
    console.error('复制服务商数据失败:', error)
    ElMessage.error('复制服务商数据失败')
  } finally {
    loading.value = false
  }
}

const handlePickBox = () => {
  pickBoxDialogVisible.value = true
}

/**
 * 处理拣货完成后的更新
 */
const handlePickBoxUpdate = async () => {
  // 先刷新系统箱数据
  await getSystemBoxList()
  // 然后刷新详情数据
  emit('refresh-detail')
}

// 初始化数据
onMounted(() => {
  if (props.id) {
    getSystemBoxList()
  }
})
</script>

<template>
  <ContentWrap title="系统收货数据">
    <template #header>
      <div class="w-full flex justify-end">
        <el-button
          size="small"
          type="primary"
          plain
          @click="handleCopyServiceProviderMeasureData"
          >复制服务商数据</el-button
        >
        <el-button size="small" type="primary" @click="handlePickBox"
          >拣货</el-button
        >
      </div>
    </template>
    <el-table
      :data="tableData"
      :border="true"
      style="width: 100%; height: 400px"
      v-loading="loading"
    >
      <el-table-column label="系统箱号" prop="boxNo" width="240" />
      <el-table-column label="客户数据">
        <template #default="{ row }">
          <div>
            {{ row.systemBoxWeight ? `${row.systemBoxWeight}kg` : '-' }}
          </div>
          <div>
            {{
              row.systemBoxLength && row.systemBoxWidth && row.systemBoxHeight
                ? `${row.systemBoxLength}*${row.systemBoxWidth}*${row.systemBoxHeight}cm`
                : '-'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="拣货数据">
        <template #default="{ row }">
          <div>
            {{ row.pickBoxWeight ? `${row.pickBoxWeight}kg` : '-' }}
          </div>
          <div>
            {{
              row.pickBoxLength && row.pickBoxWidth && row.pickBoxHeight
                ? `${row.pickBoxLength}*${row.pickBoxWidth}*${row.pickBoxHeight}cm`
                : '-'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #header>
          <div>快递承运商</div>
          <div>单号</div>
        </template>
        <template #default="{ row }">
          <div>
            {{ row.carrier ?? '-' }}
          </div>
          <div>
            {{ row.expressMainOrder ?? '-' }}
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 拣货弹窗集成 -->
    <PickBoxDialog
      v-model="pickBoxDialogVisible"
      :box-list="tableData"
      :id="id"
      @update="handlePickBoxUpdate"
    />
  </ContentWrap>
</template>
