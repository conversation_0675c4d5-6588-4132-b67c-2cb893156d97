<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'
import FormEntity from '../components/FormEntity.vue'
import SkuTable from '../components/SkuTable.vue'
import ThirdPartyTrajectory from './components/ThirdPartyTrajectory.vue'
import SystemTrajectory from './components/SystemTrajectory.vue'
import ThirdPartyBoxData from './components/ThirdPartyBoxData.vue'
import SystemBoxData from './components/SystemBoxData.vue'
import {
  getFirstVesselById,
  updateFirstVessel,
  saveFirstVessel
} from '@/api/firstLeg'
import type { FirstVesselVo } from '@/api/firstLeg/types'

defineOptions({
  name: 'FirstLegEdit'
})

const route = useRoute()
const router = useRouter()
const { id } = route.query

const destination_type = useDictItem('destination_type')
const declaration_method = useDictItem('declaration_method')
const taxation_method = useDictItem('taxation_method')
const storage_type = useDictItem('storage_type')
const purchase_insurance = useDictItem('purchase_insurance')
const container_type = useDictItem('container_type')
const country = useDictItem('country')

provide('destination_type', destination_type)
provide('declaration_method', declaration_method)
provide('taxation_method', taxation_method)
provide('storage_type', storage_type)
provide('purchase_insurance', purchase_insurance)
provide('container_type', container_type)
provide('country', country)

// 表格引用
const formEntityRef = ref()
const skuTableRef = ref()

// 初始化数据
const vesselDetail = ref<FirstVesselVo>()
const loading = ref(false)

/**
 * 获取头程详细信息
 */
const getVesselDetail = async () => {
  if (!id) return

  try {
    loading.value = true
    // 获取头程运单详情
    const { success, result } = await getFirstVesselById({ id: String(id) })
    if (success && result) {
      vesselDetail.value = result
      // 回写数据到表单组件
      if (formEntityRef.value?.formData) {
        // 将FirstVesselVo数据转换为FirstVesselEntity格式
        Object.assign(formEntityRef.value.formData, {
          id: result.id,
          customer: result.customer,
          customerWaybillNumber: result.customerWaybillNumber,
          deliveryWarehouse: result.deliveryWarehouse,
          destinationType: result.destinationType,
          shippingService: result.shippingService,
          destination: result.destination,
          declarationMethod: result.declarationMethod,
          taxationMethod: result.taxationMethod,
          storageType: result.storageType,
          purchaseInsurance: result.purchaseInsurance,
          customerRemarks: result.customerRemarks,
          internalRemarks: result.internalRemarks,
          vatNo: result.vatNo,
          vatAddress: result.vatAddress,
          eoriNo: result.eoriNo,
          eoriAddress: result.eoriAddress,
          containerType: result.containerType,
          recipient: result.recipient,
          addressOne: result.addressOne,
          addressTwo: result.addressTwo,
          city: result.city,
          state: result.state,
          zipCode: result.zipCode,
          country: result.country,
          phone: result.phone,
          email: result.email
        })
      }

      // 回写数据到商品表格组件
      if (
        skuTableRef.value?.tableData &&
        result.boxInfo &&
        result.boxInfo.length > 0
      ) {
        skuTableRef.value.tableData = result.boxInfo.map(item => ({
          ...item,
          commodity:
            item.commodity?.map(skuItem => ({
              ...skuItem,
              productSkuCode: skuItem.sku,
              productName: skuItem.commodityName,
              productAttrOne: skuItem.productAttrOne,
              productAttrTwo: skuItem.productAttrTwo
            })) || []
        }))
      }
    }
  } catch (error) {
    console.error('获取头程运单详情失败:', error)
    ElMessage.error('获取头程运单详情失败')
  } finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(async () => {
  if (id) {
    await getVesselDetail()
  }
})

provide('detail', vesselDetail)

/**
 * 保存运单
 */
const handleSave = async () => {
  if (!formEntityRef.value?.formData) {
    ElMessage.error('表单数据不完整')
    return
  }

  try {
    loading.value = true

    // 获取表单数据
    const formData = formEntityRef.value.formData

    // 获取商品表格数据
    if (skuTableRef.value?.tableData) {
      formData.boxInfo = skuTableRef.value.tableData.map((item: any) => {
        return {
          ...item,
          boxId: item.id
        }
      })
    }

    // 调用保存或更新API
    const api = id ? updateFirstVessel : saveFirstVessel
    const { success, message } = await api(formData)

    if (success) {
      ElMessage.success(id ? '更新成功' : '创建成功')
      // 跳转到列表页面
      router.push('/waybill/firstLeg')
    } else {
      ElMessage.error(message || (id ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('保存运单失败:', error)
    ElMessage.error(id ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  router.push('/waybill/firstLeg')
}
</script>

<template>
  <div class="w-full">
    <el-row :gutter="20" class="w-full">
      <el-col :span="16">
        <ContentWrap :title="id ? '编辑运单' : '创建运单'" class="h-full">
          <div v-loading="loading">
            <FormEntity ref="formEntityRef"></FormEntity>
          </div>
        </ContentWrap>
      </el-col>
      <el-col :span="8">
        <div class="flex flex-col gap-20">
          <ThirdPartyTrajectory :id="String(id)" />
          <SystemTrajectory :id="String(id)" />
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20 w-full">
      <el-col :span="12">
        <ThirdPartyBoxData :id="String(id)" />
      </el-col>
      <el-col :span="12">
        <SystemBoxData :id="String(id)" @refresh-detail="getVesselDetail" />
      </el-col>
    </el-row>

    <ContentWrap class="mt-20">
      <SkuTable ref="skuTableRef"></SkuTable>
    </ContentWrap>

    <ContentWrap class="mt-20">
      <div class="h-60 flex items-center justify-center">
        <el-button type="primary" @click="handleSave" :loading="loading"
          >保存</el-button
        >
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </ContentWrap>
  </div>
</template>

<style lang="scss" scoped></style>
