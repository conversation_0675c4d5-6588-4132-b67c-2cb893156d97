<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import ImportWaybill from './components/ImportWaybill.vue'
import PushWaybill from './components/PushWaybill.vue'
import EditMainOrder from './components/EditMainOrder.vue'
import AddTrajectory from './components/AddTrajectory.vue'
import ImportTrajectory from './components/ImportTrajectory.vue'
import WaybillStatus from './components/WaybillStatus.vue'
import CostCheck from './components/CostCheck.vue'
import EditCost from './components/EditCost.vue'
import RemarkDialog from './components/RemarkDialog.vue'
import ViewReceivingData from './components/ViewReceivingData.vue'
import CountWaybill from './components/CountWaybill.vue'
import { useToggle } from '@/hooks/useToggle'
import {
  getFirstVesselList,
  batchUpdateAndUseServiceProviderTrack,
  batchUpdateAndUseServiceProviderExpressMainOrderNumber,
  batchRealTimeSyncServiceProviderNewTrack,
  // batchPushFirstVessel,
  batchPushFirstVesselAgain,
  // batchImportTrack,
  // batchEditExpressMainOrderNumber,
  batchEditCost,
  batchDeleteInternalRemarks,
  batchDeleteCustomerRemarks,
  batchCancelFirstVessel,
  batchAdjustFirstVesselStatus,
  // batchAddTrack,
  // batchAddRemarks,
  batchUpdateFirstVessel,
  batchUpdateFirstVesselAndUseServiceProviderMeasureData,
  batchUpdateServiceProviderMeasureData
} from '@/api/firstLeg'
import type { FirstVesselEntity } from '@/api/firstLeg/types'
import { useTable } from '@/hooks/useTable'
import { useOptions } from '@/hooks/useOptions'
import { useDictItem } from '@/hooks/useDictItem'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'FirstLeg'
})

const { sellerOptions } = useOptions(['sellerOptions'])
const { serviceProviderOptions } = useOptions(['serviceProviderOptions'])

provide('serviceProviderOptions', serviceProviderOptions)

const country = useDictItem('country')
const timer = ref<any[]>([])
const {
  // viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<FirstVesselEntity>({
  immediate: true,
  initialFormData: {
    status: '',
    customerWaybillNumber: undefined,
    customer: undefined,
    shippingService: undefined,
    destination: undefined,
    destinationType: undefined,
    taxationMethod: undefined,
    storageType: undefined,
    purchaseInsurance: undefined,
    type: 'createTime'
  },
  fetchDataApi: async () => {
    const [startTime, endTime] = timer.value
    const res = await getFirstVesselList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData,
      startTime: startTime ? startTime : undefined,
      endTime: endTime ? endTime : undefined
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const handleReset = () => {
  timer.value = []
  tableMethods.handleReset()
}

const router = useRouter()

const handleCreate = () => {
  // router.push({ path: '/order/firstLegView' })
  router.push({ path: '/waybill/firstLeg/create' })
}

const handleEdit = (row: any) => {
  // router.push({ path: '/order/firstLegView' })
  router.push({ path: '/waybill/firstLeg/edit', query: { id: row.id } })
}

/**
 * 验证是否有选中项
 */
const validateSelection = () => {
  if (!selection.value || selection.value.length === 0) {
    ElMessage.warning('请至少选择一条数据')
    return false
  }
  return true
}

/**
 * 处理API请求
 */
const handleApiRequest = async (
  apiFunc: (data: FirstVesselEntity) => Promise<IResponse<any>>,
  successMsg: string = '操作成功'
) => {
  if (!validateSelection()) return

  try {
    const data = {
      ids: selection.value.map(item => item.id).filter(Boolean) as string[]
    } as unknown as FirstVesselEntity

    await apiFunc(data)
    ElMessage.success(successMsg)
    // 刷新表格数据
    tableMethods.getList()
  } catch (error) {
    console.error(error)
    ElMessage.error('操作失败')
  }
}

/**
 * 处理单行数据的API请求
 */
const handleSingleRowRequest = (
  row: any,
  apiFunc: (data: FirstVesselEntity) => Promise<IResponse<any>>,
  successMsg: string = '操作成功'
) => {
  try {
    // 创建仅包含当前行ID的数据对象
    const data = {
      ids: [row.id].filter(Boolean) as string[]
    } as unknown as FirstVesselEntity

    apiFunc(data)
      .then(() => {
        ElMessage.success(successMsg)
        tableMethods.getList()
      })
      .catch(() => {
        ElMessage.error('操作失败')
      })
  } catch (error) {
    console.error(error)
    ElMessage.error('操作失败')
  }
}

const [advisible, handleAD] = useToggle()

const [iwvisible, handleIW] = useToggle()

const [pwvisible, handlePW] = useToggle()

const handlePushWaybill = () => {
  if (!selection.value || selection.value.length === 0) {
    ElMessage.warning('请至少选择一条运单')
    return
  }
  handlePW()
}

// 再次推送运单
const handlePushAgain = () => {
  handleApiRequest(batchPushFirstVesselAgain, '再次推送运单成功')
}

// 取消服务商运单
const handleCancelServiceVessel = (row?: any) => {
  const confirmAction = () => {
    if (row) {
      handleSingleRowRequest(row, batchCancelFirstVessel, '取消服务商运单成功')
    } else {
      handleApiRequest(batchCancelFirstVessel, '取消服务商运单成功')
    }
  }

  ElMessageBox.confirm('确定要取消选中的服务商运单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(confirmAction)
}

// 更新运单
const handleUpdateVessel = () => {
  handleApiRequest(batchUpdateFirstVessel, '更新运单成功')
}

// 更新并使用服务商测量数据
const handleUpdateAndUseMeasureData = () => {
  handleApiRequest(
    batchUpdateFirstVesselAndUseServiceProviderMeasureData,
    '更新并使用服务商测量数据成功'
  )
}

// 更新服务商测量数据
const handleUpdateMeasureData = () => {
  handleApiRequest(
    batchUpdateServiceProviderMeasureData,
    '更新服务商测量数据成功'
  )
}

const [emovisible, handleEMO] = useToggle()

const emoRow = ref<FirstVesselEntity[]>()
const handleEMOAfter = (row?: FirstVesselEntity) => {
  if (row) {
    emoRow.value = [row]
  } else {
    if (selection.value.length === 0) {
      ElMessage.warning('请选择运单')
      return
    }
    emoRow.value = selection.value
  }
  handleEMO()
}

// 更新并使用服务商快递主单号
const handleUpdateAndUseExpressMainOrder = () => {
  handleApiRequest(
    batchUpdateAndUseServiceProviderExpressMainOrderNumber,
    '更新并使用服务商快递主单号成功'
  )
}

const [atvisible, handleAT] = useToggle()

// 添加轨迹
const handleAddTrack = () => {
  if (selection.value.length === 0) {
    ElMessage.warning('请选择运单')
    return
  }
  handleAT()
}

const [itvisible, handleIT] = useToggle()

// 实时同步服务商新轨迹
const handleSyncNewTrack = () => {
  handleApiRequest(
    batchRealTimeSyncServiceProviderNewTrack,
    '实时同步服务商新轨迹成功'
  )
}

// 更新并使用服务商轨迹
const handleUpdateAndUseTrack = () => {
  handleApiRequest(
    batchUpdateAndUseServiceProviderTrack,
    '更新并使用服务商轨迹成功'
  )
}

const [wsvisible, handleWS] = useToggle()
const wsStatus = ref<1 | 2 | 3 | 4 | 5>(1)

// 最终执行状态更新的API调用
const executeStatusUpdate = async (status: string) => {
  if (!validateSelection()) return
  try {
    await batchAdjustFirstVesselStatus({
      ids: selection.value.map(item => item.id).filter(Boolean) as string[],
      status
    })
    ElMessage.success('操作成功')
    tableMethods.getList()
  } catch (error) {
    console.error(error)
    ElMessage.error('操作失败')
  }
}

// 弹窗确认后，调用接口
const handleDialogConfirm = async (type?: number) => {
  if (!validateSelection()) return
  if (type === 4) {
    await batchCancelFirstVessel({
      ids: selection.value.map(item => item.id).filter(Boolean) as string[]
    })
    ElMessage.success('操作成功')
    tableMethods.getList()
  } else {
    const statusMap: { [key: number]: string } = {
      1: 'Received', // 虽然文档没说，但根据旧逻辑保留对已收货的支持
      2: 'InTransit',
      3: 'Signed',
      5: 'ReturnStep'
    }
    const status = statusMap[type!]
    if (status) {
      await executeStatusUpdate(status) // executeStatusUpdate内部已经会调用tableMethods.getList()
    }
  }
}

// 点击取消运单
const handleCancel = () => {
  if (!validateSelection()) return
  ElMessageBox.confirm('确认要取消运单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      executeStatusUpdate('Cancelled')
    })
    .catch(() => {
      // 用户点击了取消
    })
}

// 点击【转运中】、【已签收】、【退件】按钮时的处理逻辑
const handleStatusChange = (status: 'InTransit' | 'Signed' | 'ReturnStep') => {
  if (!validateSelection()) return

  // 检查是否有退件或已取消状态的数据
  const hasInvalidStatus = selection.value.some(item =>
    ['ReturnStep', 'Cancelled'].includes(item.status as string)
  )

  if (hasInvalidStatus) {
    // 如果有无效状态，直接打开弹窗，无需确认
    const typeMap: { [key: string]: 1 | 2 | 3 | 4 | 5 } = {
      InTransit: 2,
      Signed: 3,
      ReturnStep: 5
    }
    // 根据文档，打开弹窗, type=2, 但为了区分是哪个按钮触发的，这里也传入对应的类型
    // WaybillStatus组件内部只对type=2做了特殊处理，所以不影响
    wsStatus.value = typeMap[status]
    handleWS()
  } else {
    // 没有无效状态时，显示确认框
    ElMessageBox.confirm('确认要变更状态吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
      .then(() => {
        executeStatusUpdate(status)
      })
      .catch(() => {
        // 用户点击了取消
      })
  }
}

// [ccvisible] 需要先校验是否可添加费用
const [ccvisible, handleCC] = useToggle()

const [ecvisible, handleEC] = useToggle()

// 删除费用
const handleDeleteCost = () => {
  if (!validateSelection()) return

  ElMessageBox.confirm('确定要删除选中运单的费用吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const data = {
      ids: selection.value.map(item => item.id).filter(Boolean) as string[],
      operation: 'delete'
    } as unknown as FirstVesselEntity

    batchEditCost(data)
      .then(() => {
        ElMessage.success('删除费用成功')
        tableMethods.getList()
      })
      .catch(() => {
        ElMessage.error('删除费用失败')
      })
  })
}

const [rmdvisible, handleRmd] = useToggle()

// 删除客户备注
const handleDeleteCustomerRemark = () => {
  handleApiRequest(batchDeleteCustomerRemarks, '删除客户备注成功')
}

// 删除内部备注
const handleDeleteInternalRemark = () => {
  handleApiRequest(batchDeleteInternalRemarks, '删除内部备注成功')
}

const [vrdvisible, handleVrd] = useToggle()

const viewReceivingDataRow = ref<FirstVesselEntity[]>([])
// 查看收货数据
const handleViewReceivingData = (row?: FirstVesselEntity) => {
  if (row) {
    viewReceivingDataRow.value = [row]
  } else {
    if (selection.value.length > 0) {
      viewReceivingDataRow.value = selection.value
    } else {
      ElMessage.warning('请选择运单')
      return
    }
  }
  handleVrd()
}

const [cwvisible, handleCw] = useToggle()

const cwRow = ref<FirstVesselEntity[]>([])
// 查看统计数据
const handleCwAfter = (row?: FirstVesselEntity) => {
  if (row) {
    cwRow.value = [row]
  } else {
    if (selection.value.length > 0) {
      cwRow.value = selection.value
    } else {
      ElMessage.warning('请选择运单')
      return
    }
  }
  handleCw()
}

// 控制服务商列显示/隐藏
const showServiceProviderColumn = ref(true)

// 切换服务商列显示状态
const toggleServiceProviderColumn = () => {
  showServiceProviderColumn.value = !showServiceProviderColumn.value
}

// 计算服务商列宽度
const serviceProviderColumnWidth = computed(() => {
  return showServiceProviderColumn.value ? 180 : 50
})

const options = [
  {
    label: '已下单',
    value: 'Ordered'
  },
  {
    label: '已收货',
    value: 'Received'
  },
  {
    label: '转运中',
    value: 'InTransit'
  },
  {
    label: '已签收',
    value: 'Signed'
  },
  {
    label: '退件',
    value: 'ReturnStep'
  },
  {
    label: '申请取消',
    value: 'ApplyCancel'
  },
  {
    label: '已取消',
    value: 'Cancelled'
  },
  {
    label: '待付款',
    value: 'Obligation'
  },
  {
    label: '全部',
    value: ''
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="formData.status" @tab-click="tableMethods.handleQuery">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="left"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="运单号" prop="">
            <el-input
              v-model.trim="formData.customerWaybillNumber"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.customer"
              :options="sellerOptions"
              placeholder="请选择"
              :fields="{
                label: 'companyName',
                value: 'companyCode'
              }"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="服务商" prop="">
            <el-cascader
              v-model="formData.shippingService"
              :options="serviceProviderOptions"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                emitPath: false
              }"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="运输服务" prop="">
            <el-select v-model="formData.destination" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <el-select
              v-model="formData.destinationType"
              clearable
              placeholder=""
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="目的地" prop="">
            <el-select
              v-model="formData.destinationType"
              clearable
              placeholder=""
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="国家" prop="">
            <DSelect
              v-model="formData.country"
              :options="country"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <div class="flex w-full">
            <DSelect
              v-model="formData.type"
              :options="[
                { itemValue: 'createTime', itemText: '创建时间' },
                { itemValue: 'Received', itemText: '收货时间' },
                { itemValue: 'Signed', itemText: '签收时间' }
              ]"
              placeholder="请选择"
              class="!w-120"
            ></DSelect>

            <el-date-picker
              v-model="timer"
              type="daterange"
              placeholder="请选择"
              class="!w-full flex-1"
            />
          </div>
        </el-col>

        <el-col :span="6">
          <el-form-item label="海外仓上架状态" prop="">
            <el-select v-model="formData.shelfStatus" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <!-- class="pt-30" -->
          <el-form-item label="" prop="">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button @click="handleReset">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div>
      <el-button type="primary" class="mb-18" @click="handleCreate"
        >创建运单</el-button
      >
      <el-dropdown>
        <el-button class="ml-8 mb-18">
          导入/导出运单
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleIW">导入运单</el-dropdown-item>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button class="ml-8 mb-18" @click="() => handleAD()"
        >生成海外仓入库单</el-button
      >
      <el-dropdown>
        <el-button class="ml-8 mb-18">
          下载标签
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>下载标签</el-dropdown-item>
            <el-dropdown-item>打印标签</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          下载入仓单
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>下载入仓单</el-dropdown-item>
            <el-dropdown-item>打印入仓单</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          服务商互联
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handlePushWaybill"
              >推送运单</el-dropdown-item
            >
            <el-dropdown-item @click="handlePushAgain"
              >再次推送</el-dropdown-item
            >
            <el-dropdown-item @click="handleCancelServiceVessel()"
              >取消服务商运单</el-dropdown-item
            >
            <el-dropdown-item @click="handleUpdateVessel"
              >更新运单</el-dropdown-item
            >
            <el-dropdown-item @click="handleUpdateAndUseMeasureData"
              >更新并使用服务商测量数据</el-dropdown-item
            >
            <el-dropdown-item @click="handleUpdateMeasureData"
              >更新服务商测量数据</el-dropdown-item
            >
            <el-dropdown-item @click="handleEMOAfter()"
              >编辑快递主单号</el-dropdown-item
            >
            <el-dropdown-item @click="handleUpdateAndUseExpressMainOrder"
              >更新并使用服务商快递主单号</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          操作运输轨迹
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleSyncNewTrack"
              >实时同步服务商新轨迹</el-dropdown-item
            >
            <el-dropdown-item @click="handleUpdateAndUseTrack"
              >更新并使用服务商轨迹</el-dropdown-item
            >
            <el-dropdown-item @click="handleAddTrack()"
              >添加轨迹</el-dropdown-item
            >
            <el-dropdown-item @click="handleIT()">导入轨迹</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          调整运单状态
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleStatusChange('InTransit')"
              >转运中</el-dropdown-item
            >
            <el-dropdown-item @click="handleStatusChange('Signed')"
              >已签收</el-dropdown-item
            >
            <el-dropdown-item @click="handleStatusChange('ReturnStep')"
              >退件</el-dropdown-item
            >
            <el-dropdown-item @click="handleCancel">取消运单</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          编辑费用
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- 添加费用前需先调用 handleCC 校验运单是否可添加费用 -->
            <el-dropdown-item @click="handleCC">添加费用</el-dropdown-item>
            <el-dropdown-item @click="handleDeleteCost"
              >删除费用</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-8 mb-18">
          其他
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleRmd()">添加备注</el-dropdown-item>
            <el-dropdown-item @click="handleDeleteCustomerRemark"
              >删除客户备注</el-dropdown-item
            >
            <el-dropdown-item @click="handleDeleteInternalRemark"
              >删除内部备注</el-dropdown-item
            >
            <el-dropdown-item @click="handleViewReceivingData()"
              >查看收货数据</el-dropdown-item
            >
            <el-dropdown-item @click="handleCwAfter()"
              >统计数据</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      row-key="id"
      :border="true"
      class="w-full"
      @selection-change="tableMethods.handleSelectionChange"
    >
      <el-table-column type="selection" fixed="left" width="45" />
      <el-table-column prop="date" width="180">
        <template #header>
          <div>运单号</div>
          <div>客户运单号</div>
        </template>
        <template #default="{ row }">
          <div>{{ row.systemWaybillNumber }}</div>
          <div>{{ row.customerWaybillNumber }}</div>
        </template>
      </el-table-column>
      <el-table-column label="客户" prop="customer" width="130" />
      <el-table-column
        label="服务商"
        prop="serviceProvider"
        :width="serviceProviderColumnWidth"
      >
        <template #header>
          <div class="flex items-center">
            <div
              :class="
                showServiceProviderColumn ? 'i-ep:remove' : 'i-ep:circle-plus'
              "
              class="mr-5 cursor-pointer"
              @click="toggleServiceProviderColumn"
            ></div>
            <div v-if="showServiceProviderColumn">服务商</div>
          </div>
        </template>
        <template #default="{ row }">
          <div v-if="showServiceProviderColumn">
            <div>{{ row.serviceProvider }}</div>
            <div>{{ row.serviceProviderTransportChannelName }}</div>
            <div>{{ row.shipmentId }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="运输服务"
        prop="shippingService"
        min-width="100"
      />
      <el-table-column label="交货仓库" prop="deliveryWarehouse" width="110" />
      <el-table-column label="数量" width="120">
        <template #default="{ row }">
          <div>
            {{ `${row?.sysBoxNum}件` }}
            {{ `/ ${row.actualBoxNum || 0}件` }}
          </div>
          <div>
            {{ row.commodityNum != null ? `${row.commodityNum}个` : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实重" prop="actualWeight" width="100">
        <template #default="{ row }">
          <div>{{ row.actualWeight + 'kg' }}</div>
        </template>
      </el-table-column>
      <el-table-column width="120">
        <template #header>
          <div>体积</div>
          <div>体积重</div>
        </template>
        <template #default="{ row }">
          <div>{{ row.dimensional + 'm³' }}</div>
          <div>{{ row.volumeWeight + 'kg' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="收费重" prop="changeableWeight" width="100">
        <template #default="{ row }">
          <div>{{ row.changeableWeight + 'kg' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="费用" prop="cost" width="100" />
      <el-table-column label="目的地" prop="destination" width="100" />
      <el-table-column label="运输轨迹" prop="track" width="180" />
      <el-table-column label="快递承运商" prop="carrier" width="150" />
      <el-table-column label="国家" prop="country" width="100" />
      <el-table-column label="主品名" prop="commodityName" width="150" />
      <el-table-column label="物品属性" prop="commodityAttribute" width="130" />
      <el-table-column label="备注" width="180">
        <template #default="{ row }">
          <div>客户备注：{{ row.customerRemarks }}</div>
          <div>内部备注：{{ row.internalRemarks }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="statusName" width="100" />
      <el-table-column label="时间" width="180">
        <template #default="{ row }">
          <div>{{ row.createTime }}</div>
          <div>{{ row.statusTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-dropdown>
            <el-button>
              <span>操作</span>
              <div class="i-ep:arrow-down ml-5"></div>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleEdit(row)"
                  >编辑运单</el-dropdown-item
                >
                <el-dropdown-item>下载标签</el-dropdown-item>
                <el-dropdown-item>下载入仓单</el-dropdown-item>
                <el-dropdown-item @click="() => handleCancelServiceVessel(row)"
                  >取消运单</el-dropdown-item
                >
                <el-dropdown-item @click="handleViewReceivingData(row)"
                  >查看收货数据</el-dropdown-item
                >
                <el-dropdown-item @click="handleAD()"
                  >创建海外仓入库单</el-dropdown-item
                >
                <el-dropdown-item @click="handleEMOAfter(row)"
                  >编辑快递主单号</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog v-model="advisible"></CreateEditDialog>
  <!-- 导入运单 -->
  <ImportWaybill v-model="iwvisible"></ImportWaybill>
  <!-- 推送运单 -->
  <PushWaybill
    v-model="pwvisible"
    :selection="selection"
    @refresh="tableMethods.getList"
  ></PushWaybill>
  <!-- 编辑快递主单号 -->
  <EditMainOrder
    v-model="emovisible"
    :selection="emoRow"
    @refresh="tableMethods.getList"
  ></EditMainOrder>
  <!-- 添加轨迹 -->
  <AddTrajectory
    v-model="atvisible"
    :selection="selection"
    @success="tableMethods.getList"
  ></AddTrajectory>
  <!-- 导入轨迹 -->
  <ImportTrajectory v-model="itvisible"></ImportTrajectory>
  <!-- 运单状态 -->
  <WaybillStatus
    v-if="wsvisible"
    v-model="wsvisible"
    :type="wsStatus"
    :selection="selection"
    @update="handleDialogConfirm"
  ></WaybillStatus>
  <!-- 编辑运费之前的校验 -->
  <CostCheck v-model="ccvisible" @pass="handleEC"></CostCheck>
  <!-- 编辑费用 -->
  <EditCost v-model="ecvisible"></EditCost>
  <!-- 备注 -->
  <RemarkDialog
    v-model="rmdvisible"
    :selection="selection"
    @refresh="tableMethods.getList"
  ></RemarkDialog>
  <!-- 查看收货数据 -->
  <ViewReceivingData
    v-model="vrdvisible"
    :selection="viewReceivingDataRow"
  ></ViewReceivingData>
  <!-- 统计 -->
  <CountWaybill
    v-model="cwvisible"
    :selection="cwRow"
    @refresh="tableMethods.getList"
  ></CountWaybill>
  <!-- <DownloadTag v-model="dtvisible"></DownloadTag>
  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog> -->
</template>

<style lang="scss" scoped></style>
