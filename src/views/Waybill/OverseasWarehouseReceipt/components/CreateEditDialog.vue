<script setup lang="ts">
import {
  getFirstVesselSettingById,
  saveFirstVesselSetting,
  updateFirstVesselSetting
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const warehouse_type = inject('warehouse_type') as DictItemEntity[]
const weight_mode = inject('weight_mode') as DictItemEntity[]
const charge_mode = inject('charge_mode') as DictItemEntity[]

const destination_type = useDictItem('destination_type')
const round_mode = useDictItem('round_mode')
const charge_weight_mode = useDictItem('charge_weight_mode')

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<FirstVesselSettingEntity>(
    {
      id: undefined,
      transportServiceName: undefined,
      transportServiceCode: undefined,
      chargeMode: undefined,
      weightMode: undefined,
      roundMode: undefined,
      bubbleCoefficient: undefined,
      chargeWeightMode: undefined,
      ticketWeightPrecision: undefined,
      boxWeightPrecision: undefined,
      sizePrecision: undefined,
      minBoxRealWeight: undefined,
      minBoxMaterialWeight: undefined,
      minBoxChargeWeight: undefined,
      minTicketChargeWeight: undefined,
      arrivalCountry: undefined,
      customer: undefined,
      destinationType: undefined,
      status: undefined,
      ids: undefined
    },
    {
      transportServiceName: [
        { required: true, message: '请输入运输服务名称', trigger: 'blur' }
      ],
      arrivalCountry: [
        { required: true, message: '请选择到达国家', trigger: 'change' }
      ],
      transportServiceCode: [
        { required: true, message: '请输入服务代码', trigger: 'blur' }
      ],
      chargeMode: [
        { required: true, message: '请选择计费方式', trigger: 'change' }
      ],
      weightMode: [
        { required: true, message: '请选择计重方式', trigger: 'change' }
      ],
      roundMode: [
        { required: true, message: '请选择进位方式', trigger: 'change' }
      ],
      bubbleCoefficient: [
        { required: true, message: '请选择计泡系数', trigger: 'change' }
      ],
      chargeWeightMode: [
        { required: true, message: '请选择收费重方式', trigger: 'change' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '创建运输服务',
  EDIT: '编辑运输服务',
  DETAIL: '运输服务详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveFirstVesselSetting,
  EDIT: updateFirstVesselSetting,
  DETAIL: getFirstVesselSettingById
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    // formData.value = Object.assign(formData.value, viewEntity.record)

    getFirstVesselSettingById(viewEntity.record.id).then((res: any) => {
      formData.value = res.result
      formData.value.ids = [res.result.id]
    })
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="8">
            <el-form-item label="运输服务名称" prop="transportServiceName">
              <el-input
                v-model.trim="formData.transportServiceName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="票计重精度">
              <el-input
                v-model.number.trim="formData.ticketWeightPrecision"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到达国家" prop="arrivalCountry">
              <DSelect
                v-model="formData.arrivalCountry"
                :options="warehouse_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务代码" prop="transportServiceCode">
              <el-input
                v-model.trim="formData.transportServiceCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="箱计重精度">
              <el-input
                v-model.number.trim="formData.boxWeightPrecision"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="可用客户">
              <DSelect
                v-model="formData.customer"
                :options="warehouse_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计费方式" prop="chargeMode">
              <DSelect
                v-model="formData.chargeMode"
                :options="charge_mode"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="尺寸精度">
              <el-input
                v-model.number.trim="formData.sizePrecision"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="目的地类型">
              <DSelect
                v-model="formData.destinationType"
                :options="destination_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计重方式" prop="weightMode">
              <DSelect
                v-model="formData.weightMode"
                :options="weight_mode"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低箱实重/体积">
              <el-input
                v-model.number.trim="formData.minBoxRealWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
          <el-col :span="8">
            <el-form-item label="进位方式" prop="roundMode">
              <DSelect
                v-model="formData.roundMode"
                :options="round_mode"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低箱材重">
              <el-input
                v-model.number.trim="formData.minBoxMaterialWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
          <el-col :span="8">
            <el-form-item label="计泡系数" prop="bubbleCoefficient">
              <el-input
                v-model.trim="formData.bubbleCoefficient"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低箱收费重">
              <el-input
                v-model.number.trim="formData.minBoxChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
          <el-col :span="8">
            <el-form-item label="收费重方式" prop="chargeWeightMode">
              <DSelect
                v-model="formData.chargeWeightMode"
                :options="charge_weight_mode"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="最低票收费重">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
