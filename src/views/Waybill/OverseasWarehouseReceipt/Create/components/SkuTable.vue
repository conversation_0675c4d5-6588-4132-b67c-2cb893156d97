<script setup lang="ts">
import AddProduct from '@/components/AddProduct/index.vue'

defineOptions({
  name: 'SkuTable'
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '<PERSON>'
  }
])

const apVisible = ref(false)
const handleAddProduct = () => {
  apVisible.value = true
}
</script>

<template>
  <el-table :data="tableData" :border="true" style="width: 100%">
    <el-table-column type="index" label="序号" prop="" width="55" />
    <el-table-column label="箱数" prop="date" width="180" />
    <el-table-column label="SKU" prop="date" width="180">
      <template #default="{}">
        <div
          class="color-#3489ff flex items-center cursor-pointer"
          @click="handleAddProduct"
        >
          <div class="i-ep:circle-plus"></div>
          <div class="ml-5">添加产品</div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="产品名称" prop="date" width="180" />
    <el-table-column label="单箱数量" prop="date" width="180" />
    <el-table-column label="单箱总数量" prop="date" width="180" />
    <el-table-column label="自定义箱条码" prop="date" width="180" />
    <el-table-column label="箱条码规则" prop="date" width="180" />
    <el-table-column label="箱子尺寸(CM)" prop="date" width="180" />
    <el-table-column label="箱子重量(KG)" prop="date" width="180" />
    <el-table-column label="操作" fixed="right" width="200">
      <template #default="{}">
        <el-button type="danger">移除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-between lh-24 my-20">
    <div>
      <span>合计:</span>
    </div>
    <div class="flex">
      <div>
        <span>SKU种类数:</span>
      </div>
      <div class="ml-20">
        <span>产品数:</span>
      </div>
      <div class="ml-20">
        <span>箱数:</span>
      </div>
    </div>
  </div>
  <div>
    <el-button type="primary">添加箱子</el-button>
    <el-button>批量导入</el-button>
  </div>

  <AddProduct v-model="apVisible"></AddProduct>
</template>

<style lang="scss" scoped></style>
