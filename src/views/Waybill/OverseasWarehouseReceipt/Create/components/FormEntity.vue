<script setup lang="ts">
defineOptions({
  name: 'FormEntity'
})

const formData = ref({
  name: ''
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="top"
    @submit.prevent
  >
    <el-row :gutter="8">
      <el-col :span="12">
        <el-form-item label="客户" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目的仓" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="客户备注" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="入库类型" prop="">
          <el-radio-group v-model="formData.name">
            <el-radio :label="'散货'" :value="1"></el-radio>
            <el-radio :label="'整柜'" :value="2"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="货柜类型" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="跟踪号" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>

        <el-form-item label="内部备注" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
