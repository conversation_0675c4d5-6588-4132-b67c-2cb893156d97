<script setup lang="ts">
import FormEntity from './components/FormEntity.vue'
import SkuTable from './components/SkuTable.vue'

defineOptions({
  name: 'FirstLegCreate'
})
</script>

<template>
  <ContentWrap title="创建运单">
    <FormEntity></FormEntity>
  </ContentWrap>
  <ContentWrap class="mt-20">
    <SkuTable></SkuTable>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="h-60 flex items-center justify-center">
      <el-button type="primary">创建海外仓入库单</el-button>
      <el-button>取消</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
