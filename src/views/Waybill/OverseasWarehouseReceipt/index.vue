<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'OverseasWarehouseReceipt'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)

provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
  // selection
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()

const [bvisible, handleResetPwd] = useToggle()

const router = useRouter()
const linkCreate = () => {
  router.push({
    path: '/waybill/overseasWarehouseReceipt/create'
  })
}

const options = [
  {
    label: '草稿',
    value: ''
  },
  {
    label: '待入库',
    value: '1'
  },
  {
    label: '收货中',
    value: '2'
  },
  {
    label: '已收货',
    value: '3'
  },
  {
    label: '待上架',
    value: '4'
  },
  {
    label: '上架中',
    value: '5'
  },
  {
    label: '已上架',
    value: '6'
  },
  {
    label: '已取消',
    value: '7'
  },
  {
    label: '全部(收货)',
    value: '8'
  },
  {
    label: '全部(上架)',
    value: '9'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.destinationType"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="h-22"></div>
            </template>
            <div class="flex">
              <DSelect
                v-model="formData.goodsAttr"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-input
                v-model.trim="formData.transportServiceName"
                clearable
                placeholder="请输入"
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.goodsAttr"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="目的地" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="国家" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库单来源" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="同步状态" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="h-22"></div>
            </template>
            <div class="flex">
              <DSelect
                v-model="formData.goodsAttr"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-date-picker type="daterange" placeholder="" class="flex-1" />
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => linkCreate()"
        >创建入库单</el-button
      >
      <el-dropdown>
        <el-button class="ml-12">
          导入/导出运单
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleResetPwd"
              >导入入库单</el-dropdown-item
            >
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown>
        <el-button class="ml-12">
          下载标签
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>下载入仓标签</el-dropdown-item>
            <el-dropdown-item>下载商品标签(SKU)</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button>快捷收货</el-button>
      <el-button>停止同步</el-button>
      <el-dropdown>
        <el-button class="ml-12">
          其他
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>添加备注</el-dropdown-item>
            <el-dropdown-item>删除内部备注</el-dropdown-item>
            <el-dropdown-item>统计数据</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column width="180">
        <template #header>
          <div>入库单号</div>
          <div>主仓单号</div>
        </template>
      </el-table-column>
      <el-table-column
        label="关联单号"
        prop="transportServiceCode"
        width="180"
      />
      <el-table-column label="上架单号" prop="chargeModeName" width="180" />
      <el-table-column label="跟踪号" prop="weightModeName" width="180" />
      <el-table-column label="客户" prop="bubbleCoefficient" width="180" />
      <el-table-column label="数量" prop="arrivalCountry" width="180" />
      <el-table-column label="费用" prop="destinationTypeName" width="180" />
      <el-table-column label="目的仓" prop="statusName" width="180" />
      <el-table-column label="国家" prop="statusName" width="180" />
      <el-table-column label="主品名" prop="statusName" width="180" />
      <el-table-column label="物品属性" prop="statusName" width="180" />
      <el-table-column label="备注" prop="statusName" width="180" />
      <el-table-column label="入库单来源" prop="statusName" width="180" />
      <el-table-column label="状态" prop="statusName" width="180" />
      <el-table-column label="同步状态" prop="statusName" width="180" />
      <el-table-column label="时间" prop="statusName" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-dropdown>
            <el-button class="ml-12">
              操作
              <div class="i-ep:arrow-down ml-5"></div>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>编辑入库单</el-dropdown-item>
                <el-dropdown-item>编辑跟踪号</el-dropdown-item>
                <el-dropdown-item>下载入仓标签</el-dropdown-item>
                <el-dropdown-item>下载商品标签(SKU)</el-dropdown-item>
                <el-dropdown-item>收货</el-dropdown-item>
                <el-dropdown-item>快捷收货</el-dropdown-item>
                <el-dropdown-item>详情</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
