<script setup lang="ts">
import type { OrgEntity } from '@/api/institution/types'
import InstitutionEntity from './components/InstitutionEntity.vue'
import InstitutionTree from './components/InstitutionTree.vue'
import { deleteOrgInfo } from '@/api/institution'

defineOptions({
  name: 'Institution'
})

const InstitutionTreeRef = ref()
const InstitutionEntityRef = ref()
const viewEntity = ref<ViewEntity>({
  type: 'DETAIL',
  record: undefined
})

const handleNodeClick = (data: OrgEntity) => {
  viewEntity.value.type = 'DETAIL'
  viewEntity.value.record = data
}

const handleOrgChange = async (type: string, data: OrgEntity) => {
  if (type === 'DEL') {
    if (data.id) {
      await deleteOrgInfo(data.id)
      InstitutionTreeRef.value.init()
    }
  } else if (type === 'EDIT') {
    viewEntity.value.type = type as DlViewType
    viewEntity.value.record = data
  } else {
    viewEntity.value.type = type as DlViewType
    viewEntity.value.record = {}
  }
}

const handleRefresh = () => {
  console.log('刷新', viewEntity.value)
  InstitutionTreeRef.value.init(viewEntity.value.record?.id)
  InstitutionEntityRef.value?.refreshOrgRoleTreeApi()
}

const handleReset = () => {
  console.log('重置')
  InstitutionTreeRef.value.init(viewEntity.value.record?.id)
}
</script>

<template>
  <ContentWrap class="h-100%">
    <el-row :gutter="20">
      <el-col :span="6">
        <InstitutionTree
          ref="InstitutionTreeRef"
          @nodeClick="handleNodeClick"
          @orgChange="handleOrgChange"
        ></InstitutionTree>
      </el-col>
      <el-col :span="18">
        <InstitutionEntity
          ref="InstitutionEntityRef"
          :viewEntity="viewEntity"
          @refresh="handleRefresh"
          @reset="handleReset"
        ></InstitutionEntity>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
