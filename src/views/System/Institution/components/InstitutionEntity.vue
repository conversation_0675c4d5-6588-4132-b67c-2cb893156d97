<script setup lang="ts">
import { saveOrgInfo, updateOrgInfo } from '@/api/institution'
import type { OrgEntity } from '@/api/institution/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'InstitutionEntity'
})

const emits = defineEmits(['refresh', 'reset'])
const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const titleMap: Record<DlViewType, string> = {
  ADD: '新增组织',
  EDIT: '编辑组织',
  DETAIL: '组织详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const management_mode = useDictItem('management_mode')

const { orgRoleTree, orgRoleTreeApi } = useOptions(['orgRoleTree'])

const { formData, formDataRef, rules, resetFormData } = useFormData<OrgEntity>(
  {
    id: undefined,
    orgCode: undefined,
    orgName: undefined,
    parentId: undefined,
    chargePerson: undefined,
    telephone: undefined,
    mobilePhone: undefined,
    quota: undefined,
    address: undefined,
    managementMode: undefined,
    postcode: undefined
  },
  {
    orgCode: [{ required: true, message: '请输入机构编码', trigger: 'blur' }],
    orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
    managementMode: [
      { required: true, message: '请选择管理方式', trigger: 'change' }
    ]
  }
)

watch(
  () => [viewEntity.type, viewEntity.record],
  () => {
    if (viewEntity.type === 'ADD') {
      // 新增时重置表单数据
      resetFormData()
      formDataRef.value?.resetFields()
      formData.value.parentId = viewEntity.record?.parentId
    }
    if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
      Object.assign(formData.value, viewEntity.record)
    }

    formData.value.parentId =
      formData.value.parentId === '0' ? undefined : formData.value.parentId
  },
  {
    immediate: true,
    deep: true
  }
)

// 上级机构需过滤自己
const orgRoleTreeFilter = computed(() => {
  if (viewEntity.type === 'EDIT') {
    return orgRoleTree.value.filter(
      (item: OrgEntity) => item.id !== viewEntity.record?.id
    )
  }
  return orgRoleTree.value
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveOrgInfo,
  EDIT: updateOrgInfo,
  DETAIL: () => Promise.resolve({})
}

const handleSave = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      apiMap[viewEntity.type](formData.value).then(() => {
        // 保存成功后，刷新父组件数据
        emits('refresh')
      })
    }
  })
}

const handleReset = () => {
  emits('reset')
}

const refreshOrgRoleTreeApi = () => {
  orgRoleTreeApi()
}

defineExpose({ refreshOrgRoleTreeApi })
</script>

<template>
  <div class="mb-18 fw-600">{{ titleStr }}</div>
  <el-form
    :model="formData"
    :rules="rules"
    ref="formDataRef"
    label-position="top"
    :disabled="viewEntity.type === 'DETAIL'"
    @submit.prevent
  >
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="机构编码" prop="orgCode">
          <el-input
            v-model.trim="formData.orgCode"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="机构名称" prop="orgName">
          <el-input
            v-model.trim="formData.orgName"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="上级机构" prop="">
          <DSelect
            v-model="formData.parentId"
            :options="orgRoleTreeFilter"
            placeholder="请选择"
            :fields="{ label: 'orgName', value: 'id' }"
          ></DSelect>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="负责人" prop="">
          <el-input
            v-model.trim="formData.chargePerson"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="电话" prop="">
          <el-input
            v-model.trim="formData.telephone"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="手机" prop="">
          <el-input
            v-model.trim="formData.mobilePhone"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="代收货款限额" prop="">
          <el-input
            v-model.trim="formData.quota"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="详细地址" prop="">
          <el-input
            v-model.trim="formData.address"
            clearable
            placeholder="请输入"
            type="textarea"
            :rows="5"
            :maxlength="150"
            resize="none"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="管理方式" prop="managementMode">
          <DSelect
            v-model="formData.managementMode"
            :options="management_mode"
            placeholder="请选择"
          ></DSelect>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="邮编" prop="">
          <el-input
            v-model.trim="formData.postcode"
            clearable
            placeholder="多个之间用英文逗号分隔"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col v-if="['ADD', 'EDIT'].includes(viewEntity.type)" :span="24">
        <div class="w-full text-align-center">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
