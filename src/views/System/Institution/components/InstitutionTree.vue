<script setup lang="ts">
import { getOrgTree } from '@/api/institution'
import type { OrgEntity } from '@/api/institution/types'
import { debounce } from 'lodash-es'
import { findById } from '../helper'

defineOptions({
  name: 'InstitutionTree'
})

const emits = defineEmits(['nodeClick', 'orgChange'])
const orgTree = ref<OrgEntity[]>([])
const treeRef = ref()

const defaultProps = {
  children: 'child',
  label: 'orgName'
}

const init = async (orgId?: string) => {
  const res = await getOrgTree()
  orgTree.value = res.result

  nextTick(() => {
    if (orgId) {
      treeRef.value.setCurrentKey(orgId)
      const checkNodes = findById(orgTree.value, orgId)
      handleNodeClick(checkNodes)
    } else {
      handleNodeClick(orgTree.value?.[0])
      treeRef.value.setCurrentKey(orgTree.value?.[0]?.id)
    }
  })
}

init()

const currentNode = ref<OrgEntity>()
const handleNodeClick = debounce((data: OrgEntity) => {
  // 如果点击的是当前节点，不做任何操作
  // if (currentNode.value?.id === data.id) {
  //   return
  // }
  currentNode.value = data
  emits('nodeClick', data)
}, 500)

const handleBtn = (type: string) => {
  emits('orgChange', type, currentNode.value)
}

const formatTitle = (_node: any, data: any) => {
  return `${data.orgName} (${data.orgCode})`
}

defineExpose({ init })
</script>

<template>
  <el-scrollbar max-height="400" class="w-full">
    <div class="mb-20">
      <el-button type="primary" @click="handleBtn('ADD')">新增</el-button>
      <el-button type="primary" @click="handleBtn('EDIT')">编辑</el-button>
      <el-button type="danger" @click="handleBtn('DEL')">删除</el-button>
    </div>
    <el-tree
      ref="treeRef"
      style="max-width: 600px"
      :data="orgTree"
      node-key="id"
      highlight-current
      :expand-on-click-node="false"
      default-expand-all
      :props="defaultProps"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        {{ formatTitle(node, data) }}
      </template>
    </el-tree>
  </el-scrollbar>
</template>

<style lang="scss" scoped></style>
