/**
 * @description: 根据orgId 获取机构
 * @param {*} data
 * @param {*} targetId
 * @return {*}
 */
export function findById(data: string | any[], targetId: any) {
  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.id === targetId) {
      return item
    }
    if (item.child && item.child.length > 0) {
      const result: any = findById(item.child, targetId)
      if (result) {
        return result
      }
    }
  }
  return null
}
