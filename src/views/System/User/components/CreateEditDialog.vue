<script setup lang="ts">
import { saveSysUser, updateUser } from '@/api/user'
import type { UserEntity } from '@/api/user/types'
import { phoneReg } from '@/constants'
import { useFormData } from '@/hooks/useFormData'
import { useOptions } from '@/hooks/useOptions'
import { getRoleListByOrgId } from '../helper'

defineOptions({
  name: 'CreateEditDialog'
})
const status = inject('status') as any

const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { orgRoleTree } = useOptions(['orgRoleTree'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const titleMap: Record<DlViewType, string> = {
  ADD: '新增用户',
  EDIT: '编辑用户',
  DETAIL: '用户详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const { formData, formDataRef, rules, resetFormData } = useFormData<UserEntity>(
  {
    order: undefined,
    id: undefined,
    orgId: undefined,
    position: undefined,
    roleId: undefined,
    userName: undefined,
    nickName: undefined,
    phone: undefined,
    email: undefined,
    status: '1',
    type: undefined
  },
  {
    orgId: [{ required: true, message: '请选择部门', trigger: 'change' }],
    position: [{ required: true, message: '请输入职务', trigger: 'blur' }],
    roleId: [{ required: true, message: '请选择角色', trigger: 'change' }],
    userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    nickName: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          const reg = phoneReg
          if (reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入有效的手机号'))
          }
        },
        trigger: 'blur'
      }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }
)

const disabled = computed(() => viewEntity.type === 'EDIT')

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveSysUser,
  EDIT: updateUser,
  DETAIL: () => Promise.resolve({})
}

const roleList = computed(() => {
  if (formData.value.orgId) {
    return getRoleListByOrgId(formData.value.orgId, orgRoleTree.value)
  } else {
    return []
  }
})

const handleOpen = () => {
  console.log('viewEntity', viewEntity)
  console.log('status', status.value)

  if (viewEntity.type === 'EDIT') {
    Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    destroy-on-close
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="部门" prop="orgId">
              <DSelect
                v-model="formData.orgId"
                :options="orgRoleTree"
                placeholder="请选择"
                :fields="{ label: 'orgName', value: 'id' }"
              ></DSelect>
              <!-- <el-select
                v-model="formData.orgId"
                clearable
                placeholder="请选择部门"
              >
                <el-option value="1" label="1"></el-option>
                <el-option
                  value="45cbd73d898fbcff0bd4ae601d546ce6"
                  label="22"
                ></el-option>
                <el-option
                  value="b3a96bb90b5e961a3caec7d5345a84d7"
                  label="333"
                ></el-option>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input
                v-model.trim="formData.position"
                clearable
                placeholder="请输入职位"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleId">
              <DSelect
                v-model="formData.roleId"
                :options="roleList"
                placeholder="请选择"
                :fields="{ label: 'roleName', value: 'id' }"
              ></DSelect>
              <!-- <el-select
                v-model="formData.roleId"
                clearable
                placeholder="请选择角色"
              >
                <el-option value="1" label="1"></el-option>
                <el-option
                  value="b7bb968974e60da9413dfd591712216b"
                  label="22"
                ></el-option>
                <el-option
                  value="fa845fad2ed8b508a8e43a6eaa050a89"
                  label="333"
                ></el-option>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名" prop="userName">
              <el-input
                v-model.trim="formData.userName"
                clearable
                placeholder="请输入用户名"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickName">
              <el-input
                v-model.trim="formData.nickName"
                clearable
                placeholder="请输入昵称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model.trim="formData.phone"
                clearable
                placeholder="请输入手机号"
                :maxlength="11"
                :disabled="disabled"
                oninput="value=value.replace(/^\.+|[^\d.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model.trim="formData.email"
                clearable
                placeholder="请输入邮箱"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <DRadioGroup
                v-model="formData.status"
                :options="status"
              ></DRadioGroup>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
