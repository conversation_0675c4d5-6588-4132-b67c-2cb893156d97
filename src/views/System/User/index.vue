<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import type { UserEntity } from '@/api/user/types'
import {
  deleteUser,
  disableUser,
  enableUser,
  getUserList,
  resetPassword
} from '@/api/user'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'User'
})

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<UserEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    userName: undefined
  },
  fetchDataApi: async () => {
    const res = await getUserList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    console.log('res', res)

    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    const res = await deleteUser({ id: record.id })
    return !!res
  }
})

const status = useDictItem('user_status')

provide('status', status)

const bvisible = ref(false)
const textinfo = ref('')
const handleResetPwd = async (row: UserEntity) => {
  const _res = await resetPassword({ id: row.id })
  textinfo.value = `${row.userName} 密码重置成功，新密码为 ${_res.result}`
  bvisible.value = true
}

const handleDisabledUser = async (row: UserEntity, type: '0' | '1') => {
  if (type === '1') {
    await enableUser({ id: row.id })
  }
  if (type === '0') {
    await disableUser({ id: row.id })
  }
  tableMethods.handleQuery()
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="关键字" prop="">
            <el-input
              v-model.trim="formData.userName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect v-model="formData.status" :options="status"></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button @click="tableMethods.handleReset">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增用户</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
    >
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="登录账号" prop="userName" width="180" />
      <el-table-column label="用户名称" prop="nickName" width="180" />
      <el-table-column label="部门" prop="orgName" width="180" />
      <el-table-column label="角色信息" prop="roleName" min-width="120" />
      <el-table-column label="状态" prop="status" width="180" />
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" fixed="right" width="360">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button type="primary" @click="handleResetPwd(row)"
            >重置密码</el-button
          >
          <el-button
            v-if="row.status === '1'"
            type="warning"
            @click="() => handleDisabledUser(row, '0')"
            >冻结</el-button
          >
          <el-button
            v-if="row.status === '0'"
            type="primary"
            @click="() => handleDisabledUser(row, '1')"
            >启用</el-button
          >
          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.refresh"
  ></CreateEditDialog>

  <ClipboardDialog v-model="bvisible" :textinfo="textinfo"></ClipboardDialog>
</template>

<style lang="scss" scoped></style>
