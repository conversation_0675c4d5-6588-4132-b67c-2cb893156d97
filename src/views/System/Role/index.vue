<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import AuthDialog from './components/AuthDialog.vue'
import CreateEditDialog from './components/CreateEditDialog.vue'
import { deleteOrgInfo, getRoleList, updateRoleInfo } from '@/api/role'
import type { RoleEntity } from '@/api/role/types'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'Role'
})

const { orgRoleTree } = useOptions(['orgRoleTree'])

provide('orgRoleTree', orgRoleTree)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<RoleEntity>({
  immediate: true,
  initialFormData: {
    roleName: undefined,
    orgId: undefined
  },
  fetchDataApi: async () => {
    const res = await getRoleList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    const res = await deleteOrgInfo(record.id)
    return !!res
  }
})

const handleStatusChange = async (row: any, val: string) => {
  await updateRoleInfo({ id: row.id, status: val })
  tableMethods.handleQuery()
}

const record = ref()
const avisible = ref(false)

const handleAuthConfig = (row: RoleEntity) => {
  record.value = row
  avisible.value = true
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="角色名称" prop="">
            <el-input
              v-model.trim="formData.roleName"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="机构级别" prop="">
            <DSelect
              v-model="formData.orgId"
              :options="orgRoleTree"
              placeholder="请选择"
              :fields="{ label: 'orgName', value: 'id' }"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button @click="tableMethods.handleReset">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20" v-loading="loading">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增</el-button
      >
    </div>
    <el-table :data="dataList" row-key="id" :border="true" class="w-full">
      <el-table-column type="index" label="序号" prop="date" width="55" />
      <el-table-column label="排序编码" prop="sort" width="100" />
      <el-table-column label="角色名称" prop="roleName" min-width="180" />
      <el-table-column label="机构级别" prop="orgName" min-width="100" />
      <el-table-column label="状态" prop="status" min-width="100">
        <template #default="{ row }">
          <el-switch
            :model-value="row.status"
            active-value="1"
            inactive-value="0"
            @change="(val: any) => handleStatusChange(row, val)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="250">
        <template #default="{ row }">
          <el-button
            type="primary"
            class="cursor-pointer"
            text
            @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            type="primary"
            text
            class="!ml-8 cursor-pointer"
            @click="() => handleAuthConfig(row)"
            >权限设置</el-button
          >
          <el-button
            type="danger"
            text
            class="!ml-8 cursor-pointer"
            @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :viewEntity="viewEntity"
    @refresh="tableMethods.refresh"
  ></CreateEditDialog>
  <AuthDialog v-model="avisible" :record="record"></AuthDialog>
</template>

<style lang="scss" scoped></style>
