<script setup lang="ts">
import { saveRoleInfo, updateRoleInfo } from '@/api/role'
import type { RoleEntity } from '@/api/role/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const orgRoleTree = inject('orgRoleTree') as any[]

const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])
const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const titleMap: Record<DlViewType, string> = {
  ADD: '新建用户',
  EDIT: '编辑用户',
  DETAIL: '用户详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const { formData, formDataRef, rules, resetFormData } = useFormData<RoleEntity>(
  {
    id: undefined,
    roleName: undefined,
    orgId: undefined,
    sort: undefined,
    status: '1'
  },
  {
    roleName: [{ required: true, message: '请选择部门', trigger: 'blur' }],
    orgId: [{ required: true, message: '请选择角色', trigger: 'change' }]
  }
)

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveRoleInfo,
  EDIT: updateRoleInfo,
  DETAIL: () => Promise.resolve()
}

const handleOpen = () => {
  if (viewEntity.type === 'EDIT') {
    Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirn = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    destroy-on-close
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="8" class="w-full">
          <el-col :span="12">
            <el-form-item label="角色名称" prop="roleName">
              <el-input
                v-model.trim="formData.roleName"
                clearable
                placeholder="请输入角色名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构级别" prop="orgId">
              <DSelect
                v-model="formData.orgId"
                :options="orgRoleTree"
                placeholder="请选择"
                :fields="{ label: 'orgName', value: 'id' }"
              ></DSelect>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="显示排序" prop="">
              <el-input
                v-model.trim="formData.sort"
                clearable
                placeholder="请输入排序"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="">
              <el-radio-group v-model="formData.status">
                <el-radio :label="'启用'" value="1"></el-radio>
                <el-radio :label="'禁用'" value="0"></el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
