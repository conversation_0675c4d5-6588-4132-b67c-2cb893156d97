<script setup lang="ts">
import { getMenuIdList, getMenuTree, saveOrUpdateRoleMenu } from '@/api/role'
import type { RoleEntity } from '@/api/role/types'

defineOptions({
  name: 'AuthDialog'
})
const visible = defineModel({ default: false })
const { record } = defineProps<{
  record: RoleEntity | undefined
}>()

interface Tree {
  label: string
  children?: Tree[]
}

const treeRef = ref()
const menuTree = ref<any[]>([])

const handleNodeClick = (data: Tree) => {
  console.log(data)
}

const defaultProps = {
  children: 'children',
  label: 'name'
}

const handleOpen = async () => {
  const res = await getMenuTree()
  console.log('res', res)
  menuTree.value = res.result

  if (record?.id) {
    getMenuIdList(record.id).then(res => {
      console.log('res', res)
      if (res.result.length > 0) {
        treeRef.value.setCheckedKeys(res.result)
      }
    })
  }
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = async () => {
  // visible.value = false
  const checkNodes = treeRef.value.getCheckedNodes()
  const checkNodeKeys = treeRef.value.getCheckedKeys()
  console.log('checkNodes', checkNodes)
  console.log('checkNodeKeys', checkNodeKeys)
  await saveOrUpdateRoleMenu({
    roleId: record?.id as string,
    menuIds: checkNodeKeys
  })

  handleCancel()
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="权限设置"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-tree
        ref="treeRef"
        style="max-width: 600px"
        show-checkbox
        node-key="id"
        default-expand-all
        :data="menuTree"
        :props="defaultProps"
        @check="handleNodeClick"
      />
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
