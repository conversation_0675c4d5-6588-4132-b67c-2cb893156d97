<script setup lang="ts">
defineOptions({
  name: 'FreightTrialCalculation'
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '<PERSON>',
    address: 'No. 18, Grove St, Los Angeles'
  }
])

const formData = ref({
  name: ''
})
</script>

<template>
  <div class="flex w-full h-[calc(100vh-90px)]">
    <div class="w-400 h-full">
      <ContentWrap class="h-full">
        <div class="flex justify-between items-center">
          <div class="text-16 fw-600">运费试算</div>
        </div>
        <div class="flex-1 mt-10">
          <el-form :model="formData" ref="formDataRef" @submit.prevent>
            <el-form-item label="发货仓" prop="name">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
            <el-form-item label="到货国家/地区" prop="name">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
            <el-form-item label="到货编码" prop="name">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
            <el-form-item label="报价方案" prop="name">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
            <el-form-item label="物流渠道" prop="name">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
            <el-form-item label="货物信息" prop="name">
              <el-radio-group v-model="formData.name">
                <el-radio :label="'手动输入'" :value="1"></el-radio>
                <el-radio :label="'选择SKU'" :value="2"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="尺寸" prop="name">
              <div class="w-full flex">
                <el-input
                  v-model.trim="formData.name"
                  class="flex-1"
                  clearable
                  placeholder=""
                ></el-input>
                <el-input
                  v-model.trim="formData.name"
                  class="flex-1"
                  clearable
                  placeholder=""
                ></el-input>
                <el-input
                  v-model.trim="formData.name"
                  class="flex-1"
                  clearable
                  placeholder=""
                ></el-input>
                <el-input value="cm" disabled class="!w-50"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="重量" prop="name">
              <div class="flex w-full">
                <el-input
                  v-model.trim="formData.name"
                  class="flex-1"
                  clearable
                  placeholder=""
                ></el-input>
                <el-input value="kg" disabled class="!w-50"></el-input>
              </div>
            </el-form-item>
          </el-form>
          <div class="flex justify-around mt-10">
            <el-button>重置</el-button>
            <el-button type="primary">试算</el-button>
          </div>
        </div>
      </ContentWrap>
    </div>
    <div class="flex-1 ml-20 flex flex-col">
      <ContentWrap class="h-230">
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="商品编码(SKU)" prop="date" />
          <el-table-column label="产品名称" prop="date" />
          <el-table-column label="数量" prop="date" />
          <el-table-column label="尺寸(cm)" prop="date" />
          <el-table-column label="重量(kg)" prop="date" />
          <el-table-column label="操作" width="260">
            <el-button type="primary" text>解除配对</el-button>
            <el-button type="primary" text>取消</el-button>
            <el-button type="primary" text>配对</el-button>
          </el-table-column>
        </el-table>
        <el-button type="primary" class="mt-16">选择商品</el-button>
      </ContentWrap>
      <ContentWrap class="mt-20 flex-1">
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="物流渠道" prop="date" />
          <el-table-column label="总费用" prop="date" />
          <el-table-column label="基础运费" prop="date" />
          <el-table-column label="操作费" prop="date" />
          <el-table-column label="附加费" prop="date" />
          <el-table-column label="币种" prop="date" />
          <el-table-column label="计费重" prop="date" />
          <el-table-column label="体积重" prop="date" />
        </el-table>
      </ContentWrap>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
