<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import { useTable } from '@/hooks/useTable'
import { ElMessage } from 'element-plus'
import { useDictItem } from '@/hooks/useDictItem'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList,
  updateFirstVesselSetting
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'InitialServiceSettings'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')
const country = useDictItem('country')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)
provide('country', country)
provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()

const handleStatusChange = async (type: '0' | '1', records: any[]) => {
  if (records.length === 0) {
    ElMessage.error('请选择服务')
    return
  }
  const ids = records.map(item => item.id?.toString())
  const result = records.filter(item => item.status?.toString() === type)
  if (result?.length > 0) {
    if (type === '0') {
      ElMessage.error('选中的服务中存在停用的服务')
      return
    }
    ElMessage.error('选中的服务中存在停用的服务')
    return
  }
  await updateFirstVesselSetting({ ids: ids as string[], status: type })
  tableMethods.handleQuery()
}

const handleDelete = () => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择服务')
    return
  }
  tableMethods.hadnleDel(selection.value)
}

const [bvisible, handleResetPwd] = useToggle()

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '电商平台仓',
    value: 'platformWarehouse'
  },
  {
    label: '海外仓',
    value: 'overseasWarehouse'
  },
  {
    label: '其他地址',
    value: 'otherAddress'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.destinationType"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="服务名称" prop="">
            <el-input
              v-model.trim="formData.transportServiceName"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="服务代码" prop="">
            <el-input
              v-model.trim="formData.transportServiceCode"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="计费方式" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="计重方式" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="国家" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >创建服务</el-button
      >
      <el-button
        type="primary"
        @click="() => handleStatusChange('1', selection)"
        >启用</el-button
      >
      <el-button
        type="warning"
        @click="() => handleStatusChange('0', selection)"
        >停用</el-button
      >
      <el-button type="danger" @click="() => handleDelete()">删除</el-button>
      <el-dropdown>
        <el-button class="ml-12">
          导入/导出
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleResetPwd">导入</el-dropdown-item>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column
        label="运输服务名称"
        prop="transportServiceName"
        width="180"
      />
      <el-table-column
        label="服务代码"
        prop="transportServiceCode"
        width="180"
      />
      <el-table-column label="计费方式" prop="chargeModeName" width="180" />
      <el-table-column label="计重方式" prop="weightModeName" width="180" />
      <el-table-column label="计泡系数" prop="bubbleCoefficient" width="180" />
      <el-table-column label="国家" prop="arrivalCountry" width="180" />
      <el-table-column
        label="目的地类型"
        prop="destinationTypeName"
        width="180"
      />
      <el-table-column label="状态" prop="statusName" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button
            text
            type="primary"
            @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            v-if="row.status === '0'"
            text
            type="primary"
            @click="() => handleStatusChange('1', [row])"
            >启用</el-button
          >
          <el-button
            v-if="row.status === '1'"
            text
            type="warning"
            @click="() => handleStatusChange('0', [row])"
            >停用</el-button
          >
          <el-button
            text
            type="danger"
            @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
