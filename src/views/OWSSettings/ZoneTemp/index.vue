<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import type { CategoryEntity } from '@/api/category/types'
import { deleteCategory, getCategoryList } from '@/api/category'

defineOptions({
  name: 'ZoneTemp'
})
const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})

const router = useRouter()

const linkCreate = () => {
  router.push({
    path: '/OWSSettings/zoneTemp/create'
  })
}

const linkEdit = () => {
  router.push({
    path: '/OWSSettings/zoneTemp/edit'
  })
}

const linkTesging = () => {
  router.push({
    path: '/OWSSettings/zoneTemp/testing'
  })
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="规则名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => linkCreate()"
        >新建分区规则</el-button
      >
      <el-button @click="linkTesging">测试分区规则</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="规则名称" prop="id" />
      <el-table-column label="最后更新人" prop="parentId" />
      <el-table-column label="最后更新时间" prop="status" />
      <el-table-column label="备注" prop="status" />
      <el-table-column label="状态" prop="status" />
      <el-table-column label="操作" fixed="right" width="320">
        <template #default="{ row }">
          <el-button type="primary" @click="() => linkEdit()">编辑</el-button>

          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >停用</el-button
          >

          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >启用</el-button
          >

          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.handleQuery()"
  ></CreateEditDialog>
</template>

<style lang="scss" scoped></style>
