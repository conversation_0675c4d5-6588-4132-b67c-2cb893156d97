<script setup lang="ts">
defineOptions({
  name: 'Testing'
})

const formData = reactive({
  name: ''
})

const dataList = reactive([
  {
    id: 1,
    parentId: 1,
    status: 1,
    remark: '1'
  }
])
</script>

<template>
  <div class="flex h-[calc(100vh-var(--headerHeight)-40px)]">
    <div class="w-260 h-full mr-16 bg-white p-24">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
      >
        <el-form-item label="分区规则名称" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="国家/地区" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="城市" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item label="邮编" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item>
          <div class="w-full flex">
            <el-button type="primary" class="flex-1">搜索</el-button>
            <el-button>重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-1 h-full">
      <el-scrollbar max-height="100%">
        <ContentWrap class="">
          <div class="mb-24">
            <span class="fw-600">计费使用的分区</span>
            <span class="text-14 color-#595959 ml-10"
              >同一个地址可能会找到多个分区，但是计费时只会使用优先级最高的一个
            </span>
          </div>
          <el-table :data="dataList" :border="true" class="w-full">
            <el-table-column label="分区" prop="id" />
            <el-table-column label="国家/地区" prop="parentId" />
            <el-table-column label="城市" prop="status" />
            <el-table-column label="开始邮编" prop="status" />
            <el-table-column label="结束邮编" prop="status" />
          </el-table>
        </ContentWrap>
        <ContentWrap class="mt-20">
          <div class="mb-24">
            <span class="fw-600">查询到的所有分区</span>
          </div>
          <el-table :data="dataList" :border="true" class="w-full">
            <el-table-column label="分区" prop="id" />
            <el-table-column label="国家/地区" prop="parentId" />
            <el-table-column label="城市" prop="status" />
            <el-table-column label="开始邮编" prop="status" />
            <el-table-column label="结束邮编" prop="status" />
          </el-table>
        </ContentWrap>
      </el-scrollbar>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
