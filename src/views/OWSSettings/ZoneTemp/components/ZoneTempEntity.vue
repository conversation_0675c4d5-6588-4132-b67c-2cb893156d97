<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getOrgTree } from '@/api/institution'

defineOptions({
  name: 'ZoneTempCreate'
})

// 表单数据
const formData = reactive({
  institutionId: '',
  remark: '',
  name: ''
})

// 机构列表
const institutionList = ref([])

// 获取机构列表
const getInstitutionList = async () => {
  try {
    const res = await getOrgTree()
    if (res && res.result) {
      institutionList.value = res.result
    }
  } catch (error) {
    console.error('获取机构列表失败', error)
  }
}

// 表格数据
const tableData = ref<any[]>([])

// 添加行
const handleAddRow = () => {
  tableData.value.push({
    id: Date.now(),
    zoneName: '',
    remark: ''
  })
}

// 保存表单
const handleSave = () => {
  // 这里实现保存逻辑
  ElMessage.success('保存成功')
}

// 取消
const handleCancel = () => {
  // 返回上一页
  history.back()
}

// 页面加载时获取机构列表
onMounted(() => {
  getInstitutionList()
})
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="规则名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex">
      <el-button>Excel导入</el-button>
      <el-button>导入区域</el-button>
    </div>
    <div class="mt-12 bg-[#e8f4ff] p-12 font-14 flex items-center">
      <div class="i-ep:info-filled color-#0f44ff text-16 mr-10"></div>
      <div class="text-14">
        如果要导入多条同类型的分区，可以使用Excel导入方式，不用单条添加。
      </div>
    </div>

    <!-- 表格 -->
    <el-table :data="tableData" :border="true" class="mt-12">
      <el-table-column label="分区代码">
        <template #default="{ row }">
          <el-input v-model="row.zoneName" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="国家/地区">
        <template #default="{ row }">
          <el-input v-model="row.remark" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="城市">
        <template #default="{ row }">
          <el-input v-model="row.startCode" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="开始邮编">
        <template #default="{ row }">
          <el-input v-model="row.endCode" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="结束邮编">
        <template #default="{ row }">
          <el-input v-model="row.endCode" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ $index }">
          <el-button type="danger" link @click="tableData.splice($index, 1)"
            >移除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加按钮 -->
    <div class="mt-12">
      <el-button type="primary" text plain @click="handleAddRow"
        >+ 添加行</el-button
      >
    </div>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <!-- 按钮区域 -->
    <div class="flex justify-center">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.tip-text {
  margin-left: 20px;
  color: #909399;
  font-size: 14px;
}

.add-row {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.button-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
