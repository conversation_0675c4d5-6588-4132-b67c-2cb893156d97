<script setup lang="ts">
import { saveCategory, updateCategory } from '@/api/category'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    parentId: undefined,
    name: undefined,
    description: undefined,
    level: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const titleMap: Record<DlViewType, string> = {
  ADD: '新建燃油规则',
  EDIT: '编辑燃油规则',
  DETAIL: '燃油规则详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveCategory,
  EDIT: updateCategory,
  DETAIL: () => Promise.resolve({})
}

const isADD = computed(() => viewEntity.type === 'ADD')

const handleOpen = () => {
  console.log('viewEntity', viewEntity)
  // TODO 创建子类、默认选中父级
  if (viewEntity.subType === 1) {
    formData.value.parentId = viewEntity.record.id
    formData.value.level = viewEntity.record.level
  }

  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="500"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="规格名称" prop="">
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
        ></el-input>
      </el-form-item>

      <el-form-item label="生效日期范围" prop="">
        <el-date-picker type="daterange" placeholder="" />
      </el-form-item>

      <el-form-item label="费率" prop="">
        <el-input v-model.trim="formData.description" clearable placeholder="">
          <template #suffix>
            <span>%</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="isADD" label="备注" prop="">
        <el-input
          v-model.trim="formData.description"
          clearable
          placeholder=""
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
