<script setup lang="ts">
defineOptions({
  name: 'TabThird'
})

const formData = ref({
  name: '1',
  num: 1,
  bol: false,
  arr: []
})

const rules = ref({})

const tableData = ref([
  {
    name: 1,
    value: 2
  }
])
</script>

<template>
  <el-form
    :model="formData"
    :rules="rules"
    ref="formDataRef"
    label-width="200px"
    label-position="right"
    @submit.prevent
    class="w-full"
  >
    <el-form-item label="附加费类型" prop="">
      <el-checkbox-group v-model="formData.arr">
        <el-checkbox label="偏远地区附加费(DAS)" :value="1" />
        <el-checkbox label="超偏远地区附加费(EAS)" :value="2" />
        <el-checkbox label="超级偏远地区附加费(RAS)" :value="3" />
        <el-checkbox label="超超偏远地区附加费(SAS)" :value="4" />
        <el-checkbox label="超长处理费(AHS)" :value="5" />
        <el-checkbox label="Oversize处理费" :value="6" />
        <el-checkbox label="超限制附加费" :value="7" />
        <el-checkbox label="超尺寸附加费" :value="8" />
        <el-checkbox label="超体积附加费" :value="9" />
        <el-checkbox label="住宅地址附加费" :value="10" />
        <el-checkbox label="签名附加费" :value="11" />
        <el-checkbox label="保险附加费" :value="12" />
        <el-checkbox label="旺季附加费" :value="13" />
        <el-checkbox label="面单取消手续费" :value="14" />
      </el-checkbox-group>
    </el-form-item>

    <el-form-item label="偏远地区附加费(DAS)" prop="">
      <div class="flex items-center">
        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <el-input
          v-model.trim="formData.num"
          clearable
          placeholder=""
          class="!w-120 ml-10"
        ></el-input>
        <el-checkbox label="计算燃费" :value="1" class="ml-10" />
      </div>
    </el-form-item>

    <el-form-item label="超偏远地区附加费(EAS)" prop="">
      <div class="flex items-center">
        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <el-input
          v-model.trim="formData.num"
          clearable
          placeholder=""
          class="!w-120 ml-10"
        ></el-input>
        <el-checkbox label="计算燃费" :value="1" class="ml-10" />
      </div>
    </el-form-item>

    <el-form-item label="超级偏远地区附加费(RAS)" prop="">
      <div class="flex items-center">
        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <el-input
          v-model.trim="formData.num"
          clearable
          placeholder=""
          class="!w-120 ml-10"
        ></el-input>
        <el-checkbox label="计算燃费" :value="1" class="ml-10" />
      </div>
    </el-form-item>

    <el-form-item label="超超偏远地区附加费(SAS)" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="超长处理费(AHS)" prop="">
      <div class="flex items-center">
        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150"
        ></DSelect>

        <DSelect
          v-model="formData.name"
          :options="[{ itemText: '1', itemValue: '1' }]"
          placeholder="请选择"
          class="!w-150 ml-10"
        ></DSelect>

        <el-checkbox label="最小计费重" :value="1" class="ml-10" />
        <el-checkbox label="计算燃费" :value="1" class="ml-10" />
      </div>
      <div class="mt-12 w-full">
        <el-table :data="tableData" :border="true" class="w-full">
          <el-table-column label="分区" prop="date">
            <template #default="{}"> 费用 </template>
          </el-table-column>
          <el-table-column label="1" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="2" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="3" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="4" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="5" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="6" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="7" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="8" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="9" prop="date">
            <template #default="{ row }">
              <el-input
                v-model.trim="row.value"
                clearable
                placeholder=""
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="date">
            <template #default="{}">
              <el-button type="primary" text>设置整行</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form-item>

    <el-form-item label="超重处理费(AHS)" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="Oversize处理费" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="超限制附加费" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="超尺寸附加费" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="超体积附加费" prop="">
      <el-select
        v-model="formData.name"
        clearable
        placeholder=""
        class="!w-150"
      >
        <el-option :value="1" label="1"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="住宅地址附加费" prop="">
      <div class="flex items-center">
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
          class="!w-120"
        ></el-input>
        <div class="ml-5">/ 每票</div>
        <el-checkbox label="计算燃费" :value="1" class="ml-8" />
      </div>
    </el-form-item>

    <el-form-item label="签名附加费" prop="">
      <div class="flex items-center">
        <div>
          <div>直接签名附加费</div>
          <div class="flex">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              class="!w-120"
            ></el-input>
            <div class="ml-5">/ 每票</div>
          </div>
        </div>

        <div class="ml-10">
          <div>间接签名附加费</div>
          <div class="flex">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              class="!w-120"
            ></el-input>
            <div class="ml-5">/ 每票</div>
          </div>
        </div>

        <div class="ml-10">
          <div>成人签名附加费</div>
          <div class="flex">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              class="!w-120"
            ></el-input>
            <div class="ml-5">/ 每票</div>
          </div>
        </div>

        <el-checkbox label="计算燃费" :value="1" class="ml-10 mt-32" />
      </div>
    </el-form-item>

    <el-form-item label="保险附加费" prop="">
      <div class="flex items-center">
        <div>保险金额(USD)*</div>
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
          class="!w-120 ml-5"
        ></el-input>
        <el-checkbox label="计算燃费" :value="1" class="ml-10" />
      </div>
    </el-form-item>

    <el-form-item label="旺季附加费" prop="">
      <div>
        <div class="flex items-center">
          <div>
            <div>附加费类型</div>
            <div class="flex">
              <el-select
                v-model="formData.name"
                clearable
                placeholder=""
                class="!w-150"
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </div>
          </div>

          <div class="ml-10">
            <div>生效日期</div>
            <div class="flex">
              <el-date-picker
                v-model="formData.name"
                type="daterange"
                placeholder=""
              />
            </div>
          </div>

          <div class="ml-10">
            <div>费用</div>
            <div class="flex">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="!w-120"
              ></el-input>
            </div>
          </div>

          <el-checkbox label="计算燃费" :value="1" class="ml-10 mt-32" />
        </div>
        <div class="mt-12">
          <el-button type="primary" plain>添加费用</el-button>
        </div>
      </div>
    </el-form-item>

    <el-form-item label="面单取消手续费" prop="">
      <div class="flex items-center">
        <div>
          <div>计算类型</div>
          <div class="flex">
            <el-select
              v-model="formData.name"
              clearable
              placeholder=""
              class="!w-150"
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </div>
        </div>

        <div class="ml-10">
          <div>费用</div>
          <div class="flex">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              class="!w-120"
            >
              <template #suffix>
                <span>%</span>
              </template>
            </el-input>
          </div>
        </div>

        <div class="ml-10">
          <div>费用类型</div>
          <div class="flex">
            <el-select
              v-model="formData.name"
              clearable
              placeholder=""
              class="!w-150"
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </div>
        </div>
      </div>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped></style>
