<script setup lang="ts">
import { useToggle } from '@/hooks/useToggle'
import IntroducePartitionRules from './IntroducePartitionRules.vue'

defineOptions({
  name: 'TabFirst'
})

const dataList = ref([
  {
    id: 1
  }
])

const [iprVisible, handleIPR] = useToggle()
</script>

<template>
  <div class="mt-12">
    <el-button type="primary" @click="() => handleIPR()"
      >引入分区规则</el-button
    >
    <el-button>导入分区</el-button>
  </div>
  <div class="mt-24 bg-[#e8f4ff] p-12 font-14 flex items-center">
    <div class="i-ep:info-filled color-#0f44ff text-16 mr-10"></div>
    <div class="text-14">
      默认支持邮编的区间段匹配，如果要按匹配邮编的前几位（截取）来匹配，则需要使用通配符“*”，不使用通配符“*”则表示不截取，需要完整匹配
    </div>
  </div>

  <el-table :data="dataList" :border="true" class="w-full mt-24">
    <!-- <el-table-column type="selection" width="55" /> -->
    <el-table-column label="分区" prop="id">
      <template #default="{ row }">
        <el-input v-model.trim="row.id" clearable placeholder=""></el-input>
      </template>
    </el-table-column>
    <el-table-column label="国家/地区" prop="parentId">
      <template #default="{ row }">
        <el-input v-model.trim="row.id" clearable placeholder=""></el-input>
      </template>
    </el-table-column>
    <el-table-column label="城市" prop="status">
      <template #default="{ row }">
        <el-input v-model.trim="row.id" clearable placeholder=""></el-input>
      </template>
    </el-table-column>
    <el-table-column label="开始邮编" prop="status">
      <template #default="{ row }">
        <el-input v-model.trim="row.id" clearable placeholder=""></el-input>
      </template>
    </el-table-column>
    <el-table-column label="结束邮编" prop="status">
      <template #default="{ row }">
        <el-input v-model.trim="row.id" clearable placeholder=""></el-input>
      </template>
    </el-table-column>
    <el-table-column label="操作" fixed="right" width="320">
      <template #default="{}">
        <el-button type="primary" text>编辑</el-button>

        <el-button type="primary" text>插入行</el-button>

        <el-button type="danger" text>移除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <IntroducePartitionRules v-model="iprVisible"></IntroducePartitionRules>
</template>

<style lang="scss" scoped></style>
