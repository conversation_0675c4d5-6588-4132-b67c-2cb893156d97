<script setup lang="ts">
defineOptions({
  name: 'IntroducePartitionRules'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const initialFormData = () => {
  return {
    id: undefined,
    parentId: undefined,
    name: undefined,
    description: undefined,
    level: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const handleOpen = () => {}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      emits('refresh')
      handleCancel()
    }
  })
}

const tableData = ref([
  {
    name: 'zs'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="选择分区规则"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="" label-width="0" prop="">
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
          class="!w-150"
        ></el-input>
        <el-button type="primary" plain class="ml-8">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      style="width: 100%"
      class="mt-12"
    >
      <el-table-column label="规则名称" prop="name" />
      <el-table-column label="分区数量" prop="name" />
      <el-table-column label="备注" prop="name" />
      <el-table-column label="操作" prop="name">
        <template #default="{}">
          <el-button type="primary" text>选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
