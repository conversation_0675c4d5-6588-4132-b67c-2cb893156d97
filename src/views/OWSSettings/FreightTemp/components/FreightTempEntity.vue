<script setup lang="ts">
import { deleteCategory, getCategoryList } from '@/api/category'
import type { CategoryEntity } from '@/api/category/types'
import { useTable } from '@/hooks/useTable'
import TabFirst from './TabFirst/index.vue'
import TabSecond from './TabSecond/index.vue'
import TabThird from './TabThird/index.vue'

defineOptions({
  name: 'FreightTempEntity'
})

const {
  // viewEntity,
  // total, loading, dataList
  tableState: { pageSize, currentPage },
  // tableMethods,
  formData
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})

const active = ref(1)
</script>

<template>
  <ContentWrap title="基础信息">
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="物流费模版名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单位" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="体积重" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="体积重计算条件" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="体积重系数" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="尺寸进位" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'进位'" :value="1"></el-radio>
              <el-radio :label="'不进位'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="燃油规则" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="重量进位" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'进位'" :value="1"></el-radio>
              <el-radio :label="'不进位'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs v-model="active">
      <el-tab-pane label="分区" :name="1">
        <TabFirst></TabFirst>
      </el-tab-pane>
      <el-tab-pane label="重量段加个" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
      <el-tab-pane label="附加费" :name="3">
        <TabThird></TabThird>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex justify-center">
      <el-button>返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
