<script setup lang="ts">
import TabFirst from './components/TabFirst.vue'
import TabSecond from './components/TabSecond.vue'
import TabThird from './components/TabThird.vue'
defineOptions({
  name: 'WarehouseRent'
})

const formData = ref({
  name: ''
})

const active = ref(1)
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="创建时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="仓租费名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary">搜索</el-button>
            <el-button>重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs v-model="active">
      <el-tab-pane label="产品库存仓租" :name="1">
        <TabFirst></TabFirst>
      </el-tab-pane>
      <el-tab-pane label="箱库存仓租" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
      <el-tab-pane label="退货库存仓租" :name="3">
        <TabThird></TabThird>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
