<script setup lang="ts">
import { useClipboard } from '@/hooks/useClipboard'

defineOptions({
  name: 'TabThird'
})

const dataList = ref([
  {
    name: '张三'
  }
])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const router = useRouter()

const linkeCreate = () => {
  router.push({
    path: '/OWSSettings/warehouseRent/create'
  })
}

const { copy } = useClipboard()

const handleCopy = (row: any) => {
  copy(row.id)
  ElMessage.success('复制成功')
}
</script>

<template>
  <div class="mb-20">
    <el-button type="primary" @click="linkeCreate">新建仓租费</el-button>
  </div>

  <el-table ref="tableRef" :data="dataList" :border="true" class="w-full">
    <el-table-column
      label="仓租费规则"
      prop="transportServiceName"
      min-width="120"
    >
      <template #default="{ row }">
        <div class="flex items-center">
          <span class="color-#0f44ff cursor-pointer">{{ row.name }}</span>
          <div
            class="i-ep:document-copy w-16px h-16px ml-5 cursor-pointer"
            @click="() => handleCopy(row)"
          ></div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="币种" prop="chargeModeName" />
    <el-table-column label="计算方式" prop="weightModeName" />
    <el-table-column label="计算单位" prop="weightModeName" />
    <el-table-column label="关联的应收报价方案" prop="weightModeName" />
    <el-table-column label="关联的应付报价方案" prop="weightModeName" />
    <el-table-column label="状态" prop="destinationTypeName" />
    <el-table-column label="创建时间" prop="destinationTypeName" />

    <el-table-column label="操作" fixed="right" width="200">
      <template #default="{}">
        <el-button text type="primary">快速复制</el-button>
        <el-button text type="primary">更新</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="[10, 20, 50, 100]"
    layout="total, ->, sizes, prev, pager, next, jumper"
    :total="total"
    class="mt-16"
  />
</template>

<style lang="scss" scoped></style>
