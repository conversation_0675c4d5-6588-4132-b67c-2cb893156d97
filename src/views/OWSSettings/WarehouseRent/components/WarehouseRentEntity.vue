<script setup lang="ts">
import { deleteCategory, getCategoryList } from '@/api/category'
import type { CategoryEntity } from '@/api/category/types'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'WarehouseRentEntity'
})

const {
  // viewEntity,
  // total, loading, dataList
  tableState: { pageSize, currentPage, loading, dataList },
  // tableMethods,
  formData
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})
</script>

<template>
  <ContentWrap title="基础信息">
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="仓租费名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计算方式" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="最小计费体积" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计算单位" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="批次体积进位" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap title="规则设置" class="mt-20">
    <el-table
      :data="dataList"
      v-loading="loading"
      :border="true"
      style="width: 100%"
    >
      <el-table-column label="库龄" prop="date" width="180" />
      <el-table-column label="一月" prop="date" width="180" />
      <el-table-column label="二月" prop="date" width="180" />
      <el-table-column label="三月" prop="date" width="180" />
      <el-table-column label="四月" prop="date" width="180" />
      <el-table-column label="五月" prop="date" width="180" />
      <el-table-column label="六月" prop="date" width="180" />
      <el-table-column label="七月" prop="date" width="180" />
      <el-table-column label="八月" prop="date" width="180" />
      <el-table-column label="九月" prop="date" width="180" />
      <el-table-column label="十月" prop="date" width="180" />
      <el-table-column label="十一月" prop="date" width="180" />
      <el-table-column label="十二月" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button type="primary" text>设置整行</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex justify-center">
      <el-button>返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
