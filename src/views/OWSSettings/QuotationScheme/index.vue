<script setup lang="ts">
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'QuotationScheme'
})

const warehouse_type = useDictItem('warehouse_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

const {
  // viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: '1'
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()

const options = [
  {
    label: '应收',
    value: '1'
  },
  {
    label: '应付',
    value: '2'
  },
  {
    label: '隐藏',
    value: '3'
  }
]
const router = useRouter()
const linkCreate = () => {
  router.push({
    path: '/OWSSettings/QuotationScheme/create'
  })
}
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.destinationType"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="仓库" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="报价方案名称" prop="">
            <el-input
              v-model.trim="formData.arrivalCountry"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item prop="">
            <template #label>
              <div class="h-22"></div>
            </template>
            <div class="flex w-full">
              <el-select
                v-model="formData.name"
                clearable
                placeholder=""
                class="!w-120"
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
              <el-date-picker
                v-model="formData.name"
                type="daterange"
                placeholder=""
                class="flex-1"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <div class="w-full flex justify-end mt-32">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => linkCreate()"
        >新建报价方案</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column
        label="报价方案名称"
        prop="transportServiceName"
        width="180"
      />
      <el-table-column label="仓库" prop="transportServiceCode" width="180" />
      <el-table-column label="客户" prop="chargeModeName" width="180" />
      <el-table-column label="状态" prop="weightModeName" width="180" />
      <el-table-column label="类型" prop="bubbleCoefficient" width="180" />
      <el-table-column label="客户等级" prop="arrivalCountry" width="180" />
      <el-table-column label="生效时间" prop="arrivalCountry" width="180" />
      <el-table-column label="失效时间" prop="arrivalCountry" width="180" />
      <el-table-column label="备注" prop="arrivalCountry" width="180" />
      <el-table-column
        label="最后更新人"
        prop="destinationTypeName"
        width="180"
      />
      <el-table-column prop="destinationTypeName" width="180">
        <template #header>
          <div>创建时间</div>
          <div>更新时间</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button
            text
            type="primary"
            @click="() => tableMethods.handleEdit(row)"
            >审核</el-button
          >
          <el-button text type="primary">复制</el-button>
          <el-button text type="primary">编辑</el-button>
          <el-button text type="primary">删除</el-button>
          <el-button text type="primary">隐藏</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
