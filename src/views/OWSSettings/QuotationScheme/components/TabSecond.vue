<script setup lang="ts">
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'TabSecond'
})
const active = ref(1)

const {
  tableState: { pageSize, currentPage, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})
</script>

<template>
  <el-tabs v-model="active" type="card">
    <el-tab-pane label="入库" :name="1">
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        :border="true"
        class="w-full"
        @selection-change="val => tableMethods.handleSelectionChange(val)"
      >
        <el-table-column type="selection" fixed="left" width="55" />
        <el-table-column label="操作费名称" prop="transportServiceName">
          <template #default="{ row }">
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column label="单据类型" prop="transportServiceCode">
        </el-table-column>
        <el-table-column label="币种" prop="weightModeName" width="180" />
        <el-table-column label="备注" prop="weightModeName" width="180" />
        <el-table-column
          label="最后更新时间"
          prop="weightModeName"
          width="180"
        />
        <el-table-column label="调整" prop="weightModeName" width="180" />
      </el-table>

      <div class="mt-10 flex items-center">
        <el-popover placement="right" :width="400" trigger="click">
          <template #reference>
            <el-button type="primary" plain>
              <div class="flex items-center">
                <div>已选择0项</div>
                <div class="i-ep:arrow-down text-16 ml-4"></div>
              </div>
            </el-button>
          </template>
          <el-table :data="[]" :border="true">
            <el-table-column property="计费项名称" label="date" />
            <el-table-column property="单据类型" label="name" />
          </el-table>
        </el-popover>

        <el-button type="primary" text class="ml-10">新增</el-button>
      </div>
    </el-tab-pane>
    <el-tab-pane label="入库" :name="2">
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        :border="true"
        class="w-full"
        @selection-change="val => tableMethods.handleSelectionChange(val)"
      >
        <el-table-column type="selection" fixed="left" width="55" />
        <el-table-column label="操作费名称" prop="transportServiceName">
          <template #default="{ row }">
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column label="单据类型" prop="transportServiceCode">
        </el-table-column>
        <el-table-column label="币种" prop="weightModeName" width="180" />
        <el-table-column label="备注" prop="weightModeName" width="180" />
        <el-table-column
          label="最后更新时间"
          prop="weightModeName"
          width="180"
        />
        <el-table-column label="调整" prop="weightModeName" width="180" />
      </el-table>

      <div class="mt-10 flex items-center">
        <el-popover placement="right" :width="400" trigger="click">
          <template #reference>
            <el-button type="primary" plain>
              <div class="flex items-center">
                <div>已选择0项</div>
                <div class="i-ep:arrow-down text-16 ml-4"></div>
              </div>
            </el-button>
          </template>
          <el-table :data="[]" :border="true">
            <el-table-column property="计费项名称" label="date" />
            <el-table-column property="单据类型" label="name" />
          </el-table>
        </el-popover>

        <el-button type="primary" text class="ml-10">新增</el-button>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped></style>
