<script setup lang="ts">
import { deleteCategory, getCategoryList } from '@/api/category'
import type { CategoryEntity } from '@/api/category/types'
import { useTable } from '@/hooks/useTable'
import TabFirst from './TabFirst.vue'
import TabSecond from './TabSecond.vue'
import TabThird from './TabThird.vue'

defineOptions({
  name: 'FreightTempEntity'
})

const {
  // viewEntity,
  // total, loading, dataList
  tableState: { pageSize, currentPage },
  // tableMethods,
  formData
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})

const active = ref(1)
</script>

<template>
  <ContentWrap title="基础信息">
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="报价方案名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库结算币种" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="生效日期范围" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs v-model="active">
      <el-tab-pane label="物流费" :name="1">
        <TabFirst></TabFirst>
      </el-tab-pane>
      <el-tab-pane label="操作费设置" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
      <el-tab-pane label="仓租" :name="3">
        <TabThird></TabThird>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex justify-center">
      <el-button>返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
