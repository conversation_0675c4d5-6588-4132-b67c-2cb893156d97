<script setup lang="ts">
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'TabFirst'
})

const {
  tableState: { pageSize, currentPage, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})
</script>

<template>
  <el-table
    v-loading="loading"
    ref="tableRef"
    :data="dataList"
    :border="true"
    class="w-full"
    @selection-change="val => tableMethods.handleSelectionChange(val)"
  >
    <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
    <el-table-column label="物流渠道" prop="transportServiceName">
      <template #default="{ row }">
        <el-select v-model="row.name" clearable placeholder="">
          <el-option :value="1" label="1"></el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="物流费名称" prop="transportServiceCode">
      <template #default="{ row }">
        <el-select v-model="row.name" clearable placeholder="">
          <el-option :value="1" label="1"></el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="调整" prop="chargeModeName" width="180" />
    <el-table-column label="币种" prop="weightModeName" width="180" />
    <el-table-column label="备注" prop="weightModeName" width="180" />
    <el-table-column label="最后更新时间" prop="weightModeName" width="180" />

    <el-table-column label="操作" fixed="right" width="260">
      <template #default="{}">
        <el-button text type="danger">移除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <el-button type="primary" text class="mt-10">添加</el-button>
</template>

<style lang="scss" scoped></style>
