<script setup lang="ts">
import {
  findLogisticsProviderInfo,
  saveLogisticsProvider,
  updateLogisticsProvider
} from '@/api/LSA'
import type { LogisticsProviderEntity } from '@/api/LSA/types'
// import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const user_status = inject('user_status') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<LogisticsProviderEntity>(
    {
      id: undefined,
      logisticsProviderName: undefined,
      account: undefined,
      remark: undefined,
      info1: undefined,
      info2: undefined,
      info3: undefined,
      info4: undefined,
      warehouse: [],
      status: undefined
    },
    {
      logisticsProviderName: [
        { required: true, message: '请输入物流商', trigger: 'blur' }
      ],
      account: [{ required: true, message: '请输入账号名称', trigger: 'blur' }]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '添加授权',
  EDIT: '编辑授权',
  DETAIL: '运输服务详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveLogisticsProvider,
  EDIT: updateLogisticsProvider,
  DETAIL: findLogisticsProviderInfo
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    findLogisticsProviderInfo(viewEntity.record.id).then((res: any) => {
      formData.value = res.result
    })
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="物流商" prop="logisticsProviderName">
              <DSelect
                v-model="formData.logisticsProviderName"
                :options="[{ itemText: '顺丰', itemValue: '顺丰' }]"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号名称" prop="account">
              <el-input
                v-model.trim="formData.account"
                clearable
                placeholder="请输入账号"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态">
              <DSelect
                v-model="formData.status"
                :options="user_status"
                placeholder="请选择状态"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model.trim="formData.remark"
                clearable
                type="textarea"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>

          <el-divider />

          <el-col :span="12">
            <el-form-item label="扩展字段1" prop="info1">
              <el-input
                v-model.trim="formData.info1"
                clearable
                placeholder="请输入扩展字段1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展字段2" prop="info2">
              <el-input
                v-model.trim="formData.info2"
                clearable
                placeholder="请输入扩展字段2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展字段3" prop="info3">
              <el-input
                v-model.trim="formData.info3"
                clearable
                placeholder="请输入扩展字段3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展字段4" prop="info4">
              <el-input
                v-model.trim="formData.info4"
                clearable
                placeholder="请输入扩展字段4"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
