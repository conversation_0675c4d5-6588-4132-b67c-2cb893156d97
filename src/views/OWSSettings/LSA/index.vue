<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import ViewRecords from './components/ViewRecords.vue'
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'
import {
  findLogisticsProviderList,
  changeLogisticsProviderStatus
} from '@/api/LSA'
import type { LogisticsProviderEntity } from '@/api/LSA/types'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'InitialServiceSettings'
})

const user_status = useDictItem('user_status')
provide('user_status', user_status)

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)

provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<LogisticsProviderEntity>({
  immediate: true,
  initialFormData: {
    logisticsProviderName: undefined,
    account: undefined,
    status: ''
  },
  fetchDataApi: async () => {
    const res = await findLogisticsProviderList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const tableRef = ref()

const [vrVisible, handleVr] = useToggle()

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: '1'
  },
  {
    label: '停用',
    value: '0'
  },
  {
    label: '隐藏',
    value: '2'
  }
]

// 处理状态变更
const handleStatusChange = async (
  row: LogisticsProviderEntity,
  status: string
) => {
  try {
    await changeLogisticsProviderStatus({
      id: row.id!,
      status
    })
    ElMessage.success('状态更新成功')
    tableMethods.handleQuery()
  } catch {
    ElMessage.error('状态更新失败')
  }
}
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.status"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="账户名称" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="物流商" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="授权状态" prop="">
            <DSelect
              v-model="formData.arrivalCountry"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <div class="w-full flex justify-end mt-32">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >添加授权</el-button
      >
      <el-button @click="() => handleVr()">查看记录</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
      <el-table-column label="账户名称" prop="account" width="180" />
      <el-table-column
        label="物流商"
        prop="logisticsProviderName"
        width="180"
      />
      <el-table-column label="归属主仓" prop="warehouse" width="180" />
      <el-table-column label="授权状态" prop="authStatus" width="180" />
      <el-table-column label="子服务数量" prop="subServiceCount" width="180" />
      <el-table-column label="状态" prop="statusName" width="180" />
      <el-table-column label="备注" prop="remark" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button
            link
            type="primary"
            @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            v-if="row.status === '0'"
            link
            type="primary"
            @click="() => handleStatusChange(row, '1')"
            >启用</el-button
          >
          <el-button
            v-if="row.status === '1'"
            link
            type="warning"
            @click="() => handleStatusChange(row, '0')"
            >停用</el-button
          >

          <el-button
            v-if="formData.status === '2' || row.status === '2'"
            link
            type="primary"
            @click="() => handleStatusChange(row, '1')"
            >恢复</el-button
          >
          <el-button
            v-if="formData.status !== '2' && row.status !== '2'"
            link
            type="primary"
            @click="() => handleStatusChange(row, '2')"
            >隐藏</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <ViewRecords v-model="vrVisible"></ViewRecords>
</template>

<style lang="scss" scoped></style>
