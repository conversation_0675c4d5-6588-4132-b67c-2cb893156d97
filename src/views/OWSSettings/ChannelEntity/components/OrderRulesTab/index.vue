<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  findListSystemChannelCondition,
  changeStatusSystemChannelCondition
} from '@/api/channelEntity'
import type { SystemChannelConditionVo } from '@/api/channelEntity/index.d'
import { useToggle } from '@/hooks/useToggle'
import AddRules from './AddRules.vue'

defineOptions({
  name: 'OrderRulesTab'
})

// 生效模式
const effectMode = ref(1) // 1: 满足所有条件, 2: 满足任一条件
// 单位模式
const unitMode = ref(1) // 1: kg,cn,CMB, 2: lb,in,CBF

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<SystemChannelConditionVo>({
  immediate: true,
  initialFormData: {},
  fetchDataApi: async () => {
    const res = await findListSystemChannelCondition({
      pageSize: pageSize.value,
      pageNum: currentPage.value
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      // 批量删除：将状态设置为删除状态
      const promises = record.map((item: any) =>
        changeStatusSystemChannelCondition({
          id: item.id,
          status: '0' // 0表示删除状态
        })
      )
      const results = await Promise.all(promises)
      return results.every((res: any) => !!res)
    } else {
      // 单个删除：将状态设置为删除状态
      const res = await changeStatusSystemChannelCondition({
        id: record.id,
        status: '0'
      })
      return !!res
    }
  }
})

const tableRef = ref()
const [accVisible, handleAcc] = useToggle()
const [editVisible, handleEdit] = useToggle()
const currentEditRecord = ref<SystemChannelConditionVo | null>(null)

// 处理编辑
const handleEditRecord = (record: SystemChannelConditionVo) => {
  currentEditRecord.value = record
  handleEdit()
}

// 处理删除
const handleDeleteRecord = async (record: SystemChannelConditionVo) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await tableMethods.hadnleDel(record)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除或删除失败
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理添加成功
const handleAddSuccess = () => {
  tableMethods.getList()
}

// 处理编辑成功
const handleEditSuccess = () => {
  tableMethods.getList()
  currentEditRecord.value = null
}
</script>

<template>
  <div>
    <div class="mb-18 flex justify-between w-full">
      <el-button @click="() => handleAcc()">添加条件</el-button>

      <div class="flex">
        <div class="flex items-center">
          <span class="mr-6">生效模式:</span>
          <DRadioGroup
            v-model="effectMode"
            :options="[
              { itemText: '满足所有条件', itemValue: 1 },
              { itemText: '满足任一条件', itemValue: 2 }
            ]"
          ></DRadioGroup>
        </div>
        <div class="flex items-center ml-10">
          <span class="mr-6">单位:</span>
          <DRadioGroup
            v-model="unitMode"
            :options="[
              { itemText: 'kg,cn,CMB', itemValue: 1 },
              { itemText: 'lb,in,CBF', itemValue: 2 }
            ]"
          ></DRadioGroup>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="序号" width="80">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="条件详情">
        <template #default="{ row }">
          <span v-if="row.condition && row.calculation && row.value">
            {{ row.condition }} {{ row.calculation }} {{ row.value
            }}{{ row.unit ? ` ${row.unit}` : '' }}
          </span>
          <span v-else class="text-gray-400">暂无条件详情</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="160">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEditRecord(row)"
            >编辑</el-button
          >
          <el-button link type="danger" @click="handleDeleteRecord(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <!-- 添加条件弹窗 -->
    <AddRules v-model="accVisible" @success="handleAddSuccess" />

    <!-- 编辑条件弹窗 -->
    <AddRules
      v-model="editVisible"
      :edit-data="currentEditRecord"
      @success="handleEditSuccess"
    />
  </div>
</template>

<style lang="scss" scoped></style>
