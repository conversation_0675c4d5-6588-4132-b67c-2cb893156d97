<script setup lang="ts">
defineOptions({
  name: 'ChannelEntityForm'
})

// 定义表单数据类型
interface FormData {
  id?: string
  name?: string
  code?: string
  logisticsProviderId?: string
  apiServiceCode?: string
  carrier?: string
  insuranceServices?: string
  signatureService?: string
  status?: string
}

// 定义 Props
const props = defineProps<{
  modelValue: FormData
}>()

// 定义 Emits
const emit = defineEmits<{
  'update:modelValue': [value: FormData]
}>()

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

// 获取字典数据（如果需要的话可以在这里添加）

// 表单引用
const formDataRef = ref()

// 暴露表单方法给父组件
defineExpose({
  formRef: formDataRef,
  validate: () => formDataRef.value?.validate(),
  resetFields: () => formDataRef.value?.resetFields(),
  clearValidate: () => formDataRef.value?.clearValidate()
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="left"
    @submit.prevent
  >
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="物流商账号" prop="logisticsProviderId">
          <el-input
            v-model.trim="formData.logisticsProviderId"
            clearable
            placeholder="请输入物流商账号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道代码" prop="code">
          <el-input
            v-model.trim="formData.code"
            clearable
            placeholder="请输入物流渠道代码"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道名称" prop="name">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder="请输入物流渠道名称"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="API服务代码" prop="apiServiceCode">
          <el-input
            v-model.trim="formData.apiServiceCode"
            clearable
            placeholder="请输入API服务代码"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="承运商" prop="carrier">
          <el-input
            v-model.trim="formData.carrier"
            clearable
            placeholder="请输入承运商"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="保险服务" prop="insuranceServices">
          <el-radio-group v-model="formData.insuranceServices">
            <el-radio label="支持保险" value="0" />
            <el-radio label="不支持保险" value="1" />
          </el-radio-group>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="签名服务" prop="signatureService">
          <el-select
            v-model="formData.signatureService"
            placeholder="请选择签名服务"
            clearable
          >
            <el-option label="直接签名" value="0" />
            <el-option label="间接签名" value="1" />
            <el-option label="成人签名" value="2" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="启用" value="1" />
            <el-radio label="停用" value="0" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
