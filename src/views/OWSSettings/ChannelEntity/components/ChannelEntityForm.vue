<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'ChannelEntityForm'
})

// 定义表单数据类型
interface FormData {
  transportServiceName?: string
  transportServiceCode?: string
  chargeMode?: string
  weightMode?: string
  arrivalCountry?: string
  destinationType: string
  insuranceService?: number
  signatureService?: number[]
  uploadWaybill?: number
  platformWaybill?: number
  customerUploadWaybill?: number
}

// 定义 Props
const props = defineProps<{
  modelValue: FormData
}>()

// 定义 Emits
const emit = defineEmits<{
  'update:modelValue': [value: FormData]
}>()

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

// 获取字典数据
const charge_mode = useDictItem('charge_mode')
const weight_mode = useDictItem('weight_mode')

// 表单引用
const formDataRef = ref()

// 暴露表单方法给父组件
defineExpose({
  formRef: formDataRef,
  validate: () => formDataRef.value?.validate(),
  resetFields: () => formDataRef.value?.resetFields(),
  clearValidate: () => formDataRef.value?.clearValidate()
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="left"
    @submit.prevent
  >
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="物流商账号" prop="chargeMode">
          <DSelect
            v-model="formData.chargeMode"
            :options="charge_mode"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道代码" prop="transportServiceCode">
          <el-input
            v-model.trim="formData.transportServiceCode"
            clearable
            placeholder="请输入物流渠道代码"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道名称" prop="transportServiceName">
          <el-input
            v-model.trim="formData.transportServiceName"
            clearable
            placeholder="请输入物流渠道名称"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="API服务代码" prop="transportServiceName">
          <el-input
            v-model.trim="formData.transportServiceName"
            clearable
            placeholder="请输入物流渠道名称"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="承运商" prop="weightMode">
          <DSelect
            v-model="formData.weightMode"
            :options="weight_mode"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="保险服务" prop="insuranceService">
          <el-radio-group v-model="formData.insuranceService">
            <el-radio :label="'支持保险'" :value="1" />
            <el-radio :label="'不支持保险'" :value="2" />
          </el-radio-group>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="签名服务" prop="signatureService">
          <el-checkbox-group v-model="formData.signatureService">
            <el-checkbox label="直接签名" :value="1" />
            <el-checkbox label="间接签名" :value="2" />
            <el-checkbox label="成人签名" :value="3" />
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
