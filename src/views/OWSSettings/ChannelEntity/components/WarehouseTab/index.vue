<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'
import AddWarehouse from './AddWarehouse.vue'

defineOptions({
  name: 'WarehouseTab'
})

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()
const [awVisible, handleAw] = useToggle()
</script>

<template>
  <div>
    <div class="mb-18">
      <el-button @click="() => handleAw()">添加仓库</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="仓库代码" prop="transportServiceName" />
      <el-table-column label="仓库名称" prop="transportServiceCode" />
      <el-table-column label="结算币种" prop="chargeModeName" width="180" />
      <el-table-column label="状态" prop="weightModeName" width="180" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button text type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddWarehouse v-model="awVisible" />
  </div>
</template>

<style lang="scss" scoped></style>
