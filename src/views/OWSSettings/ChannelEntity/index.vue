<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'
import WarehouseTab from './components/WarehouseTab/index.vue'
import ShippingAddressTab from './components/ShippingAddressTab/index.vue'
import OrderRulesTab from './components/OrderRulesTab/index.vue'
import ChannelEntityForm from './components/ChannelEntityForm.vue'

defineOptions({
  name: 'ChannelEntity'
})

// 定义表单数据类型
interface FormData {
  transportServiceName?: string
  transportServiceCode?: string
  chargeMode?: string
  weightMode?: string
  arrivalCountry?: string
  destinationType: string
  insuranceService?: number
  signatureService?: number[]
  uploadWaybill?: number
  platformWaybill?: number
  customerUploadWaybill?: number
}

// 字典数据
const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')
const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

// 提供字典数据给子组件
provide('warehouse_type', warehouse_type)
provide('service_provider_type', service_provider_type)
provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

// Tab 状态
const active = ref('1')

// 表单数据
const formData = ref<FormData>({
  transportServiceName: undefined,
  transportServiceCode: undefined,
  chargeMode: undefined,
  weightMode: undefined,
  arrivalCountry: undefined,
  destinationType: '',
  insuranceService: undefined,
  signatureService: [],
  uploadWaybill: undefined,
  platformWaybill: undefined,
  customerUploadWaybill: undefined
})

// 表单组件引用
const formRef = ref()

// 表单相关方法
// const handleFormSubmit = async () => {
//   try {
//     const valid = await formRef.value?.validate()
//     if (valid) {
//       console.log('表单数据:', formData.value)
//       // 这里可以添加提交逻辑
//     }
//   } catch (error) {
//     console.error('表单验证失败:', error)
//   }
// }

// const handleFormReset = () => {
//   formRef.value?.resetFields()
// }

// const handleFormClear = () => {
//   formRef.value?.clearValidate()
// }
</script>

<template>
  <!-- 表单组件 -->
  <ContentWrap>
    <ChannelEntityForm v-model="formData" ref="formRef" />
  </ContentWrap>

  <!-- Tab 组件 -->
  <ContentWrap class="mt-20">
    <el-tabs v-model="active" class="mb-16">
      <el-tab-pane label="绑定仓库" name="1">
        <WarehouseTab />
      </el-tab-pane>
      <el-tab-pane label="发货地址" name="2">
        <ShippingAddressTab />
      </el-tab-pane>
      <el-tab-pane label="下单规则" name="3">
        <OrderRulesTab />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="h-60 flex items-center justify-center">
      <el-button type="primary">保存</el-button>

      <el-button>取消</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
