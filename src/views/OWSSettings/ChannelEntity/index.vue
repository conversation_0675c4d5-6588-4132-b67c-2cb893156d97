<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'
import WarehouseTab from './components/WarehouseTab/index.vue'
import ShippingAddressTab from './components/ShippingAddressTab/index.vue'
import OrderRulesTab from './components/OrderRulesTab/index.vue'
import ChannelEntityForm from './components/ChannelEntityForm.vue'
import {
  saveSystemChannel,
  updateSystemChannel,
  findInfoSystemChannel
} from '@/api/systemChannel'

defineOptions({
  name: 'ChannelEntity'
})

// 定义表单数据类型
interface FormData {
  id?: string
  name?: string
  code?: string
  logisticsProviderId?: string
  apiServiceCode?: string
  carrier?: string
  insuranceServices?: string
  signatureService?: string[]
}

// 字典数据
const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')
const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

// 提供字典数据给子组件
provide('warehouse_type', warehouse_type)
provide('service_provider_type', service_provider_type)
provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

// Tab 状态
const active = ref('1')

// 表单数据
const formData = ref<FormData>({
  id: undefined,
  name: undefined,
  code: undefined,
  logisticsProviderId: undefined,
  apiServiceCode: undefined,
  carrier: undefined,
  insuranceServices: undefined,
  signatureService: []
})

// 表单组件引用
const formRef = ref()

// 获取路由参数
const route = useRoute()
const router = useRouter()
const isEdit = computed(() => route.query.mode === 'edit')
const channelId = computed(() => route.query.id as string)

// 页面初始化
onMounted(async () => {
  if (isEdit.value && channelId.value) {
    try {
      const res = await findInfoSystemChannel({ id: channelId.value })
      if (res.result) {
        // 处理数据转换
        const data = res.result
        formData.value = {
          ...data,
          // 将字符串转换为数组（如果signatureService是逗号分隔的字符串）
          signatureService: data.signatureService
            ? data.signatureService.split(',')
            : []
        }
      }
    } catch {
      ElMessage.error('获取渠道信息失败')
    }
  }
})

// 保存表单
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (valid) {
      // 处理数据转换，将数组转换为字符串
      const submitData = {
        ...formData.value,
        signatureService: formData.value.signatureService?.join(',') || ''
      }

      if (isEdit.value) {
        await updateSystemChannel(submitData)
        ElMessage.success('更新成功')
      } else {
        await saveSystemChannel(submitData)
        ElMessage.success('创建成功')
      }
      router.push('/OWSSettings/SystemChannel')
    }
  } catch {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/OWSSettings/SystemChannel')
}
</script>

<template>
  <!-- 表单组件 -->
  <ContentWrap>
    <ChannelEntityForm v-model="formData" ref="formRef" />
  </ContentWrap>

  <!-- Tab 组件 -->
  <ContentWrap class="mt-20">
    <el-tabs v-model="active" class="mb-16">
      <el-tab-pane label="绑定仓库" name="1">
        <WarehouseTab />
      </el-tab-pane>
      <el-tab-pane label="发货地址" name="2">
        <ShippingAddressTab />
      </el-tab-pane>
      <el-tab-pane label="下单规则" name="3">
        <OrderRulesTab />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="h-60 flex items-center justify-center">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
