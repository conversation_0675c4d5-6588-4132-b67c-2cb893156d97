<script setup lang="ts">
import {
  saveSystemChannel,
  updateSystemChannel,
  findInfoSystemChannel
} from '@/api/systemChannel'
import type { SystemChannelModel } from '@/api/systemChannel/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const warehouse_type = inject('warehouse_type') as DictItemEntity[]
// const weight_mode = inject('weight_mode') as DictItemEntity[]
// const charge_mode = inject('charge_mode') as DictItemEntity[]

// const destination_type = useDictItem('destination_type')
// const round_mode = useDictItem('round_mode')
// const charge_weight_mode = useDictItem('charge_weight_mode')

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<SystemChannelModel>(
    {
      id: undefined,
      name: undefined,
      code: undefined,
      logisticsProviderId: undefined,
      apiServiceCode: undefined,
      carrier: undefined,
      insuranceServices: undefined,
      signatureService: undefined,
      status: undefined
    },
    {
      name: [
        { required: true, message: '请输入物流渠道名称', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入物流渠道代码', trigger: 'blur' }
      ],
      logisticsProviderId: [
        { required: true, message: '请选择物流商', trigger: 'change' }
      ],
      carrier: [{ required: true, message: '请输入承运商', trigger: 'blur' }]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新建系统渠道',
  EDIT: '编辑系统渠道',
  DETAIL: '系统渠道详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveSystemChannel,
  EDIT: updateSystemChannel,
  DETAIL: findInfoSystemChannel
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    // formData.value = Object.assign(formData.value, viewEntity.record)

    getFirstVesselSettingById(viewEntity.record.id).then((res: any) => {
      formData.value = res.result
      formData.value.ids = [res.result.id]
    })
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="物流商" prop="arrivalCountry">
              <DSelect
                v-model="formData.arrivalCountry"
                :options="warehouse_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="transportServiceCode">
              <el-input
                v-model.trim="formData.transportServiceCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备注">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                type="textarea"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-divider />
          <el-col :span="12">
            <el-form-item label="信息1">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息2">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息3">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息4">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
