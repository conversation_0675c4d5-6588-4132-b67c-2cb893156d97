<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'
import { deleteFirstVesselSetting } from '@/api/initialServiceSettings'
import { findListSystemChannel } from '@/api/systemChannel'
import type { SystemChannelModel } from '@/api/systemChannel/types'

defineOptions({
  name: 'SystemChannel'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)

provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<SystemChannelModel>({
  immediate: true,
  initialFormData: {},
  fetchDataApi: async () => {
    const res = await findListSystemChannel({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const router = useRouter()
const linkChannel = () => {
  router.push('/OWSSettings/ChannelEntity')
}

const tableRef = ref()

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: 'platformWarehouse'
  },
  {
    label: '停用',
    value: 'overseasWarehouse'
  },
  {
    label: '隐藏',
    value: 'otherAddress'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.destinationType"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="物流渠道代码" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物流渠道名称" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="承运商" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="物流商账号" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <!-- label-width="0" -->
          <el-form-item label-width="0">
            <template #label></template>
            <div class="flex w-full">
              <el-select
                v-model="formData.currency"
                clearable
                placeholder=""
                class="!w-120"
              >
                <el-option :value="1" label="创建时间"></el-option>
              </el-select>
              <el-date-picker
                v-model="formData.currency"
                type="daterange"
                start-placeholder="请选择"
                end-placeholder="请选择"
                class="flex-1"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <div class="w-full flex justify-end mt-32">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="linkChannel">新建渠道</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
      <el-table-column
        label="系统物流渠道名称"
        prop="logisticsProviderId"
        width="180"
      />
      <el-table-column
        label="系统物流渠道代码"
        prop="transportServiceCode"
        width="180"
      />
      <el-table-column label="物流商账号" prop="chargeModeName" width="180" />
      <el-table-column
        label="物流商账号状态"
        prop="weightModeName"
        width="180"
      />
      <el-table-column
        label="API服务代码"
        prop="bubbleCoefficient"
        width="180"
      />
      <el-table-column label="承运商" prop="carrier" width="180" />
      <el-table-column
        label="绑定仓库数量"
        prop="destinationTypeName"
        width="180"
      />
      <el-table-column
        label="最后更新人"
        prop="destinationTypeName"
        width="180"
      />
      <el-table-column prop="destinationTypeName" width="180">
        <template #header>
          <div>
            <div>创建时间</div>
            <div>更新时间</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="destinationTypeName" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button
            text
            type="primary"
            @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            v-if="row.status === '0'"
            text
            type="primary"
            @click="() => tableMethods.handleDetail(row)"
            >启用</el-button
          >
          <el-button
            v-if="row.status === '1'"
            text
            type="warning"
            @click="() => tableMethods.handleDetail(row)"
            >停用</el-button
          >
          <el-button text type="primary">隐藏</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
</template>

<style lang="scss" scoped></style>
