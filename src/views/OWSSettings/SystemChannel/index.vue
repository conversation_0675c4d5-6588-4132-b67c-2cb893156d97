<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'
import {
  findListSystemChannel,
  changeStatusSystemChannel
} from '@/api/systemChannel'
import type { SystemChannelModel } from '@/api/systemChannel/types'

defineOptions({
  name: 'SystemChannel'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)

provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<SystemChannelModel>({
  immediate: true,
  initialFormData: {},
  fetchDataApi: async () => {
    // 根据当前tab设置状态参数
    let statusParam = undefined

    if (formData.destinationType === 'platformWarehouse') {
      // 启用tab：查询启用状态的记录
      statusParam = '1'
    } else if (formData.destinationType === 'overseasWarehouse') {
      // 停用tab：查询停用状态的记录
      statusParam = '0'
    } else if (formData.destinationType === 'otherAddress') {
      // 隐藏tab：查询隐藏状态的记录
      statusParam = '2'
    }
    // 全部tab：不设置状态参数，后端返回所有非删除状态的记录

    const res = await findListSystemChannel({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData,
      status: statusParam // 将状态参数传递给后端
    })

    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const router = useRouter()
const linkChannel = () => {
  router.push('/OWSSettings/ChannelEntity')
}

// 编辑渠道
const handleEdit = (row: any) => {
  router.push({
    path: '/OWSSettings/ChannelEntity',
    query: { id: row.id, mode: 'edit' }
  })
}

// 状态切换处理
const handleStatusChange = async (row: any, newStatus: string) => {
  try {
    await changeStatusSystemChannel({
      id: row.id,
      status: newStatus
    })

    const statusMap: Record<string, string> = {
      '1': '启用',
      '0': '停用',
      '2': '隐藏'
    }

    ElMessage.success(`${statusMap[newStatus]}成功`)
    tableMethods.handleQuery()
  } catch {
    ElMessage.error('状态更新失败')
  }
}

// 恢复处理（从隐藏状态恢复到启用状态）
const handleRestore = async (row: any) => {
  try {
    await changeStatusSystemChannel({
      id: row.id,
      status: '1' // 恢复为启用状态
    })
    ElMessage.success('恢复成功')
    tableMethods.handleQuery()
  } catch {
    ElMessage.error('恢复失败')
  }
}

const tableRef = ref()

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: '1'
  },
  {
    label: '停用',
    value: '0'
  },
  {
    label: '隐藏',
    value: 'otherAddress'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.destinationType"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="物流渠道代码" prop="code">
            <el-input
              v-model.trim="formData.code"
              clearable
              placeholder="请输入物流渠道代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物流渠道名称" prop="name">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder="请输入物流渠道名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="承运商" prop="carrier">
            <el-input
              v-model.trim="formData.carrier"
              clearable
              placeholder="请输入承运商"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="物流商账号" prop="logisticsProviderId">
            <el-input
              v-model.trim="formData.logisticsProviderId"
              clearable
              placeholder="请输入物流商账号"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="formData.createTime"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-full"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <div class="w-full flex justify-end mt-32">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="linkChannel">新建渠道</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
      <el-table-column label="系统物流渠道名称" prop="name" width="180" />
      <el-table-column label="系统物流渠道代码" prop="code" width="180" />
      <el-table-column
        label="物流商账号"
        prop="logisticsProviderId"
        width="180"
      />
      <el-table-column label="物流商账号状态" prop="status" width="180">
        <template #default="{ row }">
          <el-tag v-if="row.status === '1'" type="success">启用</el-tag>
          <el-tag v-else-if="row.status === '0'" type="danger">停用</el-tag>
          <el-tag v-else type="info">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="API服务代码" prop="apiServiceCode" width="180" />
      <el-table-column label="承运商" prop="carrier" width="180" />
      <el-table-column label="绑定仓库数量" prop="warehouseCount" width="180">
        <template #default="{ row }">
          {{ row.warehouseCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="最后更新人" prop="updateUser" width="180" />
      <el-table-column prop="createTime" width="180">
        <template #header>
          <div>
            <div>创建时间</div>
            <div>更新时间</div>
          </div>
        </template>
        <template #default="{ row }">
          <div>
            <div>{{ row.createTime }}</div>
            <div>{{ row.updateTime }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="180">
        <template #default="{ row }">
          <el-tag v-if="row.status === '1'" type="success">启用</el-tag>
          <el-tag v-else-if="row.status === '0'" type="danger">停用</el-tag>
          <el-tag v-else type="info">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="300">
        <template #default="{ row }">
          <el-button text type="primary" @click="() => handleEdit(row)"
            >编辑</el-button
          >

          <!-- 在全部和隐藏tab下显示恢复按钮 -->
          <el-button
            v-if="
              (formData.destinationType === '' ||
                formData.destinationType === 'otherAddress') &&
              row.status === '2'
            "
            text
            type="success"
            @click="() => handleRestore(row)"
            >恢复</el-button
          >

          <!-- 启用按钮：在停用状态下显示，在隐藏tab下不显示 -->
          <el-button
            v-if="
              row.status === '0' && formData.destinationType !== 'otherAddress'
            "
            text
            type="primary"
            @click="() => handleStatusChange(row, '1')"
            >启用</el-button
          >

          <!-- 停用按钮：在启用状态下显示，在隐藏tab下不显示 -->
          <el-button
            v-if="
              row.status === '1' && formData.destinationType !== 'otherAddress'
            "
            text
            type="warning"
            @click="() => handleStatusChange(row, '0')"
            >停用</el-button
          >

          <!-- 隐藏按钮：在启用或停用状态下显示，在隐藏tab下不显示 -->
          <el-button
            v-if="
              (row.status === '1' || row.status === '0') &&
              formData.destinationType !== 'otherAddress'
            "
            text
            type="info"
            @click="() => handleStatusChange(row, '2')"
            >隐藏</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
