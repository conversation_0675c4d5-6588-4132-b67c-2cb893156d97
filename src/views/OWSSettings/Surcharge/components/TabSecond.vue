<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import QueryForm from './QueryForm.vue'
import TSCreateEditDialog from './TSCreateEditDialog.vue'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'

defineOptions({
  name: 'TabSecond'
})
const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})
const router = useRouter()
const linkDetail = () => {
  router.push('/OWSSettings/surcharge/farawayDetail')
}
</script>

<template>
  <QueryForm></QueryForm>
  <div class="mb-20">
    <el-button type="primary" @click="() => tableMethods.handleAdd()"
      >新建规则</el-button
    >
  </div>

  <el-table
    ref="tableRef"
    :data="dataList"
    v-loading="loading"
    :border="true"
    class="w-full"
  >
    <el-table-column
      label="规则名称"
      prop="transportServiceName"
      min-width="120"
    />
    <el-table-column label="类型" prop="transportServiceCode" />
    <el-table-column label="状态" prop="chargeModeName" />
    <el-table-column label="明细数量" prop="weightModeName" />
    <el-table-column label="备注" prop="bubbleCoefficient" />
    <el-table-column label="最后更新人" prop="destinationTypeName" />
    <el-table-column label="最后更新时间" prop="destinationTypeName" />

    <el-table-column label="操作" fixed="right" width="360">
      <template #default="{ row }">
        <el-button
          text
          type="primary"
          @click="() => tableMethods.handleEdit(row)"
          >编辑</el-button
        >
        <el-button text type="primary" @click="() => linkDetail()"
          >偏远明细</el-button
        >
        <el-button text type="primary">导出明细</el-button>

        <el-button text type="danger">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="[10, 20, 50, 100]"
    layout="total, ->, sizes, prev, pager, next, jumper"
    :total="total"
    class="mt-16"
  />

  <TSCreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></TSCreateEditDialog>
</template>

<style lang="scss" scoped></style>
