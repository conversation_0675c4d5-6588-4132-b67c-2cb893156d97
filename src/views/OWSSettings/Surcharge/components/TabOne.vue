<script setup lang="ts">
import QueryForm from './QueryForm.vue'

defineOptions({
  name: 'TabOne'
})

const dataList = ref([
  {
    name: '张三'
  }
])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const router = useRouter()

const linkeCreate = () => {
  router.push({
    path: '/OWSSettings/surcharge/overlengthCreate'
  })
}

const linkeEdit = () => {
  router.push({
    path: '/OWSSettings/surcharge/overlengthEdit'
  })
}
</script>

<template>
  <QueryForm></QueryForm>
  <div class="mb-20">
    <el-button type="primary" @click="linkeCreate">新建规则</el-button>
  </div>

  <el-table ref="tableRef" :data="dataList" :border="true" class="w-full">
    <el-table-column
      label="规则名称"
      prop="transportServiceName"
      min-width="120"
    />
    <el-table-column label="类型" prop="transportServiceCode" />
    <el-table-column label="状态" prop="chargeModeName" />
    <el-table-column label="单位" prop="weightModeName" />
    <el-table-column label="备注" prop="bubbleCoefficient" />
    <el-table-column label="最后更新人" prop="destinationTypeName" />
    <el-table-column label="最后更新时间" prop="destinationTypeName" />

    <el-table-column label="操作" fixed="right" width="180">
      <template #default="{}">
        <el-button text type="primary" @click="linkeEdit">编辑</el-button>

        <el-button text type="danger">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="[10, 20, 50, 100]"
    layout="total, ->, sizes, prev, pager, next, jumper"
    :total="total"
    class="mt-16"
  />
</template>

<style lang="scss" scoped></style>
