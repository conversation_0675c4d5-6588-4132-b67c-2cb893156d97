<script setup lang="ts">
defineOptions({
  name: 'QueryForm'
})
const formData = ref({
  name: ''
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="top"
    @submit.prevent
  >
    <el-row :gutter="8">
      <el-col :span="6">
        <el-form-item label="类型" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="状态" prop="">
          <el-select v-model="formData.name" clearable placeholder="">
            <el-option :value="1" label="1"></el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="规则名称" prop="">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder=""
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <div class="w-full flex justify-end mt-32">
          <el-button type="primary">搜索</el-button>
          <el-button>重置</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
