<script setup lang="ts">
import tabOne from './components/TabOne.vue'
import TabSecond from './components/TabSecond.vue'
defineOptions({
  name: 'Surcharge'
})
const active = ref(1)
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="active">
      <el-tab-pane label="超长/超重规则" :name="1">
        <tabOne></tabOne>
      </el-tab-pane>
      <el-tab-pane label="偏远地区规则" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
