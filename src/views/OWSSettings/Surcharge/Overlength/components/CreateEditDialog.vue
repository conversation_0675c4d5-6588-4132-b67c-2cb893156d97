<script setup lang="ts">
import {
  getFirstVesselSettingById,
  saveFirstVesselSetting,
  updateFirstVesselSetting
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const warehouse_type = inject('warehouse_type') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<FirstVesselSettingEntity>(
    {
      id: undefined,
      transportServiceName: undefined,
      transportServiceCode: undefined,
      chargeMode: undefined,
      weightMode: undefined,
      roundMode: undefined,
      bubbleCoefficient: undefined,
      chargeWeightMode: undefined,
      ticketWeightPrecision: undefined,
      boxWeightPrecision: undefined,
      sizePrecision: undefined,
      minBoxRealWeight: undefined,
      minBoxMaterialWeight: undefined,
      minBoxChargeWeight: undefined,
      minTicketChargeWeight: undefined,
      arrivalCountry: undefined,
      customer: undefined,
      destinationType: undefined,
      status: undefined,
      ids: undefined
    },
    {
      transportServiceName: [
        { required: true, message: '请输入运输服务名称', trigger: 'blur' }
      ],
      arrivalCountry: [
        { required: true, message: '请选择到达国家', trigger: 'change' }
      ],
      transportServiceCode: [
        { required: true, message: '请输入服务代码', trigger: 'blur' }
      ],
      chargeMode: [
        { required: true, message: '请选择计费方式', trigger: 'change' }
      ],
      weightMode: [
        { required: true, message: '请选择计重方式', trigger: 'change' }
      ],
      roundMode: [
        { required: true, message: '请选择进位方式', trigger: 'change' }
      ],
      bubbleCoefficient: [
        { required: true, message: '请选择计泡系数', trigger: 'change' }
      ],
      chargeWeightMode: [
        { required: true, message: '请选择收费重方式', trigger: 'change' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新建规则',
  EDIT: '编辑规则',
  DETAIL: '规则详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveFirstVesselSetting,
  EDIT: updateFirstVesselSetting,
  DETAIL: getFirstVesselSettingById
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    // formData.value = Object.assign(formData.value, viewEntity.record)

    getFirstVesselSettingById(viewEntity.record.id).then((res: any) => {
      formData.value = res.result
      formData.value.ids = [res.result.id]
    })
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}

const router = useRouter()
const linkCreate = () => {
  router.push('/OWSSettings/surcharge/farawayCreate')
}

const handleConfirm = () => {
  linkCreate()
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="left"
        label-width="80"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="24">
            <el-form-item label="如果" prop="transportServiceCode">
              <div class="flex">
                <DSelect
                  v-model="formData.arrivalCountry"
                  :options="warehouse_type"
                  placeholder="请选择"
                  class="!w-120 mr-10"
                ></DSelect>
                <DSelect
                  v-model="formData.arrivalCountry"
                  :options="warehouse_type"
                  placeholder="请选择"
                  class="!w-120 mr-10"
                ></DSelect>
                <el-input
                  v-model.trim="formData.transportServiceCode"
                  clearable
                  placeholder="请输入"
                  class="flex-1"
                >
                  <template #append>in</template>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <template #label>
                <el-dropdown>
                  <div class="el-dropdown-link flex items-center lh-32">
                    且
                    <div class="i-ep:arrow-down ml-5"></div>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>且</el-dropdown-item>
                      <el-dropdown-item>或</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
              <div class="flex">
                <DSelect
                  v-model="formData.arrivalCountry"
                  :options="warehouse_type"
                  placeholder="请选择"
                  class="!w-120 mr-10"
                ></DSelect>
                <DSelect
                  v-model="formData.arrivalCountry"
                  :options="warehouse_type"
                  placeholder="请选择"
                  class="!w-120 mr-10"
                ></DSelect>
                <el-input
                  v-model.trim="formData.transportServiceCode"
                  clearable
                  placeholder="请输入"
                  class="flex-1"
                >
                  <template #append>in</template>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <div>
          <el-button type="primary" text>添加</el-button>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 下一步 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
