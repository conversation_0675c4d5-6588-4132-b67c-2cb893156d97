<script setup lang="ts">
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './CreateEditDialog.vue'
import FjfTemlate from './FjfTemlate.vue'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'FarawayEntity'
})
// const formData = ref({
//   name: ''
// })

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const [fjfVisible, handleFjf] = useToggle()
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="规则名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="规则类型" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单位" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="规则关系" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :offset="6" :span="6">
          <div class="w-full flex justify-end mt-32">
            <el-button type="primary">搜索</el-button>
            <el-button>重置</el-button>
          </div>
        </el-col> -->
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新建</el-button
      >
      <el-button @click="() => handleFjf()">引用模板</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
      <el-table-column label="NO." prop="transportServiceName" width="180" />
      <el-table-column label="规则详情" prop="transportServiceCode" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button
            text
            type="primary"
            @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >

          <el-button text type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="w-full flex justify-center">
      <el-button>返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <FjfTemlate v-model="fjfVisible" :view-entity="viewEntity"></FjfTemlate>
</template>

<style lang="scss" scoped></style>
