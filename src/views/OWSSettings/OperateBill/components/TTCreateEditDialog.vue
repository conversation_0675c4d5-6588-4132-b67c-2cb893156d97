<script setup lang="ts">
import { saveCategory, updateCategory } from '@/api/category'

defineOptions({
  name: 'TTCreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    parentId: undefined,
    name: undefined,
    description: undefined,
    level: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const titleMap: Record<DlViewType, string> = {
  ADD: '新建',
  EDIT: '编辑',
  DETAIL: '详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveCategory,
  EDIT: updateCategory,
  DETAIL: () => Promise.resolve({})
}

const handleOpen = () => {}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar>
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="20" class="w-full">
          <el-col :span="12">
            <el-form-item label="计费项名称" prop="">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="单据类型" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="币种" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计算单位(重量)" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计算单位(体积)" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备注" prop="">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                type="textarea"
                :rows="1"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="应收计费变量" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="单价" prop="">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="成本计费变量" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="单价" prop="">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
