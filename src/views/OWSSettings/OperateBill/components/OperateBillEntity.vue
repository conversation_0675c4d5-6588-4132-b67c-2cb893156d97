<script setup lang="ts">
import { deleteCategory, getCategoryList } from '@/api/category'
import type { CategoryEntity } from '@/api/category/types'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'OperateBillEntity'
})

const {
  // viewEntity,
  // total, loading, dataList
  tableState: { pageSize, currentPage, loading, dataList },
  // tableMethods,
  formData
} = useTable<CategoryEntity>({
  immediate: true,
  initialFormData: {
    name: undefined,
    level: undefined
  },
  fetchDataApi: async () => {
    const res = await getCategoryList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: CategoryEntity) => {
    // if (Array.isArray(record)) {
    //   const ids = record.map((item: any) => item.id?.toString())
    //   const res = await deleteCategory(ids)
    //   return !!res
    // } else {
    //   const res = await deleteCategory([record.id])
    //   return !!res
    // }
    const res = await deleteCategory({ id: record.id })
    return !!res
  }
})
</script>

<template>
  <ContentWrap title="基础信息">
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="计费项名称" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据类型" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计算单位(重量)" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="重量计算方式" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计算单位(体积)" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="尺寸进位" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'进位'" :value="1"></el-radio>
              <el-radio :label="'不进位'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="备注" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap title="规则设置" class="mt-20">
    <div class="mb-20">
      <el-button type="primary">新增</el-button>
      <el-button>全部清空</el-button>
    </div>

    <el-table
      :data="dataList"
      v-loading="loading"
      :border="true"
      style="width: 100%"
    >
      <el-table-column label="条件变量" prop="date" width="180" />
      <el-table-column label="设置方式" prop="date" width="180" />
      <el-table-column label="变量值" prop="date" width="180" />
      <el-table-column label="计费变量" prop="date" width="180" />
      <el-table-column label="减免量" prop="date" width="180" />
      <el-table-column label="单位数量" prop="date" width="180" />
      <el-table-column label="是否进位" prop="date" width="180" />
      <el-table-column label="单价" prop="date" width="180" />
      <el-table-column label="基础费" prop="date" width="180" />
      <el-table-column label="最低收费" prop="date" width="180" />
      <el-table-column label="最高收费" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button type="primary" text>新增条件</el-button>
          <el-button type="primary" text>复制</el-button>
          <el-button type="danger" text>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="flex justify-center">
      <el-button>返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
