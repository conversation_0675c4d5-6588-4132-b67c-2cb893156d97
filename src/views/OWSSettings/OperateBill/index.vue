<script setup lang="ts">
import TabFirst from './components/TabFirst.vue'
import TabSecond from './components/TabSecond.vue'
import TabThird from './components/TabThird.vue'
defineOptions({
  name: 'OperateBill'
})
const active = ref(1)
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="active">
      <el-tab-pane label="入库" :name="1">
        <TabFirst></TabFirst>
      </el-tab-pane>
      <el-tab-pane label="出库" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
      <el-tab-pane label="增值服务" :name="3">
        <TabThird></TabThird>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
