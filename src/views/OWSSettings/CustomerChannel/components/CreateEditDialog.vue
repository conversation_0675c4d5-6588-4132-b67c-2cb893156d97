<script setup lang="ts">
import {
  getFirstVesselSettingById,
  saveFirstVesselSetting,
  updateFirstVesselSetting
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
// import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const warehouse_type = inject('warehouse_type') as DictItemEntity[]
// const weight_mode = inject('weight_mode') as DictItemEntity[]
// const charge_mode = inject('charge_mode') as DictItemEntity[]

// const destination_type = useDictItem('destination_type')
// const round_mode = useDictItem('round_mode')
// const charge_weight_mode = useDictItem('charge_weight_mode')

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<FirstVesselSettingEntity>(
    {
      id: undefined,
      transportServiceName: undefined,
      transportServiceCode: undefined,
      chargeMode: undefined,
      weightMode: undefined,
      roundMode: undefined,
      bubbleCoefficient: undefined,
      chargeWeightMode: undefined,
      ticketWeightPrecision: undefined,
      boxWeightPrecision: undefined,
      sizePrecision: undefined,
      minBoxRealWeight: undefined,
      minBoxMaterialWeight: undefined,
      minBoxChargeWeight: undefined,
      minTicketChargeWeight: undefined,
      arrivalCountry: undefined,
      customer: undefined,
      destinationType: undefined,
      status: undefined,
      ids: undefined
    },
    {
      transportServiceName: [
        { required: true, message: '请输入运输服务名称', trigger: 'blur' }
      ],
      arrivalCountry: [
        { required: true, message: '请选择到达国家', trigger: 'change' }
      ],
      transportServiceCode: [
        { required: true, message: '请输入服务代码', trigger: 'blur' }
      ],
      chargeMode: [
        { required: true, message: '请选择计费方式', trigger: 'change' }
      ],
      weightMode: [
        { required: true, message: '请选择计重方式', trigger: 'change' }
      ],
      roundMode: [
        { required: true, message: '请选择进位方式', trigger: 'change' }
      ],
      bubbleCoefficient: [
        { required: true, message: '请选择计泡系数', trigger: 'change' }
      ],
      chargeWeightMode: [
        { required: true, message: '请选择收费重方式', trigger: 'change' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '添加授权',
  EDIT: '编辑授权',
  DETAIL: '运输服务详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveFirstVesselSetting,
  EDIT: updateFirstVesselSetting,
  DETAIL: getFirstVesselSettingById
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    // formData.value = Object.assign(formData.value, viewEntity.record)

    getFirstVesselSettingById(viewEntity.record.id).then((res: any) => {
      formData.value = res.result
      formData.value.ids = [res.result.id]
    })
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="物流商" prop="arrivalCountry">
              <DSelect
                v-model="formData.arrivalCountry"
                :options="warehouse_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="transportServiceCode">
              <el-input
                v-model.trim="formData.transportServiceCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备注">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                type="textarea"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-divider />
          <el-col :span="12">
            <el-form-item label="信息1">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息2">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息3">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息4">
              <el-input
                v-model.number.trim="formData.minTicketChargeWeight"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
