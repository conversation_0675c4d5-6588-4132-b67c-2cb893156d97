<script setup lang="ts">
import { getCurrencyHistoryRecordList } from '@/api/currency'
import type { CurrencyEntity } from '@/api/currency/types'
import { useTable } from '@/hooks/useTable'

defineOptions({
  name: 'ViewRecords'
})
const viewVisible = defineModel({ default: false })

// const selection = inject('selection') as Ref<any>

const {
  tableState: { pageSize, currentPage, loading, dataList },
  // tableMethods,
  formData
} = useTable<CurrencyEntity>({
  immediate: false,
  initialFormData: {
    ids: undefined,
    baseCurrencyCode: undefined,
    currencyCode: undefined
  },
  fetchDataApi: async () => {
    const res = await getCurrencyHistoryRecordList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const handleOpen = () => {
  // console.log('selection', selection.value)
  // formData.ids = selection.value.map((item: any) => item.id)
  // tableMethods.getList()
}
const handleClose = () => {
  viewVisible.value = false
}
const handleCancel = () => {
  viewVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="查看记录"
    width="70%"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full px-16">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="left"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="6">
            <el-form-item label="基准币种">
              <el-select
                v-model="formData.baseCurrencyCode"
                clearable
                placeholder=""
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="币种">
              <el-select
                v-model="formData.currencyCode"
                clearable
                placeholder=""
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label-width="0">
              <template #label></template>
              <div class="flex w-full">
                <el-select
                  v-model="formData.currency"
                  clearable
                  placeholder=""
                  class="!w-120 ml--12"
                >
                  <el-option :value="1" label="1"></el-option>
                </el-select>
                <el-date-picker
                  v-model="formData.currency"
                  type="daterange"
                  placeholder=""
                  class="flex-1"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="mr--16">
              <div class="w-full flex justify-end">
                <el-button type="primary">搜索</el-button>
                <el-button>重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-loading="loading" :data="dataList" :border="true">
        <el-table-column label="操作时间" prop="date" width="180" />
        <el-table-column label="基准币种" prop="date" width="180" />
        <el-table-column label="币种" prop="date" width="180" />
        <el-table-column label="操作人" prop="date" width="180" />
        <el-table-column label="操作IP" prop="date" width="180" />
        <el-table-column label="操作设备" prop="date" width="180" />
        <el-table-column label="操作前" prop="date" min-width="180" />
        <el-table-column label="操作后" prop="date" min-width="180" />
      </el-table>
      <!-- <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, ->, sizes, prev, pager, next, jumper"
        :total="total"
        class="mt-16"
      /> -->
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">关闭</el-button>
        <!-- <el-button type="primary" @click="handleConfirm"> 确认 </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
