<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import { getOverseasWarehouseChannel } from '@/api/warehouseProxyConfiguration'
import type {
  OverseasWareHouseModel,
  WarehouseAgentVo
} from '@/api/warehouseProxyConfiguration/types'

defineOptions({
  name: 'LogisticsChannelTab'
})

// 接收父组件传递的选中仓库信息
const props = defineProps<{
  selectedWarehouse?: WarehouseAgentVo
}>()

// 使用 useTable hook 管理表格数据和分页
const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<OverseasWareHouseModel>({
  immediate: false, // 不立即加载，等待选中仓库后再加载
  initialFormData: {
    pageSize: 10,
    pageNum: 1,
    whCode: undefined,
    whNameCn: undefined,
    status: undefined,
    agentId: undefined,
    channelCode: undefined,
    channelName: undefined,
    sysChannelName: undefined,
    customer: undefined
  },
  fetchDataApi: async () => {
    if (!props.selectedWarehouse?.id) {
      return { list: [], total: 0 }
    }

    const params: OverseasWareHouseModel = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData,
      agentId: props.selectedWarehouse.id
    }

    const res = await getOverseasWarehouseChannel(params)
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

// 监听选中仓库变化，重新加载数据
watch(
  () => props.selectedWarehouse,
  newWarehouse => {
    if (newWarehouse?.id) {
      tableMethods.getList()
    } else {
      // 清空数据
      dataList.value = []
      total.value = 0
    }
  },
  { immediate: true }
)
</script>

<template>
  <div>
    <el-table
      :data="dataList"
      :border="true"
      :loading="loading"
      style="width: 100%"
      :height="'450px'"
    >
      <el-table-column label="主仓渠道名称" prop="channelName" />
      <el-table-column label="主仓渠道代码" prop="channelCode" />
      <el-table-column label="主仓仓库名称" prop="warehouseName" />
      <el-table-column label="主仓仓库代码" prop="warehouseCode" />
      <el-table-column label="系统物流渠道" prop="sysChannelName" />
      <el-table-column label="操作" width="260">
        <template #default>
          <el-button type="primary" text>编辑</el-button>
          <el-button type="primary" text>取消</el-button>
          <el-button type="primary" text>确认</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </div>
</template>

<style lang="scss" scoped></style>
