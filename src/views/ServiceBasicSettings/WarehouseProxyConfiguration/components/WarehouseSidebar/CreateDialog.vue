<script setup lang="ts">
import {
  saveWarehouseAgent,
  updateWarehouseAgent
} from '@/api/warehouseProxyConfiguration'
import type {
  WarehouseAgentModel,
  WarehouseAgentVo
} from '@/api/warehouseProxyConfiguration/types'

defineOptions({
  name: 'CreateDialog'
})

const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity?: ViewEntity & { record?: WarehouseAgentVo }
}>()

const initialFormData = (): WarehouseAgentModel => {
  return {
    pageSize: 10,
    pageNum: 1,
    id: undefined,
    warehouseType: undefined,
    warehouseName: undefined,
    info1: undefined,
    info2: undefined
  }
}

const formData = ref<WarehouseAgentModel>(initialFormData())
const formDataRef = ref()
const loading = ref(false)

const rules = ref({
  warehouseType: [
    { required: true, message: '请选择主仓系统类型', trigger: 'change' }
  ],
  warehouseName: [
    { required: true, message: '请输入自定义主仓名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
})

const titleMap: Record<DlViewType, string> = {
  ADD: '添加主仓',
  EDIT: '编辑主仓',
  DETAIL: '主仓详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity?.type || 'ADD'] as string
})

const apiMap: Record<DlViewType, (data: WarehouseAgentModel) => Promise<any>> =
  {
    ADD: saveWarehouseAgent,
    EDIT: updateWarehouseAgent,
    DETAIL: () => Promise.resolve({})
  }

const disabled = computed(() => viewEntity?.type === 'DETAIL')

const handleOpen = () => {
  if (
    viewEntity &&
    ['EDIT', 'DETAIL'].includes(viewEntity.type) &&
    viewEntity.record
  ) {
    // 重置表单数据后再赋值，确保数据完整性
    formData.value = Object.assign(initialFormData(), viewEntity.record)
  } else {
    // 新增模式，重置表单数据
    formData.value = initialFormData()
  }
}

const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}

const handleCancel = () => {
  viewVisible.value = false
}

const handleConfirm = () => {
  if (disabled.value) {
    handleCancel()
    return
  }

  formDataRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true
        await apiMap[viewEntity?.type || 'ADD'](formData.value)
        ElMessage.success(viewEntity?.type === 'EDIT' ? '编辑成功' : '添加成功')
        emits('refresh')
        handleCancel()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error(viewEntity?.type === 'EDIT' ? '编辑失败' : '添加失败')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="500"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="left"
      label-width="130px"
      @submit.prevent
      class="w-90% mx-auto"
    >
      <el-form-item label="主仓系统类型" prop="warehouseType">
        <el-select
          v-model="formData.warehouseType"
          clearable
          placeholder="请选择主仓系统类型"
          :disabled="disabled"
          class="w-full"
        >
          <el-option value="leg" label="头程"></el-option>
          <el-option value="overseasPosition" label="海外仓"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="自定义主仓名称" prop="warehouseName">
        <el-input
          v-model.trim="formData.warehouseName"
          clearable
          placeholder="请输入自定义主仓名称"
          :disabled="disabled"
          maxlength="50"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="所需信息1" prop="info1">
        <el-input
          v-model.trim="formData.info1"
          clearable
          placeholder="请输入所需信息1"
          :disabled="disabled"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="所需信息2" prop="info2">
        <el-input
          v-model.trim="formData.info2"
          clearable
          placeholder="请输入所需信息2"
          :disabled="disabled"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ disabled ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
