<script setup lang="ts">
import { useToggle } from '@/hooks/useToggle'
import { findWarehouseAgentList } from '@/api/warehouseProxyConfiguration'
import type {
  WarehouseAgentModel,
  WarehouseAgentVo
} from '@/api/warehouseProxyConfiguration/types'
import CreateDialog from './CreateDialog.vue'

defineOptions({
  name: 'WarehouseSidebar'
})

// 定义事件
const emit = defineEmits<{
  selectWarehouse: [warehouse: WarehouseAgentVo]
}>()

// 弹窗控制
const [visible, handleVisible] = useToggle()

// 搜索关键词
const searchKeyword = ref('')

// 仓库列表数据
const warehouseList = ref<WarehouseAgentVo[]>([])
const loading = ref(false)
const selectedWarehouseId = ref<string>('')

// 弹窗视图实体
const viewEntity = ref<ViewEntity & { record?: WarehouseAgentVo }>({
  type: 'ADD',
  visible: false
})

// 查询参数
const queryParams = ref<WarehouseAgentModel>({
  pageSize: 100, // 侧边栏显示较多数据
  pageNum: 1,
  warehouseName: ''
})

/**
 * @description: 查询仓库列表
 */
const getWarehouseList = async () => {
  try {
    loading.value = true
    queryParams.value.warehouseName = searchKeyword.value

    const { result } = await findWarehouseAgentList(queryParams.value)
    warehouseList.value = result || []

    // 默认选中第一条数据
    if (warehouseList.value.length > 0 && !selectedWarehouseId.value) {
      handleSelectWarehouse(warehouseList.value[0])
    }
  } catch (error) {
    console.error('查询仓库列表失败:', error)
    ElMessage.error('查询仓库列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * @description: 选择仓库
 */
const handleSelectWarehouse = (warehouse: WarehouseAgentVo) => {
  selectedWarehouseId.value = warehouse.id || ''
  emit('selectWarehouse', warehouse)
}

/**
 * @description: 搜索框失焦事件
 */
const handleSearchBlur = () => {
  getWarehouseList()
}

/**
 * @description: 新增仓库
 */
const handleAddWarehouse = () => {
  viewEntity.value = {
    type: 'ADD',
    visible: true
  }
  handleVisible()
}

/**
 * @description: 编辑仓库
 */
const handleEditWarehouse = (warehouse: WarehouseAgentVo, event: Event) => {
  event.stopPropagation() // 阻止冒泡，避免触发选择事件
  viewEntity.value = {
    type: 'EDIT',
    visible: true,
    record: warehouse
  }
  handleVisible()
}

/**
 * @description: 刷新列表
 */
const handleRefresh = () => {
  getWarehouseList()
}

// 组件挂载时查询数据
onMounted(() => {
  getWarehouseList()
})
</script>

<template>
  <div class="w-300 h-full">
    <ContentWrap class="h-full">
      <div class="flex justify-between items-center mb-16">
        <div class="text-16 fw-600">仓库列表</div>
        <el-button type="primary" @click="handleAddWarehouse">
          添加主仓
        </el-button>
      </div>

      <!-- 搜索框 -->
      <div class="mb-16">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入仓库名称"
          clearable
          @blur="handleSearchBlur"
          @clear="handleSearchBlur"
        >
          <template #prefix>
            <div class="i-ep:search"></div>
          </template>
        </el-input>
      </div>

      <!-- 仓库列表 -->
      <div class="flex-1 overflow-auto">
        <el-scrollbar v-loading="loading">
          <div
            v-if="warehouseList.length === 0 && !loading"
            class="text-center py-20 text-gray-400"
          >
            暂无数据
          </div>
          <div
            v-for="warehouse in warehouseList"
            :key="warehouse.id"
            class="warehouse-item flex justify-between items-center p-12 mb-8 cursor-pointer rounded-6 transition-all"
            :class="{
              'warehouse-item--active': selectedWarehouseId === warehouse.id
            }"
            @click="handleSelectWarehouse(warehouse)"
          >
            <div class="flex-1 min-w-0">
              <div
                class="text-14 fw-500 truncate"
                :title="warehouse.warehouseName"
              >
                {{ warehouse.warehouseName || '未命名仓库' }}
              </div>
            </div>
            <div
              class="warehouse-edit-btn i-ep:edit-pen text-16 text-gray-400 hover:text-primary cursor-pointer ml-8"
              @click="handleEditWarehouse(warehouse, $event)"
            ></div>
          </div>
        </el-scrollbar>
      </div>
    </ContentWrap>
  </div>

  <CreateDialog
    v-model="visible"
    :view-entity="viewEntity"
    @refresh="handleRefresh"
  />
</template>

<style lang="scss" scoped>
.warehouse-item {
  border: 1px solid transparent;
  background-color: var(--el-bg-color-page);

  &:hover {
    border-color: var(--el-color-primary-light-7);
    background-color: var(--el-color-primary-light-9);
  }

  &--active {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);

    .warehouse-edit-btn {
      color: var(--el-color-primary);
    }
  }
}

.warehouse-edit-btn {
  transition: color 0.3s;
}
</style>
