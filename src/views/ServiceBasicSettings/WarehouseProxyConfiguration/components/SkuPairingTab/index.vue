<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  getOverseasWarehouseProduct,
  saveWarehouseProduct,
  deleteWarehouseProduct
} from '@/api/warehouseProxyConfiguration'
import type {
  WarehouseProductModel,
  WarehouseAgentVo,
  DeleteParams
} from '@/api/warehouseProxyConfiguration/types'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'SkuPairingTab'
})

const { productOptions } = useOptions(['productOptions'])

// 接收父组件传递的选中仓库信息
const props = defineProps<{
  selectedWarehouse?: WarehouseAgentVo
}>()

// 编辑状态管理 - 直接在行数据中添加编辑状态字段

// 使用useTable hook处理分页和查询逻辑
const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<WarehouseProductModel>({
  immediate: false, // 不立即加载，等待选中仓库后再加载
  initialFormData: {
    pageSize: 10,
    pageNum: 1,
    whCode: undefined,
    whNameCn: undefined,
    status: undefined,
    agentId: undefined,
    productName: undefined,
    sku: undefined,
    sysProductName: undefined,
    sysSku: undefined
  },
  fetchDataApi: async () => {
    if (!props.selectedWarehouse?.id) {
      return { list: [], total: 0 }
    }

    const params: WarehouseProductModel = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData,
      agentId: props.selectedWarehouse.id
    }

    const res = await getOverseasWarehouseProduct(params)
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

// 监听选中仓库变化，重新加载数据
watch(
  () => props.selectedWarehouse,
  newWarehouse => {
    if (newWarehouse?.id) {
      tableMethods.getList()
    } else {
      // 清空数据
      dataList.value = []
      total.value = 0
    }
    // 清空编辑状态
    dataList.value.forEach(row => {
      row.isEditing = false
    })
  },
  { immediate: true }
)

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  tableMethods.handleQuery()
}

// 重置处理
const handleReset = () => {
  tableMethods.handleReset()
}

// 判断行是否处于编辑状态
const isRowEditing = (row: any) => {
  return row.isEditing === true
}

// 解除配对
const handleUnpair = async (row: any) => {
  try {
    loading.value = true
    const params: DeleteParams = {
      whCode: row.whCode,
      agentId: props.selectedWarehouse?.id || ''
    }

    await deleteWarehouseProduct(params)
    ElMessage.success('解除配对成功')

    // 重新加载数据
    await tableMethods.getList()
  } catch (error) {
    console.error('解除配对失败:', error)
    ElMessage.error('解除配对失败')
  } finally {
    loading.value = false
  }
}

// 取消编辑
const handleCancel = (row: any) => {
  row.isEditing = false
  // 恢复原始值
  row.sysSku = row.originalSysSku
}

// 开始配对（进入编辑状态）
const handleStartPair = (row: any) => {
  row.isEditing = true
  // 保存原始值，用于取消时恢复
  row.originalSysSku = row.sysSku
}

// 确认配对
const handlePair = async (row: any) => {
  if (!row.sysSku) {
    ElMessage.warning('请选择系统SKU')
    return
  }

  const sysProductName = productOptions.value.find(
    (item: any) => item.productCode === row.sysSku
  )?.productName

  try {
    loading.value = true
    const params: WarehouseProductModel = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      whCode: row.whCode,
      agentId: props.selectedWarehouse?.id || '',
      productName: row.productName,
      sku: row.productSku,
      sysSku: row.sysSku,
      customer: row.customer,
      sysProductName: sysProductName
    }

    await saveWarehouseProduct(params)
    ElMessage.success('配对成功')

    // 退出编辑状态
    row.isEditing = false

    // 重新加载数据
    await tableMethods.getList()
  } catch (error) {
    console.error('配对失败:', error)
    ElMessage.error('配对失败')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between">
      <div>
        <el-button type="primary" plain>开启自动配对</el-button>
        <el-button>推送产品</el-button>
        <el-button>导入</el-button>
        <el-button>导出</el-button>
      </div>
      <div class="flex">
        <el-input
          v-model.trim="formData.productName"
          clearable
          placeholder="请输入主仓产品名称"
          class="!w-120"
        ></el-input>
        <el-input
          v-model.trim="formData.sku"
          clearable
          placeholder="请输入主仓SKU"
          class="ml-10 !w-120"
        ></el-input>
        <el-input
          v-model.trim="formData.sysProductName"
          clearable
          placeholder="请输入系统产品名称"
          class="ml-10 !w-120"
        ></el-input>

        <div class="flex ml-10">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      style="width: 100%"
      :height="'400px'"
      class="mt-16"
    >
      <el-table-column
        label="主仓产品名称"
        prop="productName"
        min-width="120"
      />
      <el-table-column label="主仓SKU" prop="productSku" />
      <el-table-column
        label="系统产品名称"
        prop="sysProductName"
        min-width="120"
      />
      <el-table-column label="系统SKU" prop="sysSku" min-width="140">
        <template #default="{ row }">
          <!-- 编辑状态：显示下拉选择器 -->
          <DSelect
            v-if="isRowEditing(row)"
            v-model="row.sysSku"
            :options="productOptions"
            placeholder="请选择"
            :fields="{
              label: 'productName',
              value: 'productCode'
            }"
          ></DSelect>
          <!-- 详情状态：显示文本 -->
          <span v-else>{{ row.sysSku || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户" prop="customer" />
      <el-table-column label="配对状态" prop="status" />
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <!-- 编辑状态：显示取消和配对按钮 -->
          <template v-if="isRowEditing(row)">
            <el-button type="info" link @click="handleCancel(row)">
              取消
            </el-button>
            <el-button type="primary" link @click="handlePair(row)">
              配对
            </el-button>
          </template>
          <!-- 详情状态：根据sysSku字段显示不同按钮 -->
          <template v-else>
            <!-- 已配对：显示sysSku文本和解除配对按钮 -->
            <template v-if="row.sysSku">
              <el-button type="danger" link @click="handleUnpair(row)">
                解除配对
              </el-button>
            </template>
            <!-- 未配对：显示配对按钮 -->
            <template v-else>
              <el-button type="primary" link @click="handleStartPair(row)">
                配对
              </el-button>
            </template>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </div>
</template>

<style lang="scss" scoped></style>
