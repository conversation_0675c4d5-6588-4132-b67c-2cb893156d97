<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTable } from '@/hooks/useTable'
import {
  getOverseasWarehouseMapping,
  deleteWarehouseMapping,
  saveWarehouseMapping
} from '@/api/warehouseProxyConfiguration'
import type {
  WarehouseMappingModel,
  WarehouseMappingVo,
  WarehouseAgentVo,
  DeleteParams
} from '@/api/warehouseProxyConfiguration/types'
import { baseWarehouseManageFindAll } from '@/api/common'

defineOptions({
  name: 'WarehouseMappingTab'
})

// 接收父组件传递的选中仓库信息
const props = defineProps<{
  selectedWarehouse?: WarehouseAgentVo
}>()

// 编辑状态管理 - 直接在行数据中添加编辑状态字段

// 使用 useTable hook 管理表格数据和分页
const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<WarehouseMappingModel>({
  immediate: false, // 不立即加载，等待选中仓库后再加载
  initialFormData: {
    pageSize: 10,
    pageNum: 1,
    whCode: undefined,
    whNameCn: undefined,
    status: undefined,
    agentId: undefined,
    warehouseCode: undefined,
    warehouseName: undefined
  },
  fetchDataApi: async () => {
    console.log('props.selectedWarehouse', props.selectedWarehouse)

    if (!props.selectedWarehouse?.id) {
      return { list: [], total: 0 }
    }

    const params: WarehouseMappingModel = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData,
      agentId: props.selectedWarehouse.id
    }

    const res = await getOverseasWarehouseMapping(params)
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

// 监听选中仓库变化，重新加载数据
watch(
  () => props.selectedWarehouse,
  newWarehouse => {
    if (newWarehouse?.id) {
      tableMethods.getList()
    } else {
      // 清空数据
      dataList.value = []
      total.value = 0
    }
    // 清空编辑状态
    dataList.value.forEach(row => {
      row.isEditing = false
    })
  },
  { immediate: true }
)

// 判断行是否处于编辑状态
const isRowEditing = (row: any) => {
  return row.isEditing === true
}

/**
 * @description: 处理解除配对操作
 */
const handleUnpair = async (row: WarehouseMappingVo) => {
  try {
    await ElMessageBox.confirm(
      `确定要解除 "${row.whNameCn || row.whCode}" 与 "${row.warehouseName || row.warehouseCode}" 的配对关系吗？`,
      '确认解除配对',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    // 调用删除接口
    const params: DeleteParams = {
      whCode: row.whCode || '',
      agentId: props?.selectedWarehouse?.id || '',
      id: row.id
    }

    await deleteWarehouseMapping(params)
    ElMessage.success('解除配对成功')
    await tableMethods.getList()
  } catch (error) {
    // 用户取消操作或接口调用失败
    console.error('解除配对失败:', error)
    if (error !== 'cancel') {
      ElMessage.error('解除配对失败')
    }
  } finally {
    loading.value = false
  }
}

// 开始配对（进入编辑状态）
const handleStartPair = (row: any) => {
  row.isEditing = true
  // 保存原始值，用于取消时恢复
  row.originalWarehouseCode = row.warehouseCode
}

// 取消编辑
const handleCancel = (row: any) => {
  row.isEditing = false
  // 恢复原始值
  row.warehouseCode = row.originalWarehouseCode
}

// 确认配对
const handlePair = async (row: any) => {
  if (!row.warehouseCode) {
    ElMessage.warning('请选择仓库')
    return
  }

  // 获取选中仓库的名称
  const selectedWarehouse = overseasDeliveryWarehouseOptions.value.find(
    (item: any) => item.warehouseCode === row.warehouseCode
  )

  try {
    await ElMessageBox.confirm(
      `确定要将 "${row.whNameCn || row.whCode}" 与 "${selectedWarehouse?.warehouseName || row.warehouseCode}" 进行配对吗？`,
      '确认配对',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    loading.value = true
    const params: WarehouseMappingModel = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      whCode: row.whCode,
      whNameCn: row.whNameCn,
      agentId: props.selectedWarehouse?.id || '',
      warehouseCode: row.warehouseCode,
      warehouseName: selectedWarehouse?.warehouseName,
      id: row.id
    }

    await saveWarehouseMapping(params)
    ElMessage.success('配对成功')

    // 退出编辑状态
    row.isEditing = false

    // 重新加载数据
    await tableMethods.getList()
  } catch (error) {
    console.error('配对失败:', error)
    if (error !== 'cancel') {
      ElMessage.error('配对失败')
    }
  } finally {
    loading.value = false
  }
}

const overseasDeliveryWarehouseOptions = ref<any[]>([])

const init = async () => {
  const res = await baseWarehouseManageFindAll('overseasDeliveryWarehouse')
  overseasDeliveryWarehouseOptions.value = res.result
}

init()
</script>

<template>
  <div>
    <el-table
      :data="dataList"
      :border="true"
      :loading="loading"
      style="width: 100%"
      :height="'450px'"
      empty-text="暂无数据"
    >
      <el-table-column label="主仓仓库名称" prop="whNameCn" />
      <el-table-column label="主仓仓库代码" prop="whCode" />
      <el-table-column label="仓库名称" prop="warehouseName">
        <template #default="{ row }">
          <!-- 编辑状态：显示下拉选择器 -->
          <DSelect
            v-if="isRowEditing(row)"
            v-model="row.warehouseCode"
            :options="overseasDeliveryWarehouseOptions"
            placeholder="请选择"
            :fields="{ label: 'warehouseName', value: 'warehouseCode' }"
          ></DSelect>
          <!-- 详情状态：显示文本 -->
          <span v-else>{{ row.warehouseName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <!-- 编辑状态：显示取消和配对按钮 -->
          <template v-if="isRowEditing(row)">
            <el-button type="info" link @click="handleCancel(row)">
              取消
            </el-button>
            <el-button type="primary" link @click="handlePair(row)">
              配对
            </el-button>
          </template>
          <!-- 详情状态：根据warehouseName字段显示不同按钮 -->
          <template v-else>
            <!-- 已配对：显示解除配对按钮 -->
            <template v-if="row.warehouseName">
              <el-button type="danger" link @click="handleUnpair(row)">
                解除配对
              </el-button>
            </template>
            <!-- 未配对：显示配对按钮 -->
            <template v-else>
              <el-button type="primary" link @click="handleStartPair(row)">
                配对
              </el-button>
            </template>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </div>
</template>

<style lang="scss" scoped></style>
