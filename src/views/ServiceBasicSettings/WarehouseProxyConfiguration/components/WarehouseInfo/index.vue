<script setup lang="ts">
import type { WarehouseAgentVo } from '@/api/warehouseProxyConfiguration/types'

defineOptions({
  name: 'WarehouseInfo'
})

// 接收选中的仓库信息
const { selectedWarehouse } = defineProps<{
  selectedWarehouse?: WarehouseAgentVo
}>()

/**
 * @description: 格式化时间
 */
const formatTime = (time?: string) => {
  if (!time) return '--'
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * @description: 获取仓库类型显示文本
 */
const getWarehouseTypeText = (type?: string) => {
  const typeMap: Record<string, string> = {
    leg: '头程',
    overseasPosition: '海外仓'
  }
  return typeMap[type || ''] || type || '--'
}
</script>

<template>
  <ContentWrap class="h-230" title="主仓信息">
    <div
      v-if="!selectedWarehouse"
      class="flex items-center justify-center h-full text-gray-400"
    >
      请选择一个仓库查看详情
    </div>

    <el-descriptions v-else title="" :column="1" class="warehouse-info">
      <el-descriptions-item label="主仓系统类型:">
        <span class="info-value">{{
          getWarehouseTypeText(selectedWarehouse.warehouseType)
        }}</span>
      </el-descriptions-item>

      <el-descriptions-item label="主仓名称:">
        <span class="info-value">{{
          selectedWarehouse.warehouseName || '--'
        }}</span>
      </el-descriptions-item>

      <el-descriptions-item label="最后更新人:">
        <span class="info-value">{{
          selectedWarehouse.updateUser || '--'
        }}</span>
      </el-descriptions-item>

      <el-descriptions-item label="最后更新时间:">
        <span class="info-value">{{
          formatTime(selectedWarehouse.updateTime)
        }}</span>
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.warehouse-info {
  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: var(--el-text-color-regular);
    width: 120px;
  }

  :deep(.el-descriptions__content) {
    color: var(--el-text-color-primary);
  }
}

.info-value {
  font-weight: 400;
  word-break: break-all;
}
</style>
