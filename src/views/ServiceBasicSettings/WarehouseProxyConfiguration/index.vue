<script setup lang="ts">
import type { WarehouseAgentVo } from '@/api/warehouseProxyConfiguration/types'
import WarehouseSidebar from './components/WarehouseSidebar/index.vue'
import WarehouseInfo from './components/WarehouseInfo/index.vue'
import WarehouseMappingTab from './components/WarehouseMappingTab/index.vue'
import LogisticsChannelTab from './components/LogisticsChannelTab/index.vue'
import SkuPairingTab from './components/SkuPairingTab/index.vue'

defineOptions({
  name: 'WarehouseProxyConfiguration'
})

const active = ref('1')
const selectedWarehouse = ref<WarehouseAgentVo>()

/**
 * @description: 处理仓库选择事件
 */
const handleSelectWarehouse = (warehouse: WarehouseAgentVo) => {
  selectedWarehouse.value = warehouse
}
</script>

<template>
  <div class="flex w-full h-[calc(100vh-90px)]">
    <!-- 仓库侧边栏 -->
    <WarehouseSidebar @select-warehouse="handleSelectWarehouse" />

    <!-- 主内容区域 -->
    <div class="flex-1 ml-20 flex flex-col">
      <!-- 仓库信息 -->
      <WarehouseInfo :selected-warehouse="selectedWarehouse" />

      <!-- Tab页内容 -->
      <ContentWrap class="mt-20 flex-1">
        <el-tabs v-model="active">
          <el-tab-pane label="仓库映射" name="1">
            <WarehouseMappingTab :selected-warehouse="selectedWarehouse" />
          </el-tab-pane>
          <el-tab-pane label="物流渠道" name="2">
            <LogisticsChannelTab :selected-warehouse="selectedWarehouse" />
          </el-tab-pane>
          <el-tab-pane label="SKU配对" name="3">
            <SkuPairingTab :selected-warehouse="selectedWarehouse" />
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
