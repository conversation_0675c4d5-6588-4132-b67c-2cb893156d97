<script setup lang="ts">
import { examineAccountRecharge } from '@/api/accountRecharge'
import type { AccountEntity } from '@/api/accountRecharge/types'

defineOptions({
  name: 'RechargeRecordReviewDialog'
})
const visible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { reviewObj } = defineProps<{
  reviewObj: AccountEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    createTime: undefined,
    createBy: undefined,
    updateTime: undefined,
    updateBy: undefined,
    customerCode: undefined,
    rechargeAccount: undefined,
    rechargeAccountId: undefined,
    rechargeMethod: undefined,
    receBank: undefined,
    receBankName: undefined,
    payBankName: undefined,
    payNumber: undefined,
    rechargeAmount: undefined,
    remarks: undefined,
    voucherUrl: undefined,
    examineStatus: undefined,
    msg: undefined,
    status: '1'
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const handleOpen = () => {
  formData.value = Object.assign(formData.value, reviewObj)
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await examineAccountRecharge(formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="充值记录审核"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        label-suffix="："
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="客户信息" prop="">
              <div>80565</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户类别" prop="">
              <div>人民币-预付款账号</div>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="是否入账转充" prop="">
              <div>是</div>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="收款银行名称" prop="">
              <div>现金(不支持预付款)</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款银行名称" prop="">
              <div>暂无</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="付款流水号" prop="">
              <div>暂无</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="付款时间" prop="">
              <div>2525-02-28 13:30:30</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="充值金额" prop="">
              <div>0.01</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="">
              <div>{{ reviewObj.remarks || '暂无' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传凭证" prop="">
              <div>暂无</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核结果" prop="">
              <el-radio-group v-model="formData.examineStatus">
                <el-radio :label="'审核通过'" :value="1"></el-radio>
                <el-radio :label="'审核不通过'" :value="2"></el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核原因" prop="">
              <el-input
                v-model.trim="formData.msg"
                clearable
                placeholder=""
                type="textarea"
                :rows="5"
                :maxlength="150"
                resize="none"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
