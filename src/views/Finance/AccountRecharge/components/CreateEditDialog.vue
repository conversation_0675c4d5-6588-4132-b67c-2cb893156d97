<script setup lang="ts">
import {
  saveAccountRecharge,
  updateAccountRecharge
} from '@/api/accountRecharge'
import type { AccountEntity } from '@/api/accountRecharge/types'
import { useDictItem } from '@/hooks/useDictItem'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const recharge_mode = useDictItem('recharge_mode')

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<AccountEntity>(
    {
      id: undefined,
      // createTime: undefined,
      // createBy: undefined,
      // updateTime: undefined,
      // updateBy: undefined,
      customerCode: undefined,
      rechargeAccount: undefined,
      rechargeAccountId: undefined,
      rechargeMethod: undefined,
      receBank: undefined,
      receBankName: undefined,
      payBankName: undefined,
      payNumber: undefined,
      rechargeAmount: undefined,
      remarks: undefined,
      voucherUrl: undefined,
      examineStatus: undefined,
      msg: undefined
    },
    {
      rechargeAccountId: [
        { required: true, message: '请选择充值账户', trigger: 'change' }
      ],
      receBank: [
        { required: true, message: '请选择收款银行卡', trigger: 'change' }
      ],
      rechargeAmount: [
        { required: true, message: '请输入充值金额', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        {
          validator: (_rule, value, callback) => {
            const reg = /\d/
            if (reg.test(value)) {
              callback()
            } else {
              callback(new Error('请输入有效的手机号'))
            }
          },
          trigger: 'blur'
        }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新增充值记录',
  EDIT: '编辑充值记录',
  DETAIL: '充值记录详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveAccountRecharge,
  EDIT: updateAccountRecharge,
  DETAIL: () => Promise.resolve({})
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}

const fileList = ref([])
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    destroy-on-close
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        :disabled="disabled"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="所属客户" prop="">
              <el-input
                v-model.trim="formData.customerCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值账户" prop="rechargeAccountId">
              <el-select
                v-model="formData.rechargeAccountId"
                clearable
                placeholder=""
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="充值方式" prop="">
              <el-radio-group v-model="formData.rechargeMethod">
                <el-radio
                  v-for="item of recharge_mode"
                  :key="item.itemValue"
                  :label="item.itemText"
                  :value="item.itemValue"
                ></el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="formData.rechargeMethod === 'bankTransfer'">
            <el-col :span="12">
              <el-form-item label="收款银行卡" prop="receBank">
                <el-select v-model="formData.receBank" clearable placeholder="">
                  <el-option :value="1" label="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款银行名称" prop="">
                <el-input
                  v-model.trim="formData.receBankName"
                  clearable
                  placeholder=""
                ></el-input>
              </el-form-item>
            </el-col>
          </template>

          <el-col :span="12">
            <el-form-item label="付款银行名称" prop="">
              <el-input
                v-model.trim="formData.payBankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款流水" prop="">
              <el-input
                v-model.trim="formData.payNumber"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值金额" prop="rechargeAmount">
              <el-input
                v-model.trim="formData.rechargeAmount"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="">
              <el-input
                v-model.trim="formData.remarks"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传凭证" prop="">
              <DUpload v-model="fileList" :limit="1"></DUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
