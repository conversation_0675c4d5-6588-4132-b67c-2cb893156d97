<script setup lang="ts">
import {
  delByIdAccountRecharge,
  // disableAccountRecharge,
  // enableAccountRecharge,
  getAccountRechargeList
} from '@/api/accountRecharge'
import CreateEditDialog from './components/CreateEditDialog.vue'
import RechargeRecordReviewDialog from './components/RechargeRecordReviewDialog.vue'
import type { AccountEntity } from '@/api/accountRecharge/types'
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'AccountRecharge'
})

const recharge_status = useDictItem('recharge_status')

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<AccountEntity>({
  immediate: true,
  initialFormData: {
    status: undefined
  },
  fetchDataApi: async () => {
    const res = await getAccountRechargeList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await delByIdAccountRecharge(ids)
      return !!res
    } else {
      const res = await delByIdAccountRecharge([record.id])
      return !!res
    }
  }
})

// const handleStatusChange = async (type: '0' | '1', row: AccountEntity) => {
//   if (type === '0') {
//     await disableAccountRecharge([row.id as string])
//   } else if (type === '1') {
//     await enableAccountRecharge([row.id as string])
//   }
//   tableMethods.handleQuery()
// }

const reviewVisible = ref(false)
const reviewObj = ref()
const handleReview = (row: AccountEntity) => {
  reviewVisible.value = true
  reviewObj.value = row
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="客户编号/名称" prop="">
            <el-input
              v-model.trim="formData.customerCode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="recharge_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审核时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
            />
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <!-- <el-form-item label="" prop="" class="pt-30">
            
          </el-form-item> -->
          <div class="w-full flex justify-end">
            <el-button type="primary">搜索</el-button>
            <el-button>重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增充值记录</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="客户编号" prop="customerCode" width="180" />
      <el-table-column label="充值账户" prop="rechargeAccount" width="180" />
      <el-table-column label="转账账户" prop="date" width="180" />
      <el-table-column label="汇率" prop="date" />
      <el-table-column label="打款银行卡" prop="payBankName" min-width="180" />
      <el-table-column label="充值金额" prop="rechargeAmount" min-width="180" />
      <el-table-column label="状态" prop="status" width="180" />
      <el-table-column label="备注" prop="remarks" width="180" />
      <el-table-column label="申请人" prop="createBy" width="180" />
      <el-table-column label="审核人" prop="updateBy" width="180" />
      <el-table-column label="操作" fixed="right" width="200">
        <template #default="{ row }">
          <el-button
            type="primary"
            @click="() => tableMethods.handleDetail(row)"
            >详情</el-button
          >
          <el-button type="primary" @click="() => handleReview(row)"
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
  <RechargeRecordReviewDialog
    v-model="reviewVisible"
    :reviewObj="reviewObj"
    @refresh="tableMethods.handleQuery"
  ></RechargeRecordReviewDialog>
</template>

<style lang="scss" scoped></style>
