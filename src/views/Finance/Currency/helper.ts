export function generateCronExpression(
  frequency: string,
  updateDays: string[],
  updateTime: string
) {
  console.log('frequency', frequency)
  console.log('updateDays', updateDays)
  console.log('updateTime', updateTime)

  // 拆分更新时间
  const [hours, minutes, seconds] = updateTime.split(':').map(Number)
  let dayPart, weekPart

  if (frequency === '1') {
    dayPart = '*'
    // weekPart = '*'
    weekPart = '?'
  } else if (frequency === '2') {
    const weeks = updateDays.join('-')
    dayPart = '*'
    weekPart = weeks
  } else if (frequency === '3') {
    dayPart = updateDays.join(',')
    // weekPart = '*'
    weekPart = '?'
  } else {
    throw new Error('不支持的频次，请输入每天、每周或每月')
  }

  return `${seconds} ${minutes} ${hours} ${dayPart} * ${weekPart}`
}
