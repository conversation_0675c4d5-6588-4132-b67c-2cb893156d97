<script setup lang="ts">
import { updateCurrencySetting } from '@/api/currency'
import type { CurrencyEntity } from '@/api/currency/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'ExchangeRateConfig'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const adjustment_method = inject('adjustment_method') as Ref<DictItemEntity[]>

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<CurrencyEntity>(
    {
      id: undefined,
      // customExchangeRate: undefined,
      customExchangeValue: undefined,
      officialExchangeRate: undefined,
      adjustmentMethods: [
        {
          name: undefined,
          value: undefined
        }
      ]
    },
    {
      subsidiaryName: [
        { required: true, message: '请输入所属子公司名', trigger: 'blur' }
      ]
    }
  )

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: updateCurrencySetting,
  EDIT: updateCurrencySetting,
  DETAIL: updateCurrencySetting
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const baseExchangeRate = ref('officialExchangeRate')

// 基准汇率options
const baseExchangeRateOptions = [
  {
    itemText: '官方汇率',
    itemValue: 'officialExchangeRate'
  },
  {
    itemText: '自定义汇率',
    // itemValue: 'customExchangeRate'
    itemValue: 'customExchangeValue'
  }
]

/**
 * @description: 添加选项
 * @return {*}
 */
const handAddAdjustment = () => {
  formData.value?.adjustmentMethods?.push({
    name: undefined,
    value: undefined
  })
}

/**
 * @description: 删除选项
 * @param {*} index
 * @return {*}
 */
const handleDel = (index: number) => {
  formData.value?.adjustmentMethods?.splice(index, 1)
}

/**
 * @description: 根据汇率基准设置官方汇率及自定义汇率
 * @return {*}
 */
const getExchangeRate = () => {
  if (baseExchangeRate.value === 'officialExchangeRate') {
    formData.value.officialExchangeRate = viewEntity.record.officialExchangeRate
    // formData.value.customExchangeRate = undefined
    formData.value.customExchangeValue = undefined
  }
  if (baseExchangeRate.value === 'customExchangeValue') {
    formData.value.officialExchangeRate = undefined
  }
}

const handleOpen = () => {
  console.log('viewEntity', viewEntity)
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
  baseExchangeRate.value = 'officialExchangeRate'
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      getExchangeRate()
      formData.value.id = viewEntity.record.id

      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="自定义汇率配置"
    width="700"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="left"
        label-width="80px"
        @submit.prevent
        class="w-500 mx-auto"
      >
        <el-row class="w-full">
          <el-col :span="24">
            <el-form-item label="汇率基准">
              <div class="flex w-full">
                <DSelect
                  v-model="baseExchangeRate"
                  :options="baseExchangeRateOptions"
                  placeholder="请选择汇率基准"
                  class="!w-180"
                ></DSelect>
                <el-input
                  v-if="baseExchangeRate === 'customExchangeValue'"
                  v-model.trim="formData.customExchangeValue"
                  clearable
                  placeholder=""
                  class="flex-1 ml-12"
                ></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="调整方式">
              <div
                class="flex w-full"
                v-for="(item, index) of formData.adjustmentMethods"
                :key="index"
                :class="{ 'mt-16': index !== 0 }"
              >
                <DSelect
                  v-model="item.name"
                  :options="adjustment_method"
                  placeholder="请选择调整方式"
                  class="!w-180"
                ></DSelect>
                <el-input
                  v-model.trim="item.value"
                  clearable
                  placeholder=""
                  class="flex-1 ml-12"
                >
                  <template #suffix>
                    <span v-if="item.name !== 'FixedValue'">%</span>
                  </template>
                </el-input>

                <el-button
                  type="danger"
                  text
                  class="ml-12"
                  :disabled="index === 0"
                  @click="() => handleDel(index)"
                >
                  <div class="i-ep:close-bold text-16"></div>
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-button
          size="small"
          type="primary"
          :disabled="
            formData?.adjustmentMethods?.length === adjustment_method.length
          "
          @click="handAddAdjustment"
          >添加一项</el-button
        >
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
