<script setup lang="ts">
import { useFormData } from '@/hooks/useFormData'
import { generateCronExpression } from '../helper'
import { currencyRegularUpdateStrategy } from '@/api/currency'
import type { CurrencyEntity } from '@/api/currency/types'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'CurrencyConfig'
})
const viewVisible = defineModel({ default: false })

const week = useDictItem('week')
const month = useDictItem('month')

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<CurrencyEntity>(
    {
      id: undefined
    },
    {}
  )

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: currencyRegularUpdateStrategy,
  EDIT: currencyRegularUpdateStrategy,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')
const frequency = ref('1')
const updateDays = ref([])
const updateTime = ref('')
// 更新频率
const frequencyOptions = [
  {
    itemText: '每天',
    itemValue: '1'
  },
  {
    itemText: '每周',
    itemValue: '2'
  },
  {
    itemText: '每月',
    itemValue: '3'
  }
]

const frequencyChange = () => {
  updateDays.value = []
}

const handleOpen = () => {}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      const cron = generateCronExpression(
        frequency.value,
        updateDays.value,
        updateTime.value
      )
      console.log('generateCronExpression', cron)
      const params = {
        // jobName: 'CurrencyRegularUpdateJob',
        cron
      }
      await apiMap[viewEntity.type](params)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="币种配置"
    width="700"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="right"
        label-width="120px"
        @submit.prevent
        class="w-300 mx-auto"
      >
        <el-row class="w-full">
          <el-col :span="24">
            <el-form-item label="汇率更新频次">
              <DSelect
                v-model="frequency"
                :options="frequencyOptions"
                placeholder="请选择汇率更新频次"
                @change="frequencyChange"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col v-if="frequency !== '1'" :span="24">
            <el-form-item label="更新日">
              <DSelect
                v-if="frequency === '2'"
                v-model="updateDays"
                :options="week"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder=""
              ></DSelect>
              <DSelect
                v-if="frequency === '3'"
                v-model="updateDays"
                :options="month"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder=""
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="更新时间">
              <el-time-picker
                v-model="updateTime"
                value-format="HH:mm:ss"
                placeholder="选择时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
