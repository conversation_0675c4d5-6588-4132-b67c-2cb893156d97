<script setup lang="ts">
import { getCurrencyHistoryRecordList } from '@/api/currency'
import type { CurrencyEntity } from '@/api/currency/types'
import { useTable } from '@/hooks/useTable'
import { getNameByCode } from '@/utils'
import dayjs from 'dayjs'

defineOptions({
  name: 'ViewRecords'
})
const viewVisible = defineModel({ default: false })

const selection = inject('selection') as Ref<any>
const currencyOption = inject('currencyOption') as Ref<CurrencyOptionEntity[]>
const adjustment_method = inject('adjustment_method') as Ref<DictItemEntity[]>
const timeArr = ref([])
const {
  tableState: { pageSize, currentPage, loading, dataList },
  tableMethods,
  formData
} = useTable<CurrencyEntity>({
  immediate: false,
  initialFormData: {
    ids: undefined,
    baseCurrencyCode: undefined,
    currencyCode: undefined,
    startTime: undefined,
    endTime: undefined,
    currencyCodes: undefined
  },
  fetchDataApi: async () => {
    if (timeArr.value[0]) {
      formData.startTime =
        dayjs(timeArr.value[0]).format('YYYY-MM-DD') + ' 00:00:00'
    }
    if (timeArr.value[1]) {
      formData.endTime =
        dayjs(timeArr.value[1]).format('YYYY-MM-DD') + ' 23:59:59'
    }

    const res = await getCurrencyHistoryRecordList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

const handleOpen = () => {
  console.log('selection', selection.value)
  // formData.ids = selection.value.map((item: any) => item.id)
  formData.currencyCodes = selection.value.map((item: any) => item.currencyCode)
  tableMethods.getList()
}
const handleClose = () => {
  viewVisible.value = false
}
const handleCancel = () => {
  viewVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="查看记录"
    width="70%"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full px-16">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="left"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="6">
            <el-form-item label="基准币种">
              <DSelect
                v-model="formData.baseCurrencyCode"
                :options="currencyOption"
                :fields="{
                  label: 'baseCurrencyName',
                  value: 'baseCurrencyCode'
                }"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="币种">
              <DSelect
                v-model="formData.currencyCode"
                :options="currencyOption"
                :fields="{ label: 'currencyName', value: 'currencyCode' }"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- label-width="0" -->
            <el-form-item>
              <template #label>创建时间</template>
              <div class="flex w-full">
                <!-- <el-select
                  v-model="formData.currency"
                  clearable
                  placeholder=""
                  class="!w-120 ml--12"
                >
                  <el-option :value="1" label="1"></el-option>
                </el-select> -->
                <el-date-picker
                  v-model="timeArr"
                  type="daterange"
                  start-placeholder="请选择"
                  end-placeholder="请选择"
                  class="flex-1"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="mr--16">
              <div class="w-full flex justify-end">
                <el-button
                  type="primary"
                  @click="() => tableMethods.handleQuery()"
                  >搜索</el-button
                >
                <el-button
                  @click="
                    () => {
                      timeArr = []
                      tableMethods.handleReset()
                    }
                  "
                  >重置</el-button
                >
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-loading="loading" :data="dataList" :border="true">
        <el-table-column label="操作时间" prop="createTime" width="180" />
        <el-table-column label="基准币种" prop="baseCurrencyName" width="180" />
        <el-table-column label="币种" prop="currencyName" width="180" />
        <el-table-column label="操作人" prop="operator" width="180" />
        <el-table-column label="操作IP" prop="ip" width="180" />
        <el-table-column label="操作设备" width="180">
          <template #default="{ row }">
            {{ row.systemName }}
            {{ row.browserName ? ' / ' + row.browserName : row.browserName }}
          </template>
        </el-table-column>
        <el-table-column label="操作前" prop="date" min-width="240">
          <template #default="{ row }">
            <div>
              <span>汇率基准 </span>
              <span class="ml-5" v-if="row.beforeCustomExchangeRate">
                <span>自定义汇率</span>
                <span>{{ row.beforeCustomExchangeRate }}</span>
              </span>
              <span v-else class="ml-5">官方汇率</span>
            </div>
            <div
              v-for="(item, index) of row.beforeAdjustmentMethods"
              :key="index"
            >
              <span>调整方式</span>
              <span class="ml-5">{{
                getNameByCode(adjustment_method, item.name)
              }}</span>
              <span class="ml-5">{{ item.value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作后" prop="date" min-width="240">
          <template #default="{ row }">
            <div>
              <span>汇率基准 </span>
              <span class="ml-5" v-if="row.afterCustomExchangeRate">
                <span>自定义汇率</span>
                <span>{{ row.afterCustomExchangeRate }}</span>
              </span>
              <span v-else class="ml-5">官方汇率</span>
            </div>
            <div
              v-for="(item, index) of row.afterAdjustmentMethods"
              :key="index"
            >
              <span>调整方式</span>
              <span class="ml-5">{{
                getNameByCode(adjustment_method, item.name)
              }}</span>
              <span class="ml-5">{{ item.value }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, ->, sizes, prev, pager, next, jumper"
        :total="total"
        class="mt-16"
      /> -->
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">关闭</el-button>
        <!-- <el-button type="primary" @click="handleConfirm"> 确认 </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
