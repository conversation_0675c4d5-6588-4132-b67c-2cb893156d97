<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import ViewRecords from './components/ViewRecords.vue'
import CurrencyConfig from './components/CurrencyConfig.vue'
import ExchangeRateConfig from './components/ExchangeRateConfig.vue'
import { useDictItem } from '@/hooks/useDictItem'
import { useToggle } from '@/hooks/useToggle'
import type { CurrencyEntity } from '@/api/currency/types'
import { enableOrDisableCurrency, getCurrencyList } from '@/api/currency'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'Currency'
})

const user_status = useDictItem('user_status')
const adjustment_method = useDictItem('adjustment_method')

const { currencyOption } = useOptions(['currencyOption'])

provide('currencyOption', currencyOption)
provide('adjustment_method', adjustment_method)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<CurrencyEntity>({
  immediate: true,
  initialFormData: {},
  fetchDataApi: async () => {
    const res = await getCurrencyList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  }
})

provide('selection', selection)

const handleStatusChange = async (type: '0' | '1', row?: CurrencyEntity[]) => {
  let ids
  if (row?.length) {
    ids = row.map(item => item.id?.toString())
  } else {
    if (selection.value?.length === 0) {
      ElMessage.error('请选择币种')
      return
    }
    ids = selection.value.map(item => item.id?.toString())

    const result = selection.value.filter(
      item => item.status?.toString() === type
    )
    if (result?.length > 0) {
      if (type === '0') {
        ElMessage.error('选中的币种中存在停用的币种')
        return
      }
      ElMessage.error('选中的币种中存在停用的币种')
      return
    }
  }

  await enableOrDisableCurrency({ ids: ids as string[], status: type })
  tableMethods.handleQuery()
}

const [vrVisible, handleVr] = useToggle()
const [ccVisible, handleCC] = useToggle()
const [ercVisible, handleERC] = useToggle()

/**
 * @description: 校验是selection否选择数据
 * @param {*} cb
 * @return {*}
 */
const checkIds = (cb: () => void) => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择币种')
    return false
  }
  if (cb) {
    cb()
  }
}

/**
 * @description: 前置处理erc
 * @param {*} row
 * @return {*}
 */
const handleErcBefore = (row: CurrencyEntity) => {
  viewEntity.type = 'EDIT'
  viewEntity.record = row
  handleERC()
}

// defaultRegularUpdateStrategy({ cron: '45 47 19 * * ?' })
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="基准币种" prop="">
            <DSelect
              v-model="formData.baseCurrencyCode"
              :options="currencyOption"
              :fields="{ label: 'baseCurrencyName', value: 'baseCurrencyCode' }"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <DSelect
              v-model="formData.currencyCode"
              :options="currencyOption"
              :fields="{ label: 'currencyName', value: 'currencyCode' }"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button @click="() => handleCC()"> 币种配置 </el-button>
      <el-button @click="() => handleStatusChange('1')"> 启用 </el-button>
      <el-button @click="() => handleStatusChange('0')"> 禁用 </el-button>
      <el-button @click="() => checkIds(handleVr)"> 查看记录 </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column label="基准币种" prop="baseCurrencyName" width="180" />
      <el-table-column label="币种" prop="currencyName" width="180" />
      <el-table-column
        label="官方汇率"
        prop="officialExchangeRate"
        width="180"
      />
      <el-table-column
        label="自定义汇率"
        prop="customExchangeRate"
        min-width="180"
      />
      <el-table-column
        label="汇率更新时间"
        prop="exchangeRateUpdateTime"
        min-width="180"
      />
      <el-table-column label="状态" prop="statusName" width="120" />
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{ row }">
          <el-button type="primary" @click="() => handleErcBefore(row)">
            设置
          </el-button>
          <el-button
            v-if="row.status === 0"
            type="primary"
            @click="() => handleStatusChange('1', [row])"
          >
            启用
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="warning"
            @click="() => handleStatusChange('0', [row])"
          >
            禁用
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <!-- 查看记录 -->
  <ViewRecords v-model="vrVisible"></ViewRecords>
  <CurrencyConfig
    v-model="ccVisible"
    :view-entity="viewEntity"
  ></CurrencyConfig>
  <ExchangeRateConfig
    v-model="ercVisible"
    :view-entity="viewEntity"
    @refresh="tableMethods.getList"
  ></ExchangeRateConfig>
</template>

<style lang="scss" scoped></style>
