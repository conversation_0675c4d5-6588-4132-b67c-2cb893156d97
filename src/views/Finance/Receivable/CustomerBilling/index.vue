<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import Count from './components/Count.vue'
import DocNumber from './components/DocNumber.vue'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'SettlementDoc'
})

const formData = ref({
  name: ''
})
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const visible = ref(false)
const handleOpen = () => {
  visible.value = true
}

const [countVisible, handleCount] = useToggle()
const [docVisible, handleDoc] = useToggle()

const value = ref('头程')
const options = [
  {
    label: '头程',
    value: '头程'
  },
  {
    label: '海外仓',
    value: '海外仓'
  },
  {
    label: '分销商城',
    value: '分销商城'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="left"
      label-width="90px"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="账单单据号" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="核销状态" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建人" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" prop="" label-width="0">
            <template #label> </template>
            <div class="flex w-full">
              <el-select
                v-model="formData.name"
                clearable
                placeholder=""
                class="!w-110 ml--12"
              >
                <el-option :value="1" label="创建时间"></el-option>
                <el-option :value="2" label="审核时间"></el-option>
                <el-option :value="3" label="核销时间"></el-option>
              </el-select>
              <el-date-picker
                v-model="formData.name"
                type="daterange"
                placeholder=""
                class="flex-1"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <!-- <el-form-item label="" prop="" class="pt-30"> -->
          <div class="w-full flex justify-end">
            <el-button type="primary">搜索</el-button>
            <el-button>重置</el-button>
          </div>
          <!-- </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button @click="() => handleDoc()">导出账单</el-button>
      <el-button>审核账单</el-button>
      <el-button>退回待审核</el-button>
      <el-button>核销账单</el-button>
      <el-button>废弃账单</el-button>
      <el-dropdown>
        <el-button class="ml-12">
          导出数据
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button class="ml-12" @click="() => handleCount()">查看统计</el-button>
    </div>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="账单单据号" prop="date" width="180" />
      <el-table-column label="客户" prop="date" width="180" />
      <el-table-column label="币种" prop="date" width="180" />
      <el-table-column label="账单总金额" prop="date" min-width="180" />
      <el-table-column label="已支付" prop="date" min-width="180" />
      <el-table-column label="待支付" prop="date" min-width="180" />
      <el-table-column label="核销状态" prop="date" min-width="180" />
      <el-table-column label="创建人" prop="date" width="180" />
      <el-table-column label="创建时间" prop="date" width="180" />
      <el-table-column label="审核时间" prop="date" width="180" />
      <el-table-column label="核销时间" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="290">
        <template #default="{}">
          <el-button text type="primary">审核</el-button>
          <el-button text type="primary" @click="handleOpen">废弃</el-button>
          <el-button text type="primary">导出账单</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <CreateEditDialog v-model="visible"></CreateEditDialog>
  <Count v-model="countVisible"></Count>
  <DocNumber v-model="docVisible"></DocNumber>
</template>

<style lang="scss" scoped></style>
