<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'

defineOptions({
  name: 'SettlementDoc'
})

const formData = ref({
  name: ''
})
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const visible = ref(false)
const handleOpen = () => {
  visible.value = true
}

const value = ref('全部')
const options = [
  {
    label: '全部',
    value: '全部'
  },
  {
    label: '待确认',
    value: '待确认',
    disabled: true
  },
  {
    label: '待付款',
    value: '待付款'
  },
  {
    label: '账单驳回',
    value: '账单驳回'
  },
  {
    label: '待核销',
    value: '待核销'
  },
  {
    label: '已核销',
    value: '已核销'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="账单编号" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="卖家信息" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账期" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="业务单号" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <!-- <el-form-item label="" prop="" class="pt-30"> -->
          <div class="w-full flex justify-end">
            <el-button type="primary">搜索</el-button>
            <el-button>重置</el-button>
          </div>
          <!-- </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary">收款确认</el-button>
      <el-button type="primary">批量付款</el-button>
      <el-button type="primary">账单核销</el-button>
      <el-button type="primary">导出数据</el-button>
    </div>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="账单编号" prop="date" width="180" />
      <el-table-column label="卖家信息" prop="date" width="180" />
      <el-table-column label="账单类型" prop="date" width="180" />
      <el-table-column label="账单期数" prop="date" min-width="180" />
      <el-table-column label="出账时间" prop="date" min-width="180" />
      <el-table-column label="账单金额" prop="date" min-width="180" />
      <el-table-column label="币种" prop="date" min-width="180" />
      <el-table-column label="已结清金额" prop="date" width="180" />
      <el-table-column label="账期天数" prop="date" width="180" />
      <el-table-column label="状态" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="290">
        <template #default="{}">
          <el-button type="primary">收款确认</el-button>
          <el-button type="primary" @click="handleOpen">付款</el-button>
          <el-button type="primary">账单核销</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <CreateEditDialog v-model="visible"></CreateEditDialog>
</template>

<style lang="scss" scoped></style>
