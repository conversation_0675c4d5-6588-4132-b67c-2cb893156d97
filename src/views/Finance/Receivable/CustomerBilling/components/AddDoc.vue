<script setup lang="ts">
defineOptions({
  name: '<PERSON><PERSON><PERSON><PERSON>'
})
const visible = defineModel({ default: false })

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const formData = ref({
  name: ''
})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="添加结算单"
    width="70%"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="px-16">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="left"
        label-width="120px"
        @submit.prevent
      >
        <el-row :gutter="8">
          <el-col :span="6">
            <el-form-item label="" prop="" label-width="0">
              <template #label> </template>
              <div class="flex w-full">
                <el-select
                  v-model="formData.name"
                  clearable
                  placeholder=""
                  class="!w-110 ml--12"
                >
                  <el-option :value="1" label="关联单号"></el-option>
                  <el-option :value="2" label="结算单据号"></el-option>
                </el-select>
                <el-input
                  v-model.trim="formData.name"
                  clearable
                  placeholder=""
                  class="flex-1"
                ></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="费用类型" prop="">
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="" prop="" label-width="0">
              <template #label> </template>
              <div class="flex w-full">
                <el-select
                  v-model="formData.name"
                  clearable
                  placeholder=""
                  class="!w-110 ml--12"
                >
                  <el-option :value="1" label="创建时间"></el-option>
                  <el-option :value="2" label="审核时间"></el-option>
                  <el-option :value="3" label="核销时间"></el-option>
                </el-select>
                <el-date-picker
                  v-model="formData.name"
                  type="daterange"
                  placeholder=""
                  class="flex-1"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <!-- <el-form-item label="" prop="" class="pt-30"> -->
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
            </div>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
      <div class="mb-18">
        <el-button>添加结算单</el-button>
      </div>
      <el-table :data="tableData" :border="true" style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column label="结算单据号" prop="date" width="180" />
        <el-table-column label="关联单号" prop="date" width="180" />
        <el-table-column label="客户" prop="date" width="180" />
        <el-table-column label="运输服务" prop="date" width="180" />
        <el-table-column label="费用类型" prop="date" width="180" />
        <el-table-column label="单位" prop="date" width="180" />
        <el-table-column label="单位费用" prop="date" width="180" />
        <el-table-column label="账单收费重" prop="date" width="180" />
        <el-table-column label="金额" prop="date" width="180" />
        <el-table-column label="待付款金额" prop="date" width="180" />
        <el-table-column label="币种" prop="date" width="180" />
        <el-table-column label="费用描述" prop="date" width="180" />
        <el-table-column label="内部备注" prop="date" width="180" />
        <el-table-column label="状态" prop="date" width="180" />
        <el-table-column label="出账状态" prop="date" width="180" />
        <el-table-column label="创建时间" prop="date" width="180" />
        <el-table-column label="确认时间" prop="date" width="180" />
        <el-table-column label="结算时间" prop="date" width="180" />
        <el-table-column label="出账时间" prop="date" width="180" />
        <el-table-column label="付款时间" prop="date" width="180" />
      </el-table>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
