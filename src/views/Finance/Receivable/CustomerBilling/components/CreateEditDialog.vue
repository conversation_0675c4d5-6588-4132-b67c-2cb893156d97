<script setup lang="ts">
defineOptions({
  name: 'CreateEditDialog'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})
const fileList = ref()

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="付款"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="账单编号" prop="">
              <div>63177</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账单金额" prop="">
              <div>63177</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款方式" prop="">
              <el-select v-model="formData.num" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
                <el-option :value="2" label="银行卡"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款金额" prop="">
              <el-input v-model.trim="formData.name" clearable placeholder="">
                <template #append>人民币</template>
              </el-input>
            </el-form-item>
          </el-col>
          <template v-if="formData.num === 2">
            <el-col :span="12">
              <el-form-item label="收款银行查询" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款开户银行" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款银行账号" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款开户名" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付银行账号" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付银行开户行" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付银行流水" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付银行开户名" prop="">
                <el-input v-model.trim="formData.name" clearable placeholder="">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付凭证" prop="">
                <el-upload v-model:file-list="fileList" action="#">
                  <el-button type="primary">上传凭证</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
