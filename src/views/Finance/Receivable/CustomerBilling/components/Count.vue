<script setup lang="ts">
defineOptions({
  name: 'Count'
})
const visible = defineModel({ default: false })

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="统计"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="px-16">
      <el-table :data="tableData" :border="true" style="width: 100%">
        <el-table-column label="币种" prop="date" width="180" />
        <el-table-column label="客户数量" prop="date" width="180" />
        <el-table-column label="账单金额" prop="date" width="180" />
        <el-table-column label="已支付" prop="date" width="180" />
        <el-table-column label="未支付" prop="date" width="180" />
      </el-table>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
