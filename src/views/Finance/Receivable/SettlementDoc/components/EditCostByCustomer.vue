<script setup lang="ts">
import { saveSettdoc } from '@/api/settlementDoc'
import type { SettdocEntity } from '@/api/settlementDoc/types'
import { getCurrentTimeFormatted } from '@/utils/timeFormat'

defineOptions({
  name: 'EditCostByCustomer'
})

const emits = defineEmits(['refresh'])

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const initialRow = () => ({
  now: getCurrentTimeFormatted(),
  // 客户id
  customerId: '',
  // 结算时间
  settlementTime: '',
  // 费用类型id
  costTypeId: '',
  // 单位
  costUnit: '',
  // 单位费用
  unitCost: '',
  // 币种
  currency: '',
  // 账单收费重
  billCharge: '',
  // 费用描述
  costDesc: '',
  // 内部备注
  remarks: ''
})

const tableData = ref<SettdocEntity[]>([initialRow()])

const handleAdd = () => {
  tableData.value.push(initialRow())
}

/**
 * @description: 设置结算时间为当前时间
 * @param {*} row
 * @return {*}
 */
const handleSetNow = (row: any) => {
  row.settlementTime = getCurrentTimeFormatted()
}

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirm = async () => {
  await saveSettdoc(tableData.value)
  emits('refresh')
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑费用"
    width="70%"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <el-table :data="tableData" :border="true" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="时间" prop="now" width="180" />
        <el-table-column label="客户" prop="name" width="180">
          <template #default="{ row }">
            <el-select v-model="row.customerId" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="结算时间" prop="name" width="280">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-date-picker
                v-model="row.settlementTime"
                type="datetime"
                placeholder=""
                class="flex-1"
              />
              <el-button type="primary" text @click="handleSetNow(row)"
                >当前</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="费用类型" prop="name" width="180">
          <template #default="{ row }">
            <el-select v-model="row.costTypeId" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="单位" prop="name" width="180">
          <template #default="{ row }">
            <el-select v-model="row.costUnit" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="单位费用" prop="name" width="180">
          <template #default="{ row }">
            <el-input
              v-model.trim="row.unitCost"
              clearable
              placeholder=""
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="币种" prop="name" width="180">
          <template #default="{ row }">
            <el-select v-model="row.currency" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="账单收费重" prop="name" width="180">
          <template #default="{ row }">
            <el-input
              v-model.trim="row.billCharge"
              clearable
              placeholder=""
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="费用描述" prop="name" width="180">
          <template #default="{ row }">
            <el-input
              v-model.trim="row.costDesc"
              clearable
              placeholder=""
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="内部备注" prop="name" width="180">
          <template #default="{ row }">
            <el-input
              v-model.trim="row.remarks"
              clearable
              placeholder=""
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <div
        border="1px solid #ebeef5"
        class="h-40 w-full flex-center color-#3489ff"
      >
        <div class="flex cursor-pointer" @click="handleAdd">
          <div class="i-ep:circle-plus text-20"></div>
          <div class="ml-8">添加费用</div>
        </div>
      </div>
      <div class="mt-16">共计{{ tableData?.length }}条</div>

      <!-- <el-table
        :data="tableData"
        :border="true"
        style="width: 100%"
        class="mt-20"
      >
        <el-table-column label="运单号" prop="name" width="180" />
        <el-table-column label="客户" prop="name" width="180" />
        <el-table-column label="服务商服务" prop="name" width="180" />
        <el-table-column label="服务商实重" prop="name" width="180" />
        <el-table-column prop="name" width="180">
          <template #hedaer>
            <div>服务商体积</div>
            <div>材积重</div>
          </template>
        </el-table-column>
        <el-table-column label="服务商收费重" prop="name" width="180" />
        <el-table-column label="运输服务" prop="name" width="180" />
        <el-table-column label="实重" prop="name" width="180" />
        <el-table-column prop="name" width="180">
          <template #hedaer>
            <div>体积</div>
            <div>材积重</div>
          </template>
        </el-table-column>
        <el-table-column label="收费重" prop="name" width="180" />
        <el-table-column label="物品属性" prop="name" width="180" />
      </el-table> -->
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
