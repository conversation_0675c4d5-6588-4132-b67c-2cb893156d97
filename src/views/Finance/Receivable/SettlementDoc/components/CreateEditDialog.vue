<script setup lang="ts">
import { saveSettdoc, updateSettdoc } from '@/api/settlementDoc'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    status: undefined,
    orderNumber: undefined,
    orderType: undefined,
    tradingDate: undefined,
    costType: undefined,
    costCategory: undefined,
    costCurrency: undefined,
    costMoney: undefined,
    adjustMoney: undefined,
    adjustMsg: undefined,
    trackNumber: undefined,
    calculationMethod: undefined,
    endTime: undefined,
    companyName: undefined,
    companyCode: undefined,
    type: undefined,
    timeType: undefined,
    startTimes: undefined,
    endTimes: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const titleMap: Record<DlViewType, string> = {
  ADD: '新增结算单',
  EDIT: '编辑结算单',
  DETAIL: '结算单详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveSettdoc,
  EDIT: updateSettdoc,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="客户信息" prop="">
              <el-select
                v-model="formData.companyCode"
                clearable
                placeholder=""
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单类型" prop="">
              <el-select v-model="formData.orderType" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务单据号" prop="">
              <el-input
                v-model.trim="formData.orderNumber"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易时间" prop="">
              <el-input
                v-model.trim="formData.tradingDate"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用项类型" prop="">
              <el-select v-model="formData.costType" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用项类别" prop="">
              <el-select
                v-model="formData.costCategory"
                clearable
                placeholder=""
              >
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用币种" prop="costCurrency">
              <div>CNY</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用金额" prop="costMoney	">
              <div>0.01</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整金额" prop="">
              <el-input
                v-model.trim="formData.adjustMoney"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整原因" prop="">
              <el-input
                v-model.trim="formData.adjustMsg"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
