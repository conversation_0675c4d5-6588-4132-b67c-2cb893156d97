<script setup lang="ts">
defineOptions({
  name: 'BatchSettlementDialog'
})
const visible = defineModel({ default: false })

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="新增结算单"
    width="600"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <div class="mb-18 text-16">批量导入</div>
      <div class="mb-18">
        <el-button type="primary">下载导入模板</el-button>
      </div>
      <div class="mb-18">
        <el-button type="primary">导入Excel</el-button>
      </div>
      <div class="mb-10 text-16">导入模板说明</div>
      <p class="lh-24">
        目前仅支持导入【入库订单】、【出库订单】、【退货订单】的结算单，以下列字字段请使用英语的英文字符:
      </p>
      <p class="lh-24">字段说明:</p>
      <p class="lh-24">
        业务类型:INBOUND("入库单”),OUTBOUND(“出库单"),REFUND(”退货订单”);
      </p>
      <p class="lh-24">
        产品类型:FIRST TRANSPORT("头程"),LAST
        TRANSPORT("尾程"),INSURANCE(“保险”), WAREHOUSE("仓储”);
      </p>
      <p class="lh-24">
        费用类型:TRANSPORT(”物流运输费”),INSURANCE("保险费”),WAREHOUSE("仓库操作费”),TENANT("合租费用”);
      </p>
      <p class="lh-24">发生日期:格式为 yyyy/mm/dd，如:2023/12/25</p>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
