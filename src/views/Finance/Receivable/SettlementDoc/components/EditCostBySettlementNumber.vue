<script setup lang="ts">
defineOptions({
  name: 'EditCostBySettlementNumber'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑费用"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <el-table :data="tableData" :border="true" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="时间" prop="name" width="180" />
        <el-table-column label="客户" prop="name" width="180" />
        <el-table-column label="结算时间" prop="name" width="180" />
        <el-table-column label="费用类型" prop="name" width="180" />
        <el-table-column label="单位" prop="name" width="180" />
        <el-table-column label="单位费用" prop="name" width="180" />
        <el-table-column label="币种" prop="name" width="180" />
        <el-table-column label="账单收费重" prop="name" width="180" />
        <el-table-column label="费用描述" prop="name" width="180" />
        <el-table-column label="内部备注" prop="name" width="180" />
      </el-table>
      <div class="mt-16">共计1条</div>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
