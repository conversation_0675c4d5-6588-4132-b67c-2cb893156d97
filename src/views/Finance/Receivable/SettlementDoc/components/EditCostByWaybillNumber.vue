<script setup lang="ts">
defineOptions({
  name: 'EditCostByWaybillNumber'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑费用"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <el-table :data="tableData" :border="true" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="时间" prop="name" width="180" />
        <el-table-column label="费用类型" prop="name" width="180" />
        <el-table-column label="单位" prop="name" width="180" />
        <el-table-column label="单位费用" prop="name" width="180" />
        <el-table-column label="币种" prop="name" width="180" />
        <el-table-column label="账单收费重" prop="name" width="180" />
        <el-table-column label="费用描述" prop="name" width="180" />
        <el-table-column label="内部备注" prop="name" width="180" />
      </el-table>
      <div
        border="1px solid #ebeef5"
        class="h-40 w-full flex-center color-#3489ff"
      >
        <div class="i-ep:circle-plus text-20"></div>
        <div class="ml-8">添加费用</div>
      </div>

      <el-table
        :data="tableData"
        :border="true"
        style="width: 100%"
        class="mt-20"
      >
        <el-table-column label="运单号" prop="name" width="180" />
        <el-table-column label="客户" prop="name" width="180" />
        <el-table-column label="服务商服务" prop="name" width="180" />
        <el-table-column label="服务商实重" prop="name" width="180" />
        <el-table-column prop="name" width="180">
          <template #hedaer>
            <div>服务商体积</div>
            <div>材积重</div>
          </template>
        </el-table-column>
        <el-table-column label="服务商收费重" prop="name" width="180" />
        <el-table-column label="运输服务" prop="name" width="180" />
        <el-table-column label="实重" prop="name" width="180" />
        <el-table-column prop="name" width="180">
          <template #hedaer>
            <div>体积</div>
            <div>材积重</div>
          </template>
        </el-table-column>
        <el-table-column label="收费重" prop="name" width="180" />
        <el-table-column label="物品属性" prop="name" width="180" />
      </el-table>
    </div>
    <div class="mt-16">共计1条</div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
