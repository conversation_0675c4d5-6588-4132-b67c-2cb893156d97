<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import { useDictItem } from '@/hooks/useDictItem'
import {
  deleteReceivingAccount,
  getReceivingAccountList
} from '@/api/receivingAccount'
import type { ReceivingAccountEntity } from '@/api/receivingAccount/types'
import { useOptions } from '@/hooks/useOptions'

defineOptions({
  name: 'ReceivingAccount'
})

const payment_method = useDictItem('payment_method')

provide('payment_method', payment_method)

const { currencyOption } = useOptions(['currencyOption'])

provide('currencyOption', currencyOption)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<ReceivingAccountEntity>({
  immediate: true,
  initialFormData: {
    paymentMethod: undefined,
    currency: undefined
  },
  fetchDataApi: async () => {
    const res = await getReceivingAccountList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    const res = await deleteReceivingAccount({ id: record.id })
    return !!res
  }
})
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="付款方式" prop="">
            <DSelect
              v-model="formData.paymentMethod"
              :options="payment_method"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="币种" prop="">
            <DSelect
              v-model="formData.currency"
              :options="currencyOption"
              :fields="{ value: 'currencyCode', label: 'currencyName' }"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button @click="() => tableMethods.handleAdd()">
        新建收款账户
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="收款账户" prop="accountHolder" width="180" />
      <el-table-column label="付款方式" prop="paymentMethodName" width="180" />
      <el-table-column label="收款币种" prop="currencyName" width="180" />
      <el-table-column label="户名" prop="accountName" min-width="180" />
      <el-table-column label="账号" prop="accountNumber" min-width="180" />
      <el-table-column label="开户银行" prop="bankName" min-width="180" />
      <el-table-column label="联行号" prop="jointBank" min-width="180" />
      <el-table-column label="收款码" prop="paymentCode" min-width="180">
        <template #default="{ row }">
          <DImage v-if="row.paymentCode" :url="row.paymentCode"></DImage>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" min-width="180" />
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)">
            编辑
          </el-button>

          <el-button @click="() => tableMethods.hadnleDel(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
</template>

<style lang="scss" scoped></style>
