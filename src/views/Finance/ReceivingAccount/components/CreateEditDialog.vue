<script setup lang="ts">
import {
  saveReceivingAccount,
  updateReceivingAccount
} from '@/api/receivingAccount'
import type { ReceivingAccountEntity } from '@/api/receivingAccount/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const payment_method = inject('payment_method') as Ref<DictItemEntity[]>
const currencyOption = inject('currencyOption') as Ref<CurrencyOptionEntity[]>

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<ReceivingAccountEntity>(
    {
      id: undefined,
      paymentMethod: undefined,
      currency: undefined,
      accountName: undefined,
      accountNumber: undefined,
      accountHolder: undefined,
      bankName: undefined,
      jointBank: undefined,
      paymentCode: undefined
    },
    {
      paymentMethod: [
        { required: true, message: '请选择付款方式', trigger: 'change' }
      ],
      currency: [
        { required: true, message: '请选择收款币种', trigger: 'change' }
      ],
      accountHolder: [
        { required: true, message: '请输入账户名', trigger: 'blur' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新增收款账户',
  EDIT: '编辑收款账户',
  DETAIL: '收款账户详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const fileList = ref([])

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveReceivingAccount,
  EDIT: updateReceivingAccount,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      // paymentCode
      console.log('file', fileList.value)
      formData.value.paymentCode = fileList.value
        .map((el: any) => el.url)
        .join(',')

      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="付款方式" prop="paymentMethod">
              <DSelect
                v-model="formData.paymentMethod"
                :options="payment_method"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="户名">
              <el-input
                v-model.trim="formData.accountName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款币种" prop="currency">
              <DSelect
                v-model="formData.currency"
                :options="currencyOption"
                :fields="{ value: 'currencyCode', label: 'currencyName' }"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号">
              <el-input
                v-model.trim="formData.accountNumber"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名" prop="accountHolder">
              <el-input
                v-model.trim="formData.accountHolder"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款码">
              <DUpload v-model="fileList" :limit="1"></DUpload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联行号">
              <el-input
                v-model.trim="formData.jointBank"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
