<script setup lang="ts">
import { saveBankAccount, updateBankAccount } from '@/api/bankAccount'
import type { BankEntity } from '@/api/bankAccount/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } = useFormData<BankEntity>(
  {
    id: undefined,
    subsidiaryName: undefined,
    currency: undefined,
    currencyName: undefined,
    bankName: undefined,
    bankNumber: undefined,
    accountName: undefined,
    bankAddress: undefined,
    status: '1'
  },
  {
    subsidiaryName: [
      { required: true, message: '请输入所属子公司名', trigger: 'blur' }
    ],
    currency: [{ required: true, message: '请选择币种', trigger: 'change' }],
    bankName: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    bankNumber: [
      { required: true, message: '请输入银行账号', trigger: 'blur' }
    ],
    accountName: [{ required: true, message: '请输入开户名', trigger: 'blur' }]
  }
)

const titleMap: Record<DlViewType, string> = {
  ADD: '新增银行卡',
  EDIT: '编辑银行卡',
  DETAIL: '银行卡详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveBankAccount,
  EDIT: updateBankAccount,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="所属子公司名" prop="subsidiaryName">
              <el-input
                v-model.trim="formData.subsidiaryName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="币种" prop="currency">
              <el-select v-model="formData.currency" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bankName">
              <el-input
                v-model.trim="formData.bankName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="bankNumber">
              <el-input
                v-model.trim="formData.bankNumber"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户名" prop="accountName">
              <el-input
                v-model.trim="formData.accountName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行地址" prop="">
              <el-input
                v-model.trim="formData.bankAddress"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
