<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import CreateEditDialog from './components/CreateEditDialog.vue'
import {
  delByIdBankAccount,
  disableBankAccount,
  enableBankAccount,
  getBankAccountList
} from '@/api/bankAccount'
import type { BankEntity } from '@/api/bankAccount/types'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'BankAccount'
})

const user_status = useDictItem('user_status')

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<BankEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    keywords: undefined
  },
  fetchDataApi: async () => {
    const res = await getBankAccountList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await delByIdBankAccount(ids)
      return !!res
    } else {
      const res = await delByIdBankAccount([record.id])
      return !!res
    }
  }
})

const handleStatusChange = async (type: '0' | '1', row: BankEntity) => {
  if (type === '0') {
    await disableBankAccount([row.id as string])
  } else if (type === '1') {
    await enableBankAccount([row.id as string])
  }
  tableMethods.handleQuery()
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="关键字" prop="">
            <el-input
              v-model.trim="formData.keywords"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button
                type="primary"
                @click="() => tableMethods.handleQuery()"
                >搜索</el-button
              >
              <el-button @click="() => tableMethods.handleReset()"
                >重置</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()">
        新增银行卡
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
    >
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="所属子公司" prop="subsidiaryName" width="180" />
      <el-table-column label="开户银行" prop="bankName" width="180" />
      <el-table-column label="银行账号" prop="bankNumber" width="180" />
      <el-table-column label="开户名" prop="accountName" min-width="180" />
      <el-table-column label="开户行地址" prop="bankAddress" min-width="180" />
      <el-table-column label="状态" prop="statusText" width="120" />
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)">
            编辑
          </el-button>
          <el-button
            v-if="row.status === 0"
            type="primary"
            @click="() => handleStatusChange('1', row)"
          >
            启用
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="warning"
            @click="() => handleStatusChange('0', row)"
          >
            禁用
          </el-button>
          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>
</template>

<style lang="scss" scoped></style>
