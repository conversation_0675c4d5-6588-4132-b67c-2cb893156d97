<script setup lang="ts">
defineOptions({
  name: 'OutboundOrderDetail'
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const active = ref('1')
</script>

<template>
  <ContentWrap>
    <el-descriptions title="订单详情" :column="3">
      <el-descriptions-item label="仓库">-</el-descriptions-item>
      <el-descriptions-item label="物流渠道">-</el-descriptions-item>
      <el-descriptions-item label="签名服务">-</el-descriptions-item>
      <el-descriptions-item label="客户">-</el-descriptions-item>
      <el-descriptions-item label="参考单号">-</el-descriptions-item>
      <el-descriptions-item label="保险金额">-</el-descriptions-item>
      <el-descriptions-item label="销售平台">-</el-descriptions-item>
      <el-descriptions-item label="平台单号">-</el-descriptions-item>
      <el-descriptions-item label="备注">-</el-descriptions-item>
    </el-descriptions>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-descriptions title="收件信息" :column="3">
      <el-descriptions-item label="收件人">-</el-descriptions-item>
      <el-descriptions-item label="电话">-</el-descriptions-item>
      <el-descriptions-item label="邮箱">-</el-descriptions-item>
      <el-descriptions-item label="收件人税号">-</el-descriptions-item>
      <el-descriptions-item label="公司名称">-</el-descriptions-item>
      <el-descriptions-item label="国家/地区">-</el-descriptions-item>
      <el-descriptions-item label="省/州">-</el-descriptions-item>
      <el-descriptions-item label="城市">-</el-descriptions-item>
      <el-descriptions-item label="邮编">-</el-descriptions-item>
      <el-descriptions-item label="门牌号">-</el-descriptions-item>
      <el-descriptions-item label="地址1" :span="2">-</el-descriptions-item>
      <el-descriptions-item label="地址2" :span="2">-</el-descriptions-item>
    </el-descriptions>
  </ContentWrap>

  <ContentWrap class="mt-20" title="包裹信息">
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column label="序号" prop="name" width="80" />
      <el-table-column label="承运商" prop="name" width="180" />
      <el-table-column label="跟踪号" prop="name" width="180" />
      <el-table-column label="包裹尺寸" prop="name" width="180" />
      <el-table-column label="包裹重量" prop="name" width="180" />
      <el-table-column label="包裹计费重" prop="name" width="180" />
      <el-table-column label="SKU种类数" prop="name" width="180" />
      <el-table-column label="商品数量" prop="name" width="180" />
      <el-table-column label="物流面单" prop="name" width="180" />
    </el-table>
    <div
      class="lh-40 flex justify-between rounded-2 px-10 text-14"
      border="1px solid #ebeef5"
    >
      <div>合计</div>
      <div class="flex">
        <div>
          <span class="mr-5">包裹数:</span>
          <span>1</span>
        </div>
        <div class="ml-20">
          <span class="mr-5">SKU种类数:</span>
          <span>1</span>
        </div>
        <div class="ml-20">
          <span class="mr-5">总商品数:</span>
          <span>1</span>
        </div>
      </div>
    </div>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs v-model="active">
      <el-tab-pane label="商品明细" name="1">
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="商品编码(SKU)" prop="name" />
          <el-table-column label="产品名称" prop="name" />
          <el-table-column label="包裹序号" prop="name" />
          <el-table-column label="预报出库数量" prop="name" />
          <el-table-column label="实际出库数量" prop="name" />
          <el-table-column label="申报价格" prop="name" />
        </el-table>
        <div
          class="lh-40 flex justify-between rounded-2 px-10 text-14"
          border="1px solid #ebeef5"
        >
          <div>合计</div>
          <div class="flex">
            <div>
              <span class="mr-5">总申报价格:</span>
              <span>1</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="操作日志" name="2">
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="操作" prop="name" />
          <el-table-column label="操作方" prop="name" />
          <el-table-column label="操作人" prop="name" />
          <el-table-column label="操作时间" prop="name" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
