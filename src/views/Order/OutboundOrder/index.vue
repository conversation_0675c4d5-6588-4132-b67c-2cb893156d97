<script setup lang="ts">
import { useToggle } from '@/hooks/useToggle'
import UpdateChannel from './components/UpdateChannel.vue'
import UpdateTrackingNumber from './components/UpdateTrackingNumber.vue'

defineOptions({
  name: 'OutboundOrder'
})

const formData = ref({
  name: ''
})
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const [ucVisible, handleUC] = useToggle()
const [utnVisible, handleUTN] = useToggle()

const router = useRouter()
const linkDetail = () => {
  router.push({
    path: '/order/outboundOrder/detail'
  })
}

const value = ref('全部')
const options = [
  {
    label: '待处理',
    value: '待处理'
  },
  {
    label: '仓库处理中',
    value: '仓库处理中'
  },
  {
    label: '已出库',
    value: '已出库'
  },
  {
    label: '已取消',
    value: '已取消'
  },
  {
    label: '异常订单',
    value: '异常订单'
  },
  {
    label: '申请截单',
    value: '申请截单'
  },
  {
    label: '全部',
    value: '全部'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="仓库" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品编码(SKU)" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="销售平台" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物流渠道" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="系统渠道" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="承运商" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单来源" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="国家" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单类型" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="有无跟踪号" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上传物流面单" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计费重" prop="">
            <div class="flex">
              <el-input
                v-model.trim="formData.name"
                class="flex-1"
                clearable
                placeholder=""
              ></el-input>
              <div class="mx-4">-</div>
              <el-input
                v-model.trim="formData.name"
                class="flex-1"
                clearable
                placeholder=""
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品数量" prop="">
            <div class="flex">
              <el-input
                v-model.trim="formData.name"
                class="flex-1"
                clearable
                placeholder=""
              ></el-input>
              <div class="mx-4">-</div>
              <el-input
                v-model.trim="formData.name"
                class="flex-1"
                clearable
                placeholder=""
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="同步状态" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="lh-22"></div>
            </template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="lh-22"></div>
            </template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="lh-22"></div>
            </template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-date-picker type="daterange" placeholder="" class="flex-1" />
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
              <el-button>刷新</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button>停止同步</el-button>
      <el-button>拣货</el-button>
      <el-button>下载面单</el-button>
      <el-button @click="() => handleUC()">修改物流渠道</el-button>
      <el-button @click="() => handleUTN()">修改跟踪号</el-button>
      <el-button>移入异常</el-button>
      <el-dropdown>
        <el-button class="ml-12">
          导入/导出
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>导入跟踪号</el-dropdown-item>
            <el-dropdown-item>导入包裹信息</el-dropdown-item>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="订单详情" prop="subsidiaryName" width="180" />
      <el-table-column label="商品明细" prop="bankName" width="180" />
      <el-table-column label="仓配信息" prop="bankNumber" width="180" />
      <el-table-column label="同步信息" prop="accountName" min-width="180" />
      <el-table-column label="时间" prop="accountName" min-width="180" />

      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{}">
          <el-button type="primary" text @click="linkDetail">
            查看详情
          </el-button>
          <el-button type="primary" text> 下载面单 </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <UpdateChannel v-model="ucVisible"></UpdateChannel>
  <UpdateTrackingNumber v-model="utnVisible"></UpdateTrackingNumber>
</template>

<style lang="scss" scoped></style>
