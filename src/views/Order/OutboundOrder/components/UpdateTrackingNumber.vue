<script setup lang="ts">
defineOptions({
  name: 'UpdateChannel'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="修改跟踪号"
    width="500"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex justify-center py-20">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="left"
        @submit.prevent
        class="w-70%"
      >
        <el-form-item label="跟踪号" prop="">
          <el-input v-model="formData.name" placeholder="请输入跟踪号" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
