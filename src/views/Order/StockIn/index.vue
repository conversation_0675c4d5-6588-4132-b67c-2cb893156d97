<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import DownloadTag from './components/DownloadTag.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'

defineOptions({
  name: 'StockIn'
})

const formData = ref({
  name: ''
})
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const visible = ref(false)
const handleOpen = () => {
  visible.value = true
}

const bvisible = ref(false)
const handleResetPwd = () => {
  bvisible.value = true
}
const advisible = ref(false)
const handleAD = () => {
  advisible.value = true
}
const pbvisible = ref(false)
const handlePB = () => {
  pbvisible.value = true
}
const wdvisible = ref(false)
const handleWD = () => {
  wdvisible.value = true
}

const dtvisible = ref(false)
const handleDT = () => {
  dtvisible.value = true
}

const value = ref('全部')
const options = [
  {
    label: '全部',
    value: '全部'
  },
  {
    label: '已下单',
    value: '已下单'
    // disabled: true
  },
  {
    label: '转运中',
    value: '转运中'
  },
  {
    label: '上架完成',
    value: '上架完成'
  },
  {
    label: '已取消',
    value: '已取消'
  },
  {
    label: '异常',
    value: '异常'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="入库单号" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="跟踪号" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="国家" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="目的仓库" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库单来源" prop="">
            <el-select v-model="formData.name" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="创建时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
              class="!w-full"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="上架时间" prop="">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
              class="!w-full"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
              <el-button>刷新</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="handleAD">创建入库单</el-button>
      <el-button type="primary" @click="handleResetPwd">批量导入</el-button>
      <el-button type="primary" @click="handleWD">导出</el-button>
      <el-button type="primary" @click="handleDT">下载入库标签</el-button>
      <el-button type="primary" @click="handleWD">发货</el-button>
      <el-button type="danger">取消</el-button>
    </div>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="入库单号" prop="date" width="180" />
      <el-table-column label="关联单号" prop="date" width="180" />
      <el-table-column label="跟踪号" prop="date" width="180" />
      <el-table-column label="数量/上架数量" prop="date" min-width="180" />
      <el-table-column label="入库单来源" prop="date" width="180" />
      <el-table-column label="收货仓库" prop="date" width="180" />
      <el-table-column label="国家" prop="date" width="180" />
      <el-table-column label="尾端运输类型" prop="date" width="180" />
      <el-table-column label="备注" prop="date" width="180" />
      <el-table-column label="状态" prop="date" width="180" />
      <el-table-column label="创建时间" prop="date" width="180" />
      <el-table-column label="上架时间" prop="date" width="180" />
      <el-table-column label="操作" fixed="right" width="400">
        <template #default="{}">
          <el-button type="primary" @click="handlePB">详情</el-button>
          <el-button type="primary" @click="handleOpen">编辑</el-button>
          <el-button type="primary">下载标签</el-button>
          <el-button type="danger">取消</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <CreateEditDialog v-model="advisible"></CreateEditDialog>
  <DownloadTag v-model="dtvisible"></DownloadTag>
  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
