<script setup lang="ts">
defineOptions({
  name: 'AddDistribution'
})
const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])
</script>

<template>
  <el-dialog
    v-model="visible"
    title="创建入库单"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="24">
            <div class="text-16">基本信息</div>
          </el-col>
          <el-col :span="8">
            <el-form-item label="目的地" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="尾端运输类型" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库类型" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="text-16 my-16">产品明细</div>
      <div class="pr-16">
        <el-button type="primary" class="mb-16">添加产品</el-button>
        <el-table :data="tableData" :border="true">
          <el-table-column type="index" label="序号" width="55" />
          <el-table-column label="箱子数量" prop="date" width="180" />
          <el-table-column label="SKU" prop="date" width="180" />
          <el-table-column label="产品名称" prop="date" width="180" />
          <el-table-column label="单项数量" prop="date" width="180">
            <template #default="{}">
              <el-input v-model="formData.num" />
            </template>
          </el-table-column>
          <el-table-column label="自定义箱条码" prop="date" width="180">
            <template #default="{}">
              <el-input v-model="formData.num" />
            </template>
          </el-table-column>
          <el-table-column label="箱条码规则" prop="date" width="180">
            <template #default="{}">
              <el-input v-model="formData.num" />
            </template>
          </el-table-column>
          <el-table-column label="箱子尺寸" prop="date" width="180">
            <template #default="{}">
              <el-input v-model="formData.num" />
            </template>
          </el-table-column>
          <el-table-column label="箱子重量" prop="date" width="180">
            <template #default="{}">
              <el-input v-model="formData.num" />
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="{}">
              <el-button type="primary">编辑</el-button>
              <el-button type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
