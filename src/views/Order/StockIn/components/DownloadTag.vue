<script setup lang="ts">
defineOptions({
  name: 'Attribute'
})

const visible = defineModel({ default: false })

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="下载入库标签"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="标签模板选择" prop="">
        <el-select v-model="formData.name" clearable placeholder="">
          <el-option :value="1" label="1"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="入库单号" prop="date" width="180" />
      <el-table-column label="箱号" prop="date" width="180" />
      <el-table-column label="箱子重量" prop="date" width="180" />
      <el-table-column label="箱子尺寸" prop="date" width="180" />
    </el-table>
    <template #footer>
      <div class="flex justify-between">
        <div class="text-14 color-bluegray">已选2个标签</div>
        <div>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
