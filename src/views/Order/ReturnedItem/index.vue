<script setup lang="ts">
defineOptions({
  name: 'ReturnedItem'
})

const formData = ref({
  name: ''
})
const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const selectable = (_row: any, index: number) => {
  return index !== 1
}

const router = useRouter()
const linkDetail = () => {
  router.push({
    path: '/order/returnedItem/detail'
  })
}

const value = ref('全部')
const options = [
  {
    label: '草稿',
    value: '草稿'
  },
  {
    label: '待入库',
    value: '待入库'
  },
  {
    label: '上架中',
    value: '上架中'
  },
  {
    label: '已上架',
    value: '已上架'
  },
  {
    label: '已取消',
    value: '已取消'
  },
  {
    label: '全部',
    value: '全部'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="value" class="mb-16">
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="h-22"></div>
            </template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-input
                v-model.trim="formData.name"
                clearable
                placeholder=""
                class="flex-1"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户等级" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库" prop="">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="退件类型" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="同步状态" prop="">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label-width="0">
            <template #label>
              <div class="h-22"></div>
            </template>
            <div class="flex w-full">
              <DSelect
                v-model="formData.name"
                :options="[]"
                placeholder="请选择"
                class="!w-120"
              ></DSelect>
              <el-date-picker type="daterange" placeholder="" class="flex-1" />
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="" class="pt-30">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
              <el-button>刷新</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button>停止同步</el-button>
      <el-button>快捷上架</el-button>
      <el-button>完成上架</el-button>
      <el-dropdown>
        <el-button class="ml-12">
          导出
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table :data="tableData" :border="true" class="w-full">
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="退件单号" prop="subsidiaryName" width="180">
        <template #default="{}">
          <el-button type="primary" text @click="linkDetail">
            ASA12323232
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="仓库" prop="bankName" width="180" />
      <el-table-column label="客户" prop="bankNumber" width="180" />
      <el-table-column label="客户备注" prop="accountName" min-width="180" />
      <el-table-column
        label="预报/已收数量"
        prop="accountName"
        min-width="180"
      />
      <el-table-column label="退件类型" prop="accountName" min-width="180" />
      <el-table-column label="跟踪号" prop="accountName" min-width="180" />
      <el-table-column label="移除单号" prop="accountName" min-width="180" />
      <el-table-column label="参考单号" prop="accountName" min-width="180" />
      <el-table-column
        label="预计到达日期"
        prop="accountName"
        min-width="180"
      />
      <el-table-column label="上架时间" prop="accountName" min-width="180" />
      <el-table-column label="提交时间" prop="accountName" min-width="180" />
      <el-table-column label="创建时间" prop="accountName" min-width="180" />
      <el-table-column label="取消时间" prop="accountName" min-width="180" />
      <el-table-column label="状态" prop="accountName" min-width="180" />
      <el-table-column label="同步状态" prop="accountName" min-width="180" />

      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{}">
          <el-button type="primary" text @click="linkDetail">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
