<script setup lang="ts">
defineOptions({
  name: 'OutboundOrderDetail'
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])
</script>

<template>
  <ContentWrap>
    <el-descriptions title="订单详情" :column="3">
      <el-descriptions-item label="退件单号">-</el-descriptions-item>
      <el-descriptions-item label="仓库">-</el-descriptions-item>
      <el-descriptions-item label="退件类型">-</el-descriptions-item>
      <el-descriptions-item label="客户">-</el-descriptions-item>
      <el-descriptions-item label="参考单号">-</el-descriptions-item>
      <el-descriptions-item label="移除单号">-</el-descriptions-item>
      <el-descriptions-item label="预计到达日期">-</el-descriptions-item>
      <el-descriptions-item label="备注">-</el-descriptions-item>
    </el-descriptions>
  </ContentWrap>

  <ContentWrap class="mt-20" title="退件包裹">
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column label="跟踪号" prop="name" />
      <el-table-column label="承运商" prop="name" />
      <el-table-column label="包裹尺寸" prop="name" />
      <el-table-column label="包裹重量" prop="name" />
    </el-table>
  </ContentWrap>

  <ContentWrap class="mt-20" title="退件商品">
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column label="商品编码(SKU)" prop="name" />
      <el-table-column label="商品名称" prop="name" />
      <el-table-column label="已上架/预报数量" prop="name" />
    </el-table>
  </ContentWrap>

  <ContentWrap class="mt-20" title="操作日志">
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column label="操作" prop="name" />
      <el-table-column label="操作方" prop="name" />
      <el-table-column label="操作人" prop="name" />
      <el-table-column label="操作时间" prop="name" />
    </el-table>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
