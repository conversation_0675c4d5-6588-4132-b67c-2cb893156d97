<template>
  <div class="h-[100vh] relative flex">
    <div class="flex-1 bg-gray-500 bg-opacity-20 relative">
      <img class="w-100% h-100%" src="@/assets/imgs/loginbg.jpg" alt="" />
      <div class="absolute inset-0 bg-[#4f46e5b3]"></div>
    </div>
    <div class="flex-1 flex items-center justify-center">
      <div class="w-360">
        <div class="text-size-24 fw-600 text-center">欢迎回来</div>
        <div class="text-size-16 lh-24 mt-8 text-center">请登录您的账号</div>
        <LoginForm />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginForm from './components/LoginForm.vue'

defineOptions({
  name: 'Login'
})
</script>

<style lang="scss" scoped></style>
