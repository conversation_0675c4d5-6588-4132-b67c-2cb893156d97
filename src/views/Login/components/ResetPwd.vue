<template>
  <el-dialog v-model="visible" title="重置密码" width="500" @open="handleOpen" @close="handleClose">
    <div>
      <el-form :model="formData" :rules="rules" ref="formDataRef" label-position="top" @submit.prevent>
        <el-form-item label="账号" prop="">
          <el-input v-model.trim="formData.username" clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="邮箱验证码">
          <div class="flex w-full">
            <el-input v-model="formData.password" class="flex-[2]" />
            <div class="flex-1 h-32 bg-amber rounded-4 ml-8"></div>
          </div>
        </el-form-item>
        <el-form-item label="密码" prop="">
          <el-input v-model.trim="formData.pwd" clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="">
          <el-input v-model.trim="formData.confirmPwd" clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const visible = defineModel<boolean>()

const formData = ref({
  username: '',
  confirmPwd: '',
  password: '',
  pwd: ''
})

const rules = ref({})

const handleOpen = () => {
  console.log('handleOpen')
}

const handleClose = () => {}

const handleCancel = () => {
  visible.value = false
}

const handleConfirn = () => {}
</script>

<style lang="scss" scoped></style>
