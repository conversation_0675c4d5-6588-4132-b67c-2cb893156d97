<template>
  <el-form
    :model="form"
    :label-position="'top'"
    label-width="auto"
    style="max-width: 600px"
  >
    <el-form-item label="账号">
      <el-input v-model="form.username" />
    </el-form-item>
    <el-form-item label="密码">
      <el-input v-model="form.password" />
    </el-form-item>
    <el-form-item label="验证码">
      <div class="flex w-full">
        <el-input v-model="form.captcha" class="flex-[2]" />
        <div class="flex-1 h-32 bg-amber rounded-4 ml-8">
          <RandomImageVerify v-model="form.checkKey" />
        </div>
      </div>
    </el-form-item>
    <div class="flex justify-between items-center mb-18">
      <el-checkbox v-model="rememberMe" label="记住我" />
      <div
        class="flex items-center text-14 color-blue cursor-pointer"
        @click="handleResetPwd"
      >
        <span>忘记密码</span>
        <div class="i-ri:question-line text-16 ml-4"></div>
      </div>
    </div>
    <el-button type="primary" class="w-full mb-18" @click="handleSignIn"
      >登录</el-button
    >
  </el-form>

  <ResetPwd v-model="visible" />
</template>

<script lang="ts" setup>
// import { useStorage } from '@/hooks/useStorage'
import ResetPwd from './ResetPwd.vue'
import { useUserStore } from '@/store/modules/user'
import { asyncRouter } from '@/router/asyncRouter'
import { usePermissionStore } from '@/store/modules/permission'
import type { RouteRecordRaw } from 'vue-router'
import type { LoginRequest } from '@/api/login/types'
import RandomImageVerify from './RandomImageVerify.vue'

// const { setStorage } = useStorage()

const form = reactive<LoginRequest>({
  username: 'admin',
  password: 'Passw0rd@!',
  captcha: '2587',
  checkKey: '',
  type: 'base'
})

const rememberMe = ref(false)

const visible = ref(false)

const handleResetPwd = () => {
  visible.value = true
}

const userStore = useUserStore()
const permissionStore = usePermissionStore()

const { addRoute, push } = useRouter()

const handleSignIn = async () => {
  await userStore.doLogin(form)
  userStore.setUserInfo(form)
  getRole()
}

const getRole = async () => {
  const routers = asyncRouter
  userStore.setRoleRouters(routers)
  await permissionStore.generateRoutes('server', routers).catch(() => {})
  permissionStore.getAddRouters.forEach(route => {
    addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  })
  permissionStore.setIsAddRouters(true)

  push({ path: permissionStore.addRouters[0].path })
}
</script>

<style lang="scss" scoped></style>
