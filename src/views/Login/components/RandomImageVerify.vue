<script setup lang="ts">
defineOptions({
  name: 'RandomImageVerify'
})

// const codeSrc = defineModel({ default: '' })
const codeSrc = ref('')
const timeKey = defineModel<string | undefined>({ default: undefined })

const handleUpdateCode = () => {
  timeKey.value = new Date().getTime().toString()
  // getRandomImage(timeKey).then(res => {
  //   codeSrc.value = res.result
  // })
}

handleUpdateCode()
</script>

<template>
  <img :src="codeSrc" alt="" @click="handleUpdateCode" />
</template>

<style lang="scss" scoped></style>
