<script setup lang="ts">
defineOptions({
  name: 'EditGroup'
})

const visible = defineModel({ default: false })

// 接收分组数据
const props = defineProps<{
  groupData?: any[]
}>()

const formData = ref({
  name: '',
  num: 0
})
const rules = ref({})

const handleOpen = () => {
  visible.value = true
  // 打印接收到的分组数据
  console.log('EditGroup 接收到的分组数据:', props.groupData)
}

const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑分组"
    width="600"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row class="w-full">
          <el-col :span="24">
            <el-form-item label="海外仓入库标签来源" prop="">
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option :value="1" label="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
