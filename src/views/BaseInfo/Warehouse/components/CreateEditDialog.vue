<script setup lang="ts">
import { getServiceProviderAll } from '@/api/common'
import { saveWarehouse, updateWarehouse } from '@/api/warehouse'
import type { WarehouseEntity } from '@/api/warehouse/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const warehouse_type = inject('warehouse_type') as DictItemEntity[]
const country = inject('country') as DictItemEntity[]

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<WarehouseEntity>(
    {
      id: undefined,
      // status: 0,
      warehouseName: undefined,
      warehouseCode: undefined,
      warehouseType: undefined,
      contacts: undefined,
      contactsPhone: undefined,
      postalCode: undefined,
      country: undefined,
      countryCode: undefined,
      province: undefined,
      provinceCode: undefined,
      city: undefined,
      cityCode: undefined,
      detailedAddressOne: undefined,
      detailedAddressTwo: undefined,
      belongEnterprise: undefined,
      labelSource: undefined,
      warehousePartners: undefined
    },
    {
      warehouseCode: [
        { required: true, message: '请输入仓库代码', trigger: 'blur' }
      ],
      warehouseType: [
        { required: true, message: '请选择仓库类型', trigger: 'change' }
      ],
      contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
      belongEnterprise: [
        { required: true, message: '请输入归属企业', trigger: 'blur' }
      ],
      countryCode: [
        { required: true, message: '请选择国家', trigger: 'change' }
      ],
      city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
      detailedAddressOne: [
        { required: true, message: '请输入地址一', trigger: 'blur' }
      ],
      postalCode: [{ required: true, message: '请输入邮编', trigger: 'blur' }]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '创建仓库',
  EDIT: '编辑仓库',
  DETAIL: '仓库详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const service_provider_type = ref<any[]>([])
// 获取归属企业下拉选项
const getServiceProviderAllOptions = async () => {
  const res = await getServiceProviderAll()
  service_provider_type.value = res.result
}

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveWarehouse,
  EDIT: updateWarehouse,
  DETAIL: () => Promise.resolve({})
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  getServiceProviderAllOptions()
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        :disabled="disabled"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input
                v-model.trim="formData.warehouseName"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库代码" prop="warehouseCode">
              <el-input
                v-model.trim="formData.warehouseCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库类型" prop="warehouseType">
              <DSelect
                v-model="formData.warehouseType"
                :options="warehouse_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contacts">
              <el-input
                v-model.trim="formData.contacts"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属企业" prop="belongEnterprise">
              <DSelect
                v-model="formData.belongEnterprise"
                :options="service_provider_type"
                placeholder="请选择"
                :fields="{
                  label: 'serviceProviderName',
                  value: 'serviceProviderName'
                }"
              ></DSelect>
              <!-- <el-input
                v-model.trim="formData.belongEnterprise"
                clearable
                placeholder=""
              ></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国家" prop="countryCode">
              <DSelect
                v-model="formData.countryCode"
                :options="country"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省/州" prop="province">
              <el-input
                v-model.trim="formData.province"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-input
                v-model.trim="formData.city"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址一" prop="">
              <el-input
                v-model.trim="formData.detailedAddressOne"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址二" prop="">
              <el-input
                v-model.trim="formData.detailedAddressTwo"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="邮编" prop="postalCode">
              <el-input
                v-model.trim="formData.postalCode"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input
                v-model.trim="formData.contactsPhone"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="" v-if="!disabled">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
