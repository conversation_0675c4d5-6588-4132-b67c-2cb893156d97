<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import EditGroup from './components/EditGroup.vue'
import { useTable } from '@/hooks/useTable'
import {
  delWarehouseById,
  disableWarehouse,
  enableWarehouse,
  getList
} from '@/api/warehouse'
import type { WarehouseEntity } from '@/api/warehouse/types'
import { ElMessage } from 'element-plus'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'Warehouse'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')
const country = useDictItem('country')
// const storage_type = useDictItem('storage_type')
provide('warehouse_type', warehouse_type)
provide('service_provider_type', service_provider_type)
provide('country', country)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<WarehouseEntity>({
  immediate: true,
  initialFormData: {
    status: '',
    warehousePartners: undefined,
    warehouseName: undefined,
    warehouseType: undefined,
    warehouseCode: undefined,
    postalCode: undefined
  },
  fetchDataApi: async () => {
    const res = await getList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    console.log('record', record)
    const ids = record.map((item: any) => item.id?.toString())
    const res = await delWarehouseById(ids)
    return !!res
  }
})

const tableRef = ref()

const handleStatusChange = async (type: '0' | '1') => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择仓库')
    return
  }
  const ids = selection.value.map(item => item.id?.toString())
  if (type === '0') {
    const result = selection.value.filter(
      item => item.status?.toString() === '0'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的仓库中存在停用的仓库')
      return
    }
    await disableWarehouse(ids as string[])
  } else if (type === '1') {
    const result = selection.value.filter(
      item => item.status?.toString() === '1'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的仓库中存在启用的仓库')
      return
    }
    await enableWarehouse(ids as string[])
  }
  // clearSelection
  tableMethods.handleQuery()
}

const handleDelete = () => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择仓库')
    return
  }
  tableMethods.hadnleDel(selection.value)
}

const bvisible = ref(false)
const handleResetPwd = () => {
  bvisible.value = true
}
const egvisible = ref(false)
const currentGroupData = ref<any[]>([])

const handleEG = (row?: any) => {
  // 如果没有传入行数据，提示用户选择
  if (!row) {
    ElMessage.warning('请先选择要编辑分组的仓库')
    return
  }

  // 获取当前行所属分组的所有数据
  const currentGroupName = row.belongEnterprise
  const currentGroupItems = dataList.value.filter(
    (item: any) => item.belongEnterprise === currentGroupName
  )

  // 设置当前分组数据
  currentGroupData.value = [
    {
      groupName: currentGroupName,
      items: currentGroupItems
    }
  ]

  console.log('当前选中分组数据:', currentGroupData.value)
  egvisible.value = true
}

// 表格合并单元格方法
const objectSpanMethod = ({ row, rowIndex, columnIndex }: any) => {
  if (columnIndex === 1) {
    // 仓库合作商列
    const belongEnterprise = row.belongEnterprise
    const sameGroupRows = dataList.value.filter(
      (item: any) => item.belongEnterprise === belongEnterprise
    )
    const firstIndex = dataList.value.findIndex(
      (item: any) => item.belongEnterprise === belongEnterprise
    )

    if (rowIndex === firstIndex) {
      return {
        rowspan: sameGroupRows.length,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  if (columnIndex === 14) {
    // 第二个操作列
    const belongEnterprise = row.belongEnterprise
    const sameGroupRows = dataList.value.filter(
      (item: any) => item.belongEnterprise === belongEnterprise
    )
    const firstIndex = dataList.value.findIndex(
      (item: any) => item.belongEnterprise === belongEnterprise
    )

    if (rowIndex === firstIndex) {
      return {
        rowspan: sameGroupRows.length,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

const headerMethodCB = ({ row, columnIndex }: any): any => {
  if (row[0].level === 1) {
    // 第 13  14 列合并
    row[13].colSpan = 2
    row[14].colSpan = 0
    // 根据列数进行样式赋予
    if (columnIndex === 14) {
      return { display: 'none' }
    }
  }
}

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: '1'
    // disabled: true
  },
  {
    label: '停用',
    value: '0'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.status"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="仓库合作商" prop="">
            <el-input
              v-model.trim="formData.warehousePartners"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="仓库名称" prop="">
            <el-input
              v-model.trim="formData.warehouseName"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="仓库类型" prop="">
            <DSelect
              v-model="formData.warehouseType"
              :options="warehouse_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="仓库代码" prop="">
            <el-input
              v-model.trim="formData.warehouseCode"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="邮编" prop="">
            <el-input
              v-model.trim="formData.postalCode"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :offset="12" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新增</el-button
      >
      <el-button type="primary" @click="handleResetPwd">批量创建</el-button>
      <el-button type="danger" @click="() => handleDelete()">删除</el-button>
      <el-button type="primary">导出</el-button>
      <el-button type="primary" @click="handleStatusChange('1')"
        >启用</el-button
      >
      <el-button type="warning" @click="handleStatusChange('0')"
        >停用</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      row-key="id"
      :span-method="objectSpanMethod"
      :header-cell-style="headerMethodCB"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="仓库合作商" prop="belongEnterprise" width="150" />
      <el-table-column label="仓库类型" prop="warehouseTypeName" width="180" />
      <el-table-column label="仓库名称" prop="warehouseName" width="180" />
      <el-table-column label="仓库代码" prop="warehouseCode" width="180" />
      <el-table-column label="联系人" prop="contacts" width="180" />
      <el-table-column label="地址" prop="detailedAddressOne" width="180" />
      <el-table-column label="城市" prop="city" width="100" />
      <el-table-column label="省/州" prop="province" width="100" />
      <el-table-column label="国家" prop="country" width="120" />
      <el-table-column label="邮编" prop="postalCode" width="120" />
      <el-table-column label="联系电话" prop="contactsPhone" width="120" />
      <el-table-column label="入库标签来源" prop="labelSource" width="180" />
      <el-table-column label="操作" fixed="right" width="160">
        <template #default="{ row }">
          <el-button
            type="primary"
            @click="() => tableMethods.handleDetail(row)"
            >详情</el-button
          >
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button type="danger" @click="() => handleEG(row)"
            >编辑分组</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>

  <EditGroup v-model="egvisible" :group-data="currentGroupData"></EditGroup>
</template>

<style lang="scss" scoped></style>
