<script setup lang="ts">
import { saveLocation, updateLocation } from '@/api/location'
import type { LocationEntity } from '@/api/location/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const location_type = inject('location_type') as DictItemEntity[]
const location_attribution = inject('location_attribution') as DictItemEntity[]
const country = inject('country') as DictItemEntity[]

const { formData, formDataRef, rules, resetFormData } =
  useFormData<LocationEntity>(
    {
      id: undefined,
      locationName: undefined,
      locationAbbr: undefined,
      locationCode: undefined,
      locationAttr: undefined,
      locationType: undefined,
      locationTypeName: undefined,
      country: undefined,
      countryCode: undefined,
      province: undefined,
      provinceCode: undefined,
      city: undefined,
      cityCode: undefined,
      detailedAddressOne: undefined,
      detailedAddressTwo: undefined,
      postalCode: undefined,
      contacts: undefined,
      contactsPhone: undefined
      // status: '0'
    },
    {
      locationName: [
        { required: true, message: '请输入地点名称', trigger: 'blur' }
      ],
      locationAbbr: [
        { required: true, message: '请输入地点简称', trigger: 'blur' }
      ],
      locationCode: [
        { required: true, message: '请输入地点代码', trigger: 'blur' }
      ],
      locationAttr: [
        { required: true, message: '请选择地点归属', trigger: 'change' }
      ],
      locationType: [
        { required: true, message: '请选择地点类型', trigger: 'change' }
      ],
      countryCode: [
        { required: true, message: '请选择地区', trigger: 'change' }
      ],
      city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
      detailedAddressOne: [
        { required: true, message: '请输入详细地址1', trigger: 'blur' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新增地图',
  EDIT: '编辑地图',
  DETAIL: '地图详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveLocation,
  EDIT: updateLocation,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        require-asterisk-position="right"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="地点名称" prop="locationName">
              <el-input
                v-model.trim="formData.locationName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地点简称" prop="locationAbbr">
              <el-input
                v-model.trim="formData.locationAbbr"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地点代码" prop="locationCode">
              <el-input
                v-model.trim="formData.locationCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地点归属" prop="locationAttr">
              <DSelect
                v-model="formData.locationAttr"
                :options="location_attribution"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地点类型" prop="locationType">
              <DSelect
                v-model="formData.locationType"
                :options="location_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国家" prop="countryCode">
              <DSelect
                v-model="formData.countryCode"
                :options="country"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省/州">
              <el-input
                v-model.trim="formData.province"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-input
                v-model.trim="formData.city"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="详细地址1" prop="detailedAddressOne">
              <el-input
                v-model.trim="formData.detailedAddressOne"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="详细地址2" prop="detailedAddressTwo">
              <el-input
                v-model.trim="formData.detailedAddressTwo"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮编" prop="postalCode">
              <el-input
                v-model.trim="formData.postalCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contacts">
              <el-input
                v-model.trim="formData.contacts"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactsPhone">
              <el-input
                v-model.trim="formData.contactsPhone"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
