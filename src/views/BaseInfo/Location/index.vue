<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import { useTable } from '@/hooks/useTable'
import type { LocationEntity } from '@/api/location/types'
import {
  delByIdLocation,
  disableLocation,
  enableLocation,
  getLocationList
} from '@/api/location'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'Location'
})

const user_status = useDictItem('user_status')
const location_type = useDictItem('location_type')
const country = useDictItem('country')
const location_attribution = useDictItem('location_attribution')

provide('location_type', location_type)
provide('location_attribution', location_attribution)
provide('country', country)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<LocationEntity>({
  immediate: true,
  initialFormData: {
    status: undefined,
    locationType: undefined,
    locationAttr: undefined,
    locationName: undefined
  },
  fetchDataApi: async () => {
    const res = await getLocationList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    const res = await delByIdLocation([record.id])
    return !!res
  }
})

const handleStatusChange = async (type: '0' | '1', row: LocationEntity) => {
  if (type === '0') {
    await disableLocation([row.id as string])
  } else if (type === '1') {
    await enableLocation([row.id as string])
  }
  tableMethods.handleQuery()
}

const bvisible = ref(false)
const handleResetPwd = () => {
  bvisible.value = true
}
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="地点信息" prop="">
            <el-input
              v-model.trim="formData.locationName"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="地点类型" prop="">
            <DSelect
              v-model="formData.locationType"
              :options="location_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <DSelect
              v-model="formData.status"
              :options="user_status"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="地点归属" prop="">
            <DSelect
              v-model="formData.locationAttr"
              :options="location_attribution"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary" @click="tableMethods.handleQuery"
              >搜索</el-button
            >
            <el-button @click="tableMethods.handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >新建地点</el-button
      >
      <el-button type="primary">导出</el-button>
      <el-button type="primary" @click="handleResetPwd">导入</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="地点名称" prop="locationName" width="180" />
      <el-table-column label="地点简称" prop="locationAbbr" width="180" />
      <el-table-column
        label="地点归属"
        prop="locationAttrText"
        min-width="180"
      />
      <el-table-column label="地点类型" prop="locationTypeText" width="180" />
      <el-table-column label="状态" prop="statusText" width="180" />
      <el-table-column label="详细地址" prop="detailedAddressOne" width="180">
        <template #default="{ row }">
          {{
            row.detailedAddressTwo
              ? `${row.detailedAddressTwo}, ${row.detailedAddressOne}`
              : row.detailedAddressOne
          }}
        </template>
      </el-table-column>
      <el-table-column label="邮编号码" prop="postalCode" width="180" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button type="primary" @click="tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            v-if="row.status === 1"
            type="warning"
            @click="handleStatusChange('0', row)"
            >停用</el-button
          >
          <el-button
            v-if="row.status === 0"
            type="primary"
            @click="handleStatusChange('1', row)"
            >启用</el-button
          >
          <el-button type="danger" @click="tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="tableMethods.handleQuery"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
