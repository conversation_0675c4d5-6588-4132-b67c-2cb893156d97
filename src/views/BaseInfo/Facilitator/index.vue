<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
import BatchCreateDialog from './components/BatchCreateDialog.vue'
import { useTable } from '@/hooks/useTable'
import type { FacilitatorEntity } from '@/api/facilitator/types'
import {
  delByIdService,
  disableService,
  enableService,
  getServiceList
} from '@/api/facilitator'
import { ElMessage } from 'element-plus'
import { useDictItem } from '@/hooks/useDictItem'

defineOptions({
  name: 'Facilitator'
})

const service_provider_type = useDictItem('service_provider_type')
const country = useDictItem('country')

provide('service_provider_type', service_provider_type)
provide('country', country)

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<FacilitatorEntity>({
  immediate: true,
  initialFormData: {
    status: '',
    serviceProviderName: undefined,
    serviceProviderAbbr: undefined,
    serviceProviderCode: undefined,
    serviceProviderType: undefined
  },
  fetchDataApi: async () => {
    const res = await getServiceList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await delByIdService(ids)
      return !!res
    } else {
      const res = await delByIdService([record.id])
      return !!res
    }
  }
})

const handleStatusChange = async (type: '0' | '1') => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择仓库')
    return
  }
  const ids = selection.value.map(item => item.id?.toString())
  if (type === '0') {
    const result = selection.value.filter(
      item => item.status?.toString() === '0'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的仓库中存在停用的仓库')
      return
    }
    await disableService(ids as string[])
  } else if (type === '1') {
    const result = selection.value.filter(
      item => item.status?.toString() === '1'
    )
    if (result?.length > 0) {
      ElMessage.error('选中的仓库中存在启用的仓库')
      return
    }
    await enableService(ids as string[])
  }
  // clearSelection
  tableMethods.handleQuery()
}

const handleDelete = () => {
  if (selection.value?.length === 0) {
    ElMessage.error('请选择仓库')
    return
  }
  tableMethods.hadnleDel(selection.value)
}

const bvisible = ref(false)
const handleResetPwd = () => {
  bvisible.value = true
}

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: '1'
  },
  {
    label: '停用',
    value: '0'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.status"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="top"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="地点名称" prop="">
            <el-input
              v-model.trim="formData.serviceProviderAbbr"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="地址代码" prop="">
            <el-input
              v-model.trim="formData.serviceProviderCode"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="服务商" prop="">
            <el-input
              v-model.trim="formData.serviceProviderName"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="服务商类型" prop="">
            <DSelect
              v-model="formData.serviceProviderType"
              :options="service_provider_type"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :offset="18" :span="6">
          <div class="w-full flex justify-end">
            <el-button type="primary" @click="() => tableMethods.handleQuery()"
              >搜索</el-button
            >
            <el-button @click="() => tableMethods.handleReset()"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-button type="primary" @click="() => tableMethods.handleAdd()"
        >创建服务商</el-button
      >
      <el-button type="primary" @click="handleResetPwd">批量创建</el-button>
      <el-button type="danger" @click="() => handleDelete()">删除</el-button>
      <el-button type="primary">导出</el-button>
      <el-button type="primary" @click="handleStatusChange('1')"
        >启用</el-button
      >
      <el-button type="warning" @click="handleStatusChange('0')"
        >停用</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="服务商" prop="serviceProviderName" width="180" />
      <el-table-column
        label="地址名称"
        prop="serviceProviderAbbr"
        width="180"
      />
      <el-table-column
        label="地址代码"
        prop="serviceProviderCode"
        width="180"
      />
      <el-table-column
        label="服务商类型"
        prop="serviceProviderTypeText"
        min-width="180"
      />
      <el-table-column label="联系人" prop="contacts" width="180" />
      <el-table-column label="地址" prop="detailedAddressOne" width="180"
        ><template #default="{ row }">
          {{
            row.detailedAddressTwo
              ? `${row.detailedAddressTwo}, ${row.detailedAddressOne}`
              : row.detailedAddressOne
          }}
        </template>
      </el-table-column>
      <el-table-column label="城市" prop="city" width="180" />
      <el-table-column label="省/州" prop="province" width="180" />
      <el-table-column label="邮编" prop="postalCode" width="180" />
      <el-table-column label="国家" prop="country" width="180" />
      <el-table-column label="联系电话" prop="contactsPhone" width="180" />
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="{ row }">
          <el-button type="primary" @click="() => tableMethods.handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            type="primary"
            plain
            @click="() => tableMethods.handleDetail(row)"
            >详情</el-button
          >
          <el-button type="danger" @click="() => tableMethods.hadnleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.handleQuery()"
  ></CreateEditDialog>

  <BatchCreateDialog v-model="bvisible"></BatchCreateDialog>
</template>

<style lang="scss" scoped></style>
