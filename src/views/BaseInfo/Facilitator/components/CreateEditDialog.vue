<script setup lang="ts">
import { saveService, updateService } from '@/api/facilitator'
import type { FacilitatorEntity } from '@/api/facilitator/types'
import { useFormData } from '@/hooks/useFormData'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const service_provider_type = inject(
  'service_provider_type'
) as DictItemEntity[]
const country = inject('country') as DictItemEntity[]

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const { formData, formDataRef, rules, resetFormData } =
  useFormData<FacilitatorEntity>(
    {
      id: undefined,
      serviceProviderName: undefined,
      serviceProviderAbbr: undefined,
      serviceProviderCode: undefined,
      serviceProviderType: undefined,
      contacts: undefined,
      contactsPhone: undefined,
      country: undefined,
      countryCode: undefined,
      province: undefined,
      provinceCode: undefined,
      city: undefined,
      cityCode: undefined,
      detailedAddressOne: undefined,
      detailedAddressTwo: undefined,
      postalCode: undefined
      // status: '1'
    },
    {
      serviceProviderName: [
        { required: true, message: '请输入服务商', trigger: 'blur' }
      ],
      serviceProviderAbbr: [
        { required: true, message: '请输入服务商简称', trigger: 'blur' }
      ],
      serviceProviderCode: [
        { required: true, message: '请输入服务商代码', trigger: 'blur' }
      ],
      contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
      countryCode: [
        { required: true, message: '请选择国家', trigger: 'change' }
      ],
      city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
      detailedAddressOne: [
        { required: true, message: '请输入地址一', trigger: 'blur' }
      ]
    }
  )

const titleMap: Record<DlViewType, string> = {
  ADD: '新增服务商',
  EDIT: '编辑服务商',
  DETAIL: '服务商详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveService,
  EDIT: updateService,
  DETAIL: () => Promise.resolve({})
}

const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  resetFormData()
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value?.validate(async (valid: any) => {
    if (valid) {
      await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    destroy-on-close
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        :disabled="disabled"
        require-asterisk-position="right"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="服务商" prop="serviceProviderName">
              <el-input
                v-model.trim="formData.serviceProviderName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商简称" prop="serviceProviderAbbr">
              <el-input
                v-model.trim="formData.serviceProviderAbbr"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商代码" prop="serviceProviderCode">
              <el-input
                v-model.trim="formData.serviceProviderCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contacts">
              <el-input
                v-model.trim="formData.contacts"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="">
              <el-input
                v-model.trim="formData.contactsPhone"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国家" prop="countryCode">
              <DSelect
                v-model="formData.countryCode"
                :options="country"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省/州" prop="">
              <el-input
                v-model.trim="formData.province"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-input
                v-model.trim="formData.city"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址一" prop="detailedAddressOne">
              <el-input
                v-model.trim="formData.detailedAddressOne"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址二" prop="">
              <el-input
                v-model.trim="formData.detailedAddressTwo"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商类型" prop="">
              <DSelect
                v-model="formData.serviceProviderType"
                :options="service_provider_type"
                placeholder="请选择"
              ></DSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮编" prop="">
              <el-input
                v-model.trim="formData.postalCode"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
