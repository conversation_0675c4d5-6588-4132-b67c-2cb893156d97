<script setup lang="ts">
import TabFirst from './components/TabFirst/index.vue'
import TabSecond from './components/TabSecond/index.vue'
defineOptions({
  name: 'WarehouseProxyConfiguration'
})
const active = ref(1)
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="active">
      <el-tab-pane label="官网仓库" :name="1">
        <TabFirst></TabFirst>
      </el-tab-pane>
      <el-tab-pane label="主仓库存" :name="2">
        <TabSecond></TabSecond>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
