<script setup lang="ts">
// import { saveAttribute, updateAttribute } from '@/api/attribute'

defineOptions({
  name: 'SetUpDistributionInventory'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

// const { viewEntity } = defineProps<{
//   viewEntity: ViewEntity
// }>()

const initialFormData = () => {
  return {
    id: undefined,
    name: undefined,
    value: undefined,
    inputType: undefined,
    isAddValue: undefined,
    limitValue: undefined,
    description: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()

// const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
//   ADD: saveAttribute,
//   EDIT: updateAttribute,
//   DETAIL: () => Promise.resolve({})
// }

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  // if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
  //   formData.value = Object.assign(formData.value, viewEntity.record)
  // }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      // await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}

const tableData = ref([
  {
    name: 1
  }
])
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="库存调整"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="px-16">
      <el-button type="primary" plain>添加商品</el-button>
      <el-table
        :data="tableData"
        :border="true"
        style="width: 100%"
        class="mt-20"
      >
        <el-table-column label="客户" prop="date" width="180" />
        <el-table-column label="商品编码" prop="date" width="180" />
        <el-table-column label="商品名称" prop="date" width="180" />
        <el-table-column label="仓库" prop="date" width="180" />
        <el-table-column label="可用库存(常规)" prop="date" width="180" />
        <el-table-column label="可用库存(分销)" prop="date" width="180" />
        <el-table-column label="调整量" prop="date" width="180">
          <template #default="{ row }">
            <el-select
              v-model="row.name"
              clearable
              placeholder=""
              class="!w-120"
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
            <el-input
              v-model.trim="row.name"
              clearable
              placeholder=""
            ></el-input>
          </template>
        </el-table-column>

        <el-table-column label="备注" prop="date" width="180">
          <template #default="{}">
            <el-date-picker type="date" placeholder="" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="{}">
            <el-button type="danger" text>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
