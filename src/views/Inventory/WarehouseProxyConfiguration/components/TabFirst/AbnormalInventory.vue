<script setup lang="ts">
// import { saveAttribute, updateAttribute } from '@/api/attribute'

defineOptions({
  name: 'AbnormalInventory'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

// const { viewEntity } = defineProps<{
//   viewEntity: ViewEntity
// }>()

const initialFormData = () => {
  return {
    id: undefined,
    name: undefined,
    value: undefined,
    inputType: undefined,
    isAddValue: undefined,
    limitValue: undefined,
    description: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()

// const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
//   ADD: saveAttribute,
//   EDIT: updateAttribute,
//   DETAIL: () => Promise.resolve({})
// }

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  // if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
  //   formData.value = Object.assign(formData.value, viewEntity.record)
  // }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      // await apiMap[viewEntity.type](formData.value)
      emits('refresh')
      handleCancel()
    }
  })
}

const tableData = ref([
  {
    name: 1
  }
])
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    title="移入异常库存"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="px-16">
      <el-descriptions title="" :column="2">
        <el-descriptions-item label="客户"></el-descriptions-item>
        <el-descriptions-item label="商品名称"></el-descriptions-item>
        <el-descriptions-item label="仓库"></el-descriptions-item>
        <el-descriptions-item label="商品编码"></el-descriptions-item>
      </el-descriptions>
      <el-table
        :data="tableData"
        :border="true"
        style="width: 100%"
        class="mt-20"
      >
        <el-table-column label="库存类型" prop="date" width="180" />
        <el-table-column label="可用库存(常规)" prop="date" width="180" />
        <el-table-column label="可用库存(分销)" prop="date" width="180" />
        <el-table-column label="买家锁定库存(分销)" prop="date" width="180">
        </el-table-column>
      </el-table>

      <div class="flex items-center mt-20">
        <div class="w-80">异常原因</div>
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
        ></el-input>
      </div>
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
