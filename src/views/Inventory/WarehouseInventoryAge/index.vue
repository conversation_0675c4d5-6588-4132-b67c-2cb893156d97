<script setup lang="ts">
defineOptions({
  name: 'WarehouseInventoryAge'
})

const formData = ref({
  name: ''
})

const dataList = ref([
  {
    name: '1',
    code: '1',
    status: '1',
    createTime: '2024-08-14 14:20:30',
    updateTime: '2024-08-14 14:20:30'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-width="80px"
      @submit.prevent
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="商品编码">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品名称">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="仓库">
            <DSelect
              v-model="formData.name"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库批次号">
            <el-input
              v-model.trim="formData.name"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="统计时间">
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :offset="6" :span="6">
          <el-form-item label="" prop="">
            <div class="w-full flex justify-end">
              <el-button type="primary">搜索</el-button>
              <el-button>重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>
  <ContentWrap class="mt-20">
    <div class="mb-18 flex justify-between">
      <div>
        <el-dropdown>
          <el-button class="ml-12">
            <span>导出</span>
            <div class="i-ep:arrow-down ml-4"></div>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>导出选中项</el-dropdown-item>
              <el-dropdown-item>导出筛选项</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <el-table :data="dataList" :border="true" class="w-full">
      <el-table-column type="selection" width="55" />
      <el-table-column label="商品编码(SKU)" prop="inputType" width="180" />
      <el-table-column label="商品名称" prop="value" />
      <el-table-column label="客户" prop="id" width="180" />
      <el-table-column label="仓库" prop="isAddValue" width="180" />
      <el-table-column label="尺寸" prop="isAddValue" width="180" />
      <el-table-column label="体积" prop="isAddValue" width="180" />
      <el-table-column label="重量" prop="isAddValue" width="180" />
      <el-table-column label="入库时间" prop="isAddValue" width="180" />
      <el-table-column label="入库批次号" prop="isAddValue" width="180" />
      <el-table-column label="统计时间" prop="isAddValue" width="180" />
      <el-table-column label="在库总体积" prop="isAddValue" width="180" />
      <el-table-column label="在库总重量" prop="isAddValue" width="180" />
      <el-table-column label="在库总数量" prop="isAddValue" width="180" />
      <el-table-column label="0-30天数量" prop="isAddValue" width="180" />
      <el-table-column label="31-90天数量" prop="isAddValue" width="180" />
      <el-table-column label="91-180天数量" prop="isAddValue" width="180" />
      <el-table-column label="180天以上数量" prop="isAddValue" width="180" />
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
