/**
 * 把对象转为formData
 */
export function objToFormData(obj: Recordable) {
  const formData = new FormData()
  Object.keys(obj).forEach(key => {
    formData.append(key, obj[key])
  })
  return formData
}

/**
 * @description: 计算ElTable 合并行的个数
 * @param {any} tableData 表格的数据
 * @param {string} key 合并的字段
 * @return {number[]}
 */
export const handleTableDataSpan = (tableData: any, key: string): number[] => {
  const rowSpanList = <number[]>[]
  let position = 0
  for (const [index, item] of tableData.entries()) {
    if (index == 0) {
      rowSpanList.push(1)
      position = 0
    } else {
      if (item[key]) {
        if (item[key] == tableData[index - 1][key]) {
          rowSpanList[position] += 1 //项目名称相同，合并到同一个数组中
          rowSpanList.push(0)
        } else {
          rowSpanList.push(1)
          position = index
        }
      } else {
        rowSpanList.push(1)
        position = index
      }
    }
  }
  return rowSpanList
}

/**
 * @description: 根据字段code获取name
 * @param {any} list
 * @param {string} code
 * @return {*}
 */
export const getNameByCode = (list: any, code: string) => {
  const item = list.find((item: any) => item.itemValue == code)
  return item ? item.itemText : ''
}
