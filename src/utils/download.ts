/**
 * @description: 根据文件地址获取文件名称
 * @return {*}
 */
const getFileNameByUrl = (url: string) => {
  const startIndex = url.lastIndexOf('/') + 1
  const endIndex = url.indexOf('?')
  if (endIndex === -1) {
    return url.slice(startIndex)
  }
  const fileName = url.slice(startIndex, endIndex)
  return fileName
}
/**
 * @description: 导出文件
 * @param {string} _url
 * @param {string} name
 * @return {*}
 */
export const downloadFile = (_url: string) => {
  const url = _url
  // const suffix = name.slice(name.lastIndexOf('.'))
  // const filename = Date.now() + suffix
  const filename = getFileNameByUrl(_url)

  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      const blobUrl = URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(blobUrl)
      link.remove()
    })
}
