// 模拟接口返回的路由
export const asyncRouter: AppCustomRouteRecordRaw[] = [
  // {
  //   path: '/dashboard',
  //   component: '#',
  //   name: 'dashboard',
  //   redirect: '/dashboard/analysis',
  //   meta: {},
  //   children: [
  //     {
  //       path: '/dashboard/analysis',
  //       component: () => import('@/views/Dashboard/index.vue'),
  //       name: 'dashboardAnalysis',
  //       meta: {}
  //     }
  //   ]
  // },
  {
    path: '/customer',
    component: '#',
    name: 'customer',
    redirect: '/customer/fundAccount',
    meta: {
      title: '客户管理',
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/customer/stockist',
        component: 'views/Customer/Stockist/index',
        name: 'customerStockist',
        meta: {
          title: '卖家管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/customer/fundAccount',
        component: 'views/Customer/FundAccount/index',
        name: 'customerFundAccount',
        meta: {
          title: '资金账户管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/customer/rechargeReview',
        component: 'views/Customer/RechargeReview/index',
        name: 'customerRechargeReview',
        meta: {
          title: '充值管理审核',
          icon: 'i-ri:accessibility-fill'
        }
      }
      // {
      //   path: '/customer/businessSetting',
      //   component: 'views/Customer/BusinessSetting/index',
      //   name: 'businessSetting',
      //   meta: {
      //     title: '业务设置',
      //     icon: 'i-ri:accessibility-fill'
      //   }
      // },
      // {
      //   path: '/customer/account',
      //   component: 'views/Customer/Account/index',
      //   name: 'account',
      //   meta: {
      //     title: '账户管理',
      //     icon: 'i-ri:accessibility-fill'
      //   }
      // }
    ]
  },
  {
    path: '/waybill',
    component: '#',
    name: 'waybill',
    redirect: '/waybill/firstLeg',
    meta: {
      title: '运单管理',
      alwaysShow: true,
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/waybill/firstLeg',
        component: 'views/Waybill/FirstLeg/index',
        name: 'orderFirstLeg',
        meta: {
          title: '头程运单管理',
          icon: 'i-ri:accessibility-fill',
          alwaysShow: true
        },
        children: [
          {
            path: '/waybill/firstLeg/create',
            component: 'views/Waybill/FirstLeg/Create/index',
            name: 'orderFirstLegCreate',
            meta: {
              title: '创建头程运单',
              hidden: true
            }
          },
          {
            path: '/waybill/firstLeg/edit',
            component: 'views/Waybill/FirstLeg/Edit/index',
            name: 'orderFirstLegEdit',
            meta: {
              title: '编辑头程运单',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/waybill/overseasWarehouseReceipt',
        component: 'views/Waybill/OverseasWarehouseReceipt/index',
        name: 'WaybillOverseasWarehouseReceipt',
        meta: {
          title: '海外仓入库单管理',
          icon: 'i-ri:accessibility-fill',
          alwaysShow: true
        },
        children: [
          {
            path: '/waybill/overseasWarehouseReceipt/create',
            component: 'views/Waybill/OverseasWarehouseReceipt/Create/index',
            name: 'WaybillOverseasWarehouseReceiptCreate',
            meta: {
              title: '创建运单',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/order',
    component: '#',
    name: 'order',
    redirect: '/order/stockIn',
    meta: {
      title: '订单管理',
      icon: 'i-ri:accessibility-fill',
      alwaysShow: true
    },
    children: [
      {
        path: '/order/stockIn',
        component: 'views/Order/StockIn/index',
        name: 'orderStockIn',
        meta: {
          title: '备货入库订单',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/order/outboundOrder',
        component: 'views/Order/OutboundOrder/index',
        name: 'orderOutboundOrder',
        meta: {
          title: '出库订单列表',
          icon: 'i-ri:accessibility-fill',
          alwaysShow: true
        },
        children: [
          {
            path: '/order/outboundOrder/detail',
            component: 'views/Order/OutboundOrder/Detail/index',
            name: 'orderOutboundOrderDetail',
            meta: {
              title: '订单详情',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/order/returnedItem',
        component: 'views/Order/ReturnedItem/index',
        name: 'orderReturnedItem',
        meta: {
          title: '退件列表',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/order/returnedItem/detail',
            component: 'views/Order/ReturnedItem/Detail/index',
            name: 'orderReturnedItemDetail',
            meta: {
              title: '订单详情',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/inventory',
    component: '#',
    name: 'inventory',
    redirect: '/inventory/warehouseProxyConfiguration',
    meta: {
      title: '库存管理',
      alwaysShow: true,
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/inventory/warehouseProxyConfiguration',
        component: 'views/Inventory/WarehouseProxyConfiguration/index',
        name: 'inventoryWarehouseProxyConfiguration',
        meta: {
          title: '仓库库存总览',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/inventory/warehouseInventoryFlow',
        component: 'views/Inventory/WarehouseInventoryFlow/index',
        name: 'inventoryWarehouseInventoryFlow',
        meta: {
          title: '仓库库存流水',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/inventory/warehouseInventoryAge',
        component: 'views/Inventory/WarehouseInventoryAge/index',
        name: 'inventoryWarehouseInventoryAge',
        meta: {
          title: '仓库库存库龄',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  },
  {
    path: '/serviceBasicSettings',
    component: '#',
    name: 'serviceBasicSettings',
    redirect: '/serviceBasicSettings/warehouseProxyConfiguration',
    meta: {
      title: '服务基础设置',
      alwaysShow: true,
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/serviceBasicSettings/warehouseProxyConfiguration',
        component:
          'views/ServiceBasicSettings/WarehouseProxyConfiguration/index',
        name: 'serviceBasicSettingsWarehouseProxyConfiguration',
        meta: {
          title: '仓库代理配置',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  },
  {
    path: '/logisticsServiceConfig',
    component: '#',
    name: 'logisticsServiceConfig',
    redirect: '/logisticsServiceConfig/initialServiceSettings',
    meta: {
      title: '头程服务设置',
      icon: 'i-ri:accessibility-fill',
      alwaysShow: true
    },
    children: [
      {
        path: '/logisticsServiceConfig/initialServiceSettings',
        component: 'views/LogisticsServiceConfig/InitialServiceSettings/index',
        name: 'initialServiceSettings',
        meta: {
          title: '头程服务设置',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  },
  {
    path: '/OWSSettings',
    component: '#',
    name: 'OWSSettings',
    redirect: '/OWSSettings/LSA',
    meta: {
      title: '海外仓服务设置',
      alwaysShow: true,
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/OWSSettings/LSA',
        component: 'views/OWSSettings/LSA/index',
        name: 'OWSSettingsLSA',
        meta: {
          title: '物流商授权管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/OWSSettings/SystemChannel',
        component: 'views/OWSSettings/SystemChannel/index',
        name: 'OWSSettingsSystemChannel',
        meta: {
          title: '系统渠道列表',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/OWSSettings/CustomerChannel',
        component: 'views/OWSSettings/CustomerChannel/index',
        name: 'OWSSettingsCustomerChannel',
        meta: {
          title: '客户渠道列表',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/OWSSettings/ChannelEntity',
        component: 'views/OWSSettings/ChannelEntity/index',
        name: 'OWSSettingsChannelEntity',
        meta: {
          title: '渠道',
          hidden: true
        }
      },
      {
        path: '/OWSSettings/FuelPlan',
        component: 'views/OWSSettings/FuelPlan/index',
        name: 'OWSSettingsFuelPlan',
        meta: {
          title: '燃油规则',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/OWSSettings/zoneTemp',
        component: 'views/OWSSettings/ZoneTemp/index',
        name: 'OWSSettingsZoneTemp',
        meta: {
          title: '分区规则',
          icon: 'i-ri:accessibility-fill',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/zoneTemp/create',
            component: 'views/OWSSettings/ZoneTemp/Create/index',
            name: 'OWSSettingsZoneTempCreate',
            meta: {
              title: '创建分区规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/zoneTemp/edit',
            component: 'views/OWSSettings/ZoneTemp/Edit/index',
            name: 'OWSSettingsZoneTempEdit',
            meta: {
              title: '编辑分区规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/zoneTemp/testing',
            component: 'views/OWSSettings/ZoneTemp/Testing/index',
            name: 'OWSSettingsZoneTempTesting',
            meta: {
              title: '测试分区规则',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/surcharge',
        component: 'views/OWSSettings/Surcharge/index',
        name: 'OWSSettingsSurcharge',
        meta: {
          title: '附加费规则',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/OWSSettings/surcharge/overlengthCreate',
            component: 'views/OWSSettings/Surcharge/Overlength/Create/index',
            name: 'OWSSettingsSurchargeOverlengthCreate',
            meta: {
              title: '新建超长/超重规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/overlengthEdit',
            component: 'views/OWSSettings/Surcharge/Overlength/Edit/index',
            name: 'OWSSettingsSurchargeOverlengthEdit',
            meta: {
              title: '编辑超长/超重规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/farawayCreate',
            component: 'views/OWSSettings/Surcharge/Faraway/Create/index',
            name: 'OWSSettingsSurchargeFarawayCreate',
            meta: {
              title: '创建偏远明细',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/farawayDetail',
            component: 'views/OWSSettings/Surcharge/Faraway/Detail/index',
            name: 'OWSSettingsSurchargeFarawayDetail',
            meta: {
              title: '偏远明细详情',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/freightTemp',
        component: 'views/OWSSettings/FreightTemp/index',
        name: 'OWSSettingsFreightTemp',
        meta: {
          title: '物流费模板',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/OWSSettings/freightTemp/create',
            component: 'views/OWSSettings/FreightTemp/Create/index',
            name: 'OWSSettingsFreightTempCreate',
            meta: {
              title: '新建物流费模板',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/channelPrice',
        component: 'views/OWSSettings/ChannelPrice/index',
        name: 'OWSSettingsChannelPrice',
        meta: {
          title: '物流费设置',
          icon: 'i-ri:accessibility-fill'
          // alwaysShow: true
        }
      },
      {
        path: '/OWSSettings/operateBill',
        component: 'views/OWSSettings/OperateBill/index',
        name: 'OWSSettingsOperateBill',
        meta: {
          title: '操作费设置',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/OWSSettings/operateBill/create',
            component: 'views/OWSSettings/OperateBill/Create/index',
            name: 'OWSSettingsOperateBillCreate',
            meta: {
              title: '新建计费项',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/warehouseRent',
        component: 'views/OWSSettings/WarehouseRent/index',
        name: 'OWSSettingsWarehouseRent',
        meta: {
          title: '仓库费设置',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/OWSSettings/warehouseRent/create',
            component: 'views/OWSSettings/WarehouseRent/Create/index',
            name: 'OWSSettingsWarehouseRentCreate',
            meta: {
              title: '新建仓库费设置',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/quotationScheme',
        component: 'views/OWSSettings/QuotationScheme/index',
        name: 'OWSSettingsQuotationScheme',
        meta: {
          title: '总报价方案',
          alwaysShow: true,
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/OWSSettings/QuotationScheme/create',
            component: 'views/OWSSettings/QuotationScheme/Create/index',
            name: 'OWSSettingsQuotationSchemeCreate',
            meta: {
              title: '新建报价方案',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/finance',
    component: '#',
    name: 'finance',
    redirect: '/finance/bankAccount',
    meta: {
      title: '财务管理',
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/finance/bankAccount',
        component: 'views/Finance/BankAccount/index',
        name: 'financeBankAccount',
        meta: {
          title: '银行账号管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/finance/currency',
        component: 'views/Finance/Currency/index',
        name: 'financeCurrency',
        meta: {
          title: '币种管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/finance/receivingAccount',
        component: 'views/Finance/ReceivingAccount/index',
        name: 'financeReceivingAccount',
        meta: {
          title: '收款账户管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/finance/accountRecharge',
        component: 'views/Finance/AccountRecharge/index',
        name: 'financeAccountRecharge',
        meta: {
          title: '账号充值管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/finance/receivable',
        component: '##',
        name: 'financeReceivable',
        redirect: '/finance/receivable/settlementDoc',
        meta: {
          title: '应收管理',
          icon: 'i-ri:accessibility-fill'
        },
        children: [
          {
            path: '/finance/receivable/settlementDoc',
            component: 'views/Finance/Receivable/SettlementDoc/index',
            name: 'financeReceivableSettlementDoc',
            meta: {
              title: '结算单管理',
              icon: 'i-ri:accessibility-fill'
            }
          },
          {
            path: '/finance/receivable/customerBilling',
            component: 'views/Finance/Receivable/CustomerBilling/index',
            name: 'financeReceivableCustomerBilling',
            meta: {
              title: '客户账单',
              icon: 'i-ri:accessibility-fill'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/toolCenter',
    component: '#',
    name: 'toolCenter',
    redirect: '/toolCenter/freightTrialCalculation',
    meta: {
      title: '工具中心',
      alwaysShow: true,
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/toolCenter/freightTrialCalculation',
        component: 'views/ToolCenter/FreightTrialCalculation/index',
        name: 'toolCenterFreightTrialCalculation',
        meta: {
          title: '运费试算',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  },
  {
    path: '/product',
    component: '#',
    name: 'product',
    redirect: '/product/category',
    meta: {
      title: '产品管理',
      icon: 'i-ri:accessibility-fill',
      alwaysShow: true
    },
    children: [
      {
        path: '/product/category',
        component: 'views/Product/Category/index',
        name: 'productCategory',
        meta: {
          title: '产品品类',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/product/list',
        component: 'views/Product/List/index',
        name: 'productList',
        meta: {
          title: '产品列表',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/product/attribute',
        component: 'views/Product/Attribute/index',
        name: 'productAttribute',
        meta: {
          title: '属性管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/product/inventory-review',
        component: 'views/Product/InventoryReview/index',
        name: 'productInventoryReview',
        meta: {
          title: '产品备货审核',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/product/create',
        component: 'views/Product/Goods/Create',
        name: 'productGoods',
        meta: {
          title: '新增商品',
          hidden: true
        }
      },
      {
        path: '/product/edit',
        component: 'views/Product/Goods/Edit',
        name: 'productEdit',
        meta: {
          title: '编辑商品',
          hidden: true
        }
      },
      {
        path: '/product/detail',
        component: 'views/Product/Goods/Detail',
        name: 'productDetail',
        meta: {
          title: '商品详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/system',
    component: '#',
    name: 'system',
    redirect: '/system/user',
    meta: {
      title: '系统管理',
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/system/user',
        component: 'views/System/User/index',
        name: 'user',
        meta: {
          title: '用户列表',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/system/role',
        component: 'views/System/Role/index',
        name: 'role',
        meta: {
          title: '角色列表',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/system/institution',
        component: 'views/System/Institution/index',
        name: 'institution',
        meta: {
          title: '机构列表',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  },

  {
    path: '/baseinfo',
    component: '#',
    name: 'baseinfo',
    redirect: '/baseinfo/location',
    meta: {
      title: '基础信息',
      icon: 'i-ri:accessibility-fill'
    },
    children: [
      {
        path: '/baseinfo/location',
        component: 'views/BaseInfo/Location/index',
        name: 'baseinfoLocation',
        meta: {
          title: '地点管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/baseinfo/facilitator',
        component: 'views/BaseInfo/Facilitator/index',
        name: 'baseinfoFacilitator',
        meta: {
          title: '服务商管理',
          icon: 'i-ri:accessibility-fill'
        }
      },
      {
        path: '/baseinfo/warehouse',
        component: 'views/BaseInfo/Warehouse/index',
        name: 'baseinfoWarehouse',
        meta: {
          title: '仓库管理',
          icon: 'i-ri:accessibility-fill'
        }
      }
    ]
  }
]
