import type { App } from 'vue'
import {
  createRouter,
  createWebHashHistory,
  type RouteRecordRaw
} from 'vue-router'
import Layout from '@/layout/index.vue'
import { NO_RESET_WHITE_LIST } from '@/constants'
import { getParentLayout } from '@/utils/routerHelper'

export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/customer/stockist',
    name: 'Root',
    meta: {
      hidden: true
    }
  },
  // {
  //   path: '/test1',
  //   component: () => import('../views/Test1/index.vue'),
  //   // redirect: '/dashboard/analysis',
  //   name: 'test1',
  //   meta: {
  //     hidden: true
  //   }
  // },
  {
    path: '/login',
    component: () => import('@/views/Login/index.vue'),
    name: 'login',
    meta: {
      hidden: true,
      title: '登录页'
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: '403',
    meta: {
      title: '403',
      hidden: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: '500',
    meta: {
      title: '500',
      hidden: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404'
    }
  }
]

export const asyncRouterMap: AppRouteRecordRaw[] = [
  // {
  //   path: '/dashboard',
  //   component: Layout,
  //   name: 'dashboard',
  //   redirect: '/dashboard/analysis',
  //   meta: {},
  //   children: [
  //     {
  //       path: '/dashboard/analysis',
  //       component: () => import('@/views/Dashboard/index.vue'),
  //       name: 'dashboardAnalysis',
  //       meta: {}
  //     }
  //   ]
  // },

  {
    path: '/customer',
    component: Layout,
    name: 'customer',
    redirect: '/customer/fundAccount',
    meta: {
      title: '客户管理'
    },
    children: [
      {
        path: '/customer/stockist',
        component: () => import('@/views/Customer/Stockist/index.vue'),
        name: 'customerStockist',
        meta: {
          title: '卖家管理'
        }
      },
      {
        path: '/customer/fundAccount',
        component: () => import('@/views/Customer/FundAccount/index.vue'),
        name: 'customerFundAccount',
        meta: {
          title: '资金账户管理'
        }
      },
      {
        path: '/customer/rechargeReview',
        component: () => import('@/views/Customer/RechargeReview/index.vue'),
        name: 'customerRechargeReview',
        meta: {
          title: '充值管理审核'
        }
      }
      // {
      //   path: '/customer/businessSetting',
      //   component: () => import('@/views/Customer/BusinessSetting/index.vue'),
      //   name: 'businessSetting',
      //   meta: {
      //     title: '业务设置'
      //   }
      // },
      // {
      //   path: '/customer/account',
      //   component: () => import('@/views/Customer/Account/index.vue'),
      //   name: 'account',
      //   meta: {
      //     title: '账户管理'
      //   }
      // }
    ]
  },
  {
    path: '/waybill',
    component: Layout,
    name: 'waybill',
    redirect: '/waybill/overseasWarehouseReceipt',
    meta: {
      title: '运单管理',
      alwaysShow: true
    },
    children: [
      {
        path: '/waybill/firstLeg',
        component: () => import('@/views/Waybill/FirstLeg/index.vue'),
        name: 'waybillFirstLeg',
        meta: {
          title: '头程运单管理',
          alwaysShow: true
        },
        children: [
          {
            path: '/waybill/firstLeg/create',
            component: () =>
              import('@/views/Waybill/FirstLeg/Create/index.vue'),
            name: 'waybillFirstLegCreate',
            meta: {
              title: '创建头程运单',
              hidden: true
            }
          },
          {
            path: '/waybill/firstLeg/edit',
            component: () => import('@/views/Waybill/FirstLeg/Edit/index.vue'),
            name: 'waybillFirstLegCreate',
            meta: {
              title: '编辑头程运单',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/waybill/overseasWarehouseReceipt',
        component: () =>
          import('@/views/Waybill/OverseasWarehouseReceipt/index.vue'),
        name: 'WaybillOverseasWarehouseReceipt',
        meta: {
          title: '海外仓入库单管理'
        },
        children: [
          {
            path: '/waybill/overseasWarehouseReceipt/create',
            component: () =>
              import(
                '@/views/Waybill/OverseasWarehouseReceipt/Create/index.vue'
              ),
            name: 'orderFirstLegCreateCreate',
            meta: {
              title: '创建运单',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    name: 'order',
    redirect: '/order/stockIn',
    meta: {
      title: '订单管理'
    },
    children: [
      {
        path: '/order/stockIn',
        component: () => import('@/views/Order/StockIn/index.vue'),
        name: 'orderStockIn',
        meta: {
          title: '备货入库订单'
        }
      },
      {
        path: '/order/outboundOrder',
        component: () => import('@/views/Order/OutboundOrder/index.vue'),
        name: 'orderOutboundOrder',
        meta: {
          title: '出库订单列表',
          alwaysShow: true
        },
        children: [
          {
            path: '/order/outboundOrder/detail',
            component: () =>
              import('@/views/Order/OutboundOrder/Detail/index.vue'),
            name: 'orderOutboundOrderDetail',
            meta: {
              title: '订单详情',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/order/returnedItem',
        component: () => import('@/views/Order/ReturnedItem/index.vue'),
        name: 'orderReturnedItem',
        meta: {
          title: '退件列表',
          alwaysShow: true
        },
        children: [
          {
            path: '/order/returnedItem/detail',
            component: () =>
              import('@/views/Order/ReturnedItem/Detail/index.vue'),
            name: 'orderReturnedItemDetail',
            meta: {
              title: '订单详情',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/inventory',
    component: Layout,
    name: 'inventory',
    redirect: '/inventory/warehouseProxyConfiguration',
    meta: {
      title: '库存管理',
      alwaysShow: true
    },
    children: [
      {
        path: '/inventory/warehouseProxyConfiguration',
        component: () =>
          import('@/views/Inventory/WarehouseProxyConfiguration/index.vue'),
        name: 'inventoryWarehouseProxyConfiguration',
        meta: {
          title: '仓库库存总览'
        }
      },
      {
        path: '/inventory/warehouseInventoryFlow',
        component: () =>
          import('@/views/Inventory/WarehouseInventoryFlow/index.vue'),
        name: 'inventoryWarehouseInventoryFlow',
        meta: {
          title: '仓库库存流水'
        }
      },
      {
        path: '/inventory/warehouseInventoryAge',
        component: () =>
          import('@/views/Inventory/WarehouseInventoryAge/index.vue'),
        name: 'inventoryWarehouseInventoryAge',
        meta: {
          title: '仓库库存库龄'
        }
      }
    ]
  },
  {
    path: '/serviceBasicSettings',
    component: Layout,
    name: 'serviceBasicSettings',
    redirect: '/serviceBasicSettings/warehouseProxyConfiguration',
    meta: {
      title: '服务基础设置',
      alwaysShow: true
    },
    children: [
      {
        path: '/serviceBasicSettings/warehouseProxyConfiguration',
        component: () =>
          import(
            '@/views/ServiceBasicSettings/WarehouseProxyConfiguration/index.vue'
          ),
        name: 'serviceBasicSettingsWarehouseProxyConfiguration',
        meta: {
          title: '仓库代理配置'
        }
      }
    ]
  },
  {
    path: '/logisticsServiceConfig',
    component: Layout,
    name: 'logisticsServiceConfig',
    redirect: '/logisticsServiceConfig/initialServiceSettings',
    meta: {
      title: '头程服务设置',
      alwaysShow: true
    },
    children: [
      {
        path: '/logisticsServiceConfig/initialServiceSettings',
        component: () =>
          import(
            '@/views/LogisticsServiceConfig/InitialServiceSettings/index.vue'
          ),
        name: 'initialServiceSettings',
        meta: {
          title: '头程服务设置'
        }
      }
    ]
  },
  {
    path: '/OWSSettings',
    component: Layout,
    name: 'OWSSettings',
    redirect: '/OWSSettings/LSA',
    meta: {
      title: '海外仓服务设置',
      alwaysShow: true
    },
    children: [
      {
        path: '/OWSSettings/LSA',
        component: () => import('@/views/OWSSettings/LSA/index.vue'),
        name: 'OWSSettingsLSA',
        meta: {
          title: '物流商授权管理'
        }
      },
      {
        path: '/OWSSettings/systemChannel',
        component: () => import('@/views/OWSSettings/SystemChannel/index.vue'),
        name: 'OWSSettingsSystemChannel',
        meta: {
          title: '系统渠道列表'
        }
      },
      {
        path: '/OWSSettings/customerChannel',
        component: () =>
          import('@/views/OWSSettings/CustomerChannel/index.vue'),
        name: 'OWSSettingsCustomerChannel',
        meta: {
          title: '客户渠道列表'
        }
      },
      {
        path: '/OWSSettings/channelEntity',
        component: () => import('@/views/OWSSettings/ChannelEntity/index.vue'),
        name: 'OWSSettingsChannelEntity',
        meta: {
          title: '渠道'
        }
      },
      {
        path: '/OWSSettings/fuelPlan',
        component: () => import('@/views/OWSSettings/FuelPlan/index.vue'),
        name: 'OWSSettingsFuelPlan',
        meta: {
          title: '燃油规则'
        }
      },
      {
        path: '/OWSSettings/zoneTemp',
        component: () => import('@/views/OWSSettings/ZoneTemp/index.vue'),
        name: 'OWSSettingsZoneTemp',
        meta: {
          title: '分区规则',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/zoneTemp/create',
            component: () =>
              import('@/views/OWSSettings/ZoneTemp/Create/index.vue'),
            name: 'OWSSettingsZoneTempCreate',
            meta: {
              title: '创建分区规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/zoneTemp/edit',
            component: () =>
              import('@/views/OWSSettings/ZoneTemp/Edit/index.vue'),
            name: 'OWSSettingsZoneTempEdit',
            meta: {
              title: '编辑分区规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/zoneTemp/testing',
            component: () =>
              import('@/views/OWSSettings/ZoneTemp/Testing/index.vue'),
            name: 'OWSSettingsZoneTempTesting',
            meta: {
              title: '测试分区规则',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/surcharge',
        component: () => import('@/views/OWSSettings/Surcharge/index.vue'),
        name: 'OWSSettingsSurcharge',
        meta: {
          title: '附加费规则',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/surcharge/overlengthCreate',
            component: () =>
              import(
                '@/views/OWSSettings/Surcharge/Overlength/Create/index.vue'
              ),
            name: 'OWSSettingsSurchargeOverlengthCreate',
            meta: {
              title: '新建超长/超重规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/overlengthEdit',
            component: () =>
              import('@/views/OWSSettings/Surcharge/Overlength/Edit/index.vue'),
            name: 'OWSSettingsSurchargeOverlengthEdit',
            meta: {
              title: '编辑超长/超重规则',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/farawayCreate',
            component: () =>
              import('@/views/OWSSettings/Surcharge/Faraway/Create/index.vue'),
            name: 'OWSSettingsSurchargeFarawayCreate',
            meta: {
              title: '创建偏远明细',
              hidden: true
            }
          },
          {
            path: '/OWSSettings/surcharge/farawayDetail',
            component: () =>
              import('@/views/OWSSettings/Surcharge/Faraway/Detail/index.vue'),
            name: 'OWSSettingsSurchargeFarawayDetail',
            meta: {
              title: '偏远明细详情',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/freightTemp',
        component: () => import('@/views/OWSSettings/FreightTemp/index.vue'),
        name: 'OWSSettingsFreightTemp',
        meta: {
          title: '物流费模板',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/freightTemp/create',
            component: () =>
              import('@/views/OWSSettings/FreightTemp/Create/index.vue'),
            name: 'OWSSettingsFreightTempCreate',
            meta: {
              title: '新建物流费模板',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/channelPrice',
        component: () => import('@/views/OWSSettings/ChannelPrice/index.vue'),
        name: 'OWSSettingsChannelPrice',
        meta: {
          title: '物流费设置',
          alwaysShow: true
        }
      },
      {
        path: '/OWSSettings/operateBill',
        component: () => import('@/views/OWSSettings/OperateBill/index.vue'),
        name: 'OWSSettingsOperateBill',
        meta: {
          title: '操作费设置',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/operateBill/create',
            component: () =>
              import('@/views/OWSSettings/OperateBill/Create/index.vue'),
            name: 'OWSSettingsOperateBillCreate',
            meta: {
              title: '新建计费项',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/warehouseRent',
        component: () => import('@/views/OWSSettings/WarehouseRent/index.vue'),
        name: 'OWSSettingsWarehouseRent',
        meta: {
          title: '仓库费设置',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/warehouseRent/create',
            component: () =>
              import('@/views/OWSSettings/WarehouseRent/Create/index.vue'),
            name: 'OWSSettingsWarehouseRentCreate',
            meta: {
              title: '新建仓库费设置',
              hidden: true
            }
          }
        ]
      },
      {
        path: '/OWSSettings/quotationScheme',
        component: () =>
          import('@/views/OWSSettings/QuotationScheme/index.vue'),
        name: 'OWSSettingsQuotationScheme',
        meta: {
          title: '总报价方案',
          alwaysShow: true
        },
        children: [
          {
            path: '/OWSSettings/QuotationScheme/create',
            component: () =>
              import('@/views/OWSSettings/QuotationScheme/Create/index.vue'),
            name: 'OWSSettingsQuotationSchemeCreate',
            meta: {
              title: '新建报价方案',
              hidden: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    name: 'finance',
    redirect: '/finance/bankAccount',
    meta: {
      title: '财务管理'
    },
    children: [
      {
        path: '/finance/bankAccount',
        component: () => import('@/views/Finance/BankAccount/index.vue'),
        name: 'financeBankAccount',
        meta: {
          title: '银行账号管理'
        }
      },
      {
        path: '/finance/currency',
        component: () => import('@/views/Finance/Currency/index.vue'),
        name: 'financeCurrency',
        meta: {
          title: '币种管理'
        }
      },
      {
        path: '/finance/receivingAccount',
        component: () => import('@/views/Finance/ReceivingAccount/index.vue'),
        name: 'financeReceivingAccount',
        meta: {
          title: '收款账户管理'
        }
      },
      {
        path: '/finance/accountRecharge',
        component: () => import('@/views/Finance/AccountRecharge/index.vue'),
        name: 'financeAccountRecharge',
        meta: {
          title: '账号充值管理'
        }
      },
      {
        path: '/finance/receivable',
        component: getParentLayout(),
        name: 'financeReceivable',
        redirect: '/finance/receivable/settlementDoc',
        meta: {
          title: '应收管理'
        },
        children: [
          {
            path: '/finance/receivable/settlementDoc',
            component: () =>
              import('@/views/Finance/Receivable/SettlementDoc/index.vue'),
            name: 'financeReceivableSettlementDoc',
            meta: {
              title: '结算单管理'
            }
          },
          {
            path: '/finance/receivable/customerBilling',
            component: () =>
              import('@/views/Finance/Receivable/CustomerBilling/index.vue'),
            name: 'financeReceivableCustomerBilling',
            meta: {
              title: '客户账单'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/toolCenter',
    component: Layout,
    name: 'toolCenter',
    redirect: '/toolCenter/freightTrialCalculation',
    meta: {
      title: '工具中心',
      alwaysShow: true
    },
    children: [
      {
        path: '/toolCenter/freightTrialCalculation',
        component: () =>
          import('@/views/ToolCenter/FreightTrialCalculation/index.vue'),
        name: 'toolCenterFreightTrialCalculation',
        meta: {
          title: '运费试算'
        }
      }
    ]
  },
  {
    path: '/product',
    component: Layout,
    name: 'product',
    redirect: '/product/category',
    meta: {
      title: '产品管理'
    },
    children: [
      {
        path: '/product/category',
        component: () => import('@/views/Product/Category/index.vue'),
        name: 'productCategory',
        meta: {
          title: '产品品类'
        }
      },
      {
        path: '/product/list',
        component: () => import('@/views/Product/List/index.vue'),
        name: 'productList',
        meta: {
          title: '产品列表'
        }
      },
      {
        path: '/product/attribute',
        component: () => import('@/views/Product/Attribute/index.vue'),
        name: 'productAttribute',
        meta: {
          title: '属性管理'
        }
      },
      {
        path: '/product/inventory-review',
        component: () => import('@/views/Product/InventoryReview/index.vue'),
        name: 'productInventoryReview',
        meta: {
          title: '产品备货审核'
        }
      },
      {
        path: '/product/create',
        component: () => import('@/views/Product/Goods/Create.vue'),
        name: 'productGoods',
        meta: {
          title: '新增商品'
        }
      },
      {
        path: '/product/edit',
        component: () => import('@/views/Product/Goods/Edit.vue'),
        name: 'productEdit',
        meta: {
          title: '编辑商品'
        }
      },
      {
        path: '/product/detail',
        component: () => import('@/views/Product/Goods/Detail.vue'),
        name: 'productDetail',
        meta: {
          title: '商品详情'
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'system',
    redirect: '/system/user',
    meta: {
      title: '系统管理'
    },
    children: [
      {
        path: '/system/user',
        component: () => import('@/views/System/User/index.vue'),
        name: 'user',
        meta: {
          title: '用户列表'
        }
      },
      {
        path: '/system/role',
        component: () => import('@/views/System/Role/index.vue'),
        name: 'role',
        meta: {
          title: '角色列表'
        }
      },
      {
        path: '/system/institution',
        component: () => import('@/views/System/Institution/index.vue'),
        name: 'institution',
        meta: {
          title: '机构列表'
        }
      }
    ]
  },

  {
    path: '/baseinfo',
    component: Layout,
    name: 'baseinfo',
    redirect: '/baseinfo/location',
    meta: {
      title: '基础信息'
    },
    children: [
      {
        path: '/baseinfo/location',
        component: () => import('@/views/BaseInfo/Location/index.vue'),
        name: 'baseinfoLocation',
        meta: {
          title: '地点管理'
        }
      },
      {
        path: '/baseinfo/facilitator',
        component: () => import('@/views/BaseInfo/Facilitator/index.vue'),
        name: 'baseinfoFacilitator',
        meta: {
          title: '服务商管理'
        }
      },
      {
        path: '/baseinfo/warehouse',
        component: () => import('@/views/BaseInfo/Warehouse/index.vue'),
        name: 'baseinfoWarehouse',
        meta: {
          title: '仓库管理'
        }
      }
    ]
  }
]

// const routes: AppRouteRecordRaw[] = [...constantRouterMap]
// const routes: AppRouteRecordRaw[] = [...constantRouterMap, ...asyncRouterMap]

const router = createRouter({
  history: createWebHashHistory(),
  strict: true,
  routes: constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  router.getRoutes().forEach(route => {
    const { name } = route
    if (name && !NO_RESET_WHITE_LIST.includes(name as string)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router
