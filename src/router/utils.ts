import type { RouteRecordRaw } from 'vue-router'

/** 通过指定 `key` 获取父级路径集合，默认 `key` 为 `path` */
export function getParentPaths(value: string, routes: RouteRecordRaw[], key = 'path') {
  // 深度遍历查找
  function dfs(routes: RouteRecordRaw[], value: string, parents: string[]) {
    for (let i = 0; i < routes.length; i++) {
      const item: any = routes[i]
      // 返回父级path
      if (item[key] === value) return parents
      // children不存在或为空则不递归
      if (!item.children || !item.children.length) continue
      // 往下查找时将当前path入栈
      parents.push(item.path)

      if (dfs(item.children, value, parents).length) return parents
      // 深度遍历查找未找到时当前path 出栈
      parents.pop()
    }
    // 未找到时返回空数组
    return []
  }

  return dfs(routes, value, [])
}

/** 查找对应 `path` 的路由信息 */
export function findRouteByPath(path: string, routes: RouteRecordRaw[]): any {
  let res = routes.find((item: { path: string }) => item.path == path)
  if (res) {
    return isProxy(res) ? toRaw(res) : res
  } else {
    for (const route of routes) {
      if (Array.isArray(route.children) && route.children.length > 0) {
        res = findRouteByPath(path, route.children)
        if (res) {
          return isProxy(res) ? toRaw(res) : res
        }
      }
    }
    // for (let i = 0; i < routes.length; i++) {
    //   if (Array.isArray(routes[i].children) && routes[i].children.length > 0) {
    //     res = findRouteByPath(path, routes[i].children)
    //     if (res) {
    //       return isProxy(res) ? toRaw(res) : res
    //     }
    //   }
    // }
    return null
  }
}
