# 调整运单状态逻辑

## 相关文件

- 主页面: src/views/Waybill/FirstLeg/index.vue
- 状态提示弹窗: src/views/Waybill/FirstLeg/components/WaybillStatus.vue

## 状态值

```
[
  {
    label: '已下单',
    value: 'Ordered'
  },
  {
    label: '已收货',
    value: 'Received'
  },
  {
    label: '转运中',
    value: 'InTransit'
  },
  {
    label: '已签收',
    value: 'Signed'
  },
  {
    label: '退件',
    value: 'ReturnStep'
  },
  {
    label: '申请取消',
    value: 'ApplyCancel'
  },
  {
    label: '已取消',
    value: 'Cancelled'
  },
  {
    label: '待付款',
    value: 'Obligation'
  },
  {
    label: '全部',
    value: ''
  }
]
```

## 逻辑

1. 点击取消运单按钮，传入type = 4 打开弹窗, 抛出事件 调用更新状态接口
2. 点击转运中按钮
   1. 使用ElMessageBox.confirm。 状态success,提示语：确认要变更状态吗?
   2. 点击confirm确认按钮校验当前选中的订单即selection中是否存在：“退件”或“已取消”状态。如果存在则 打开WaybillStatus弹窗
   3. 打开WaybillStatus弹窗。type = 2. 根据传入到WaybillStatus组件中的selection，更新订单数量。
   4. 输入“确认调整”并校验通过以后。 抛出事件 调用更新状态接口。如果“确认调整”校验未通过使用ElMessage提示校验未通过。
   5. 如果不存在“退件”或“已取消”状态。 直接调用更新状态接口
3. 点击已签收、退件按钮。逻辑同转运中按钮

# 拣货弹窗计算材积重收费重接口

1. 点击填充或者当前这一行长宽高重量有变化的时候去调pickingGoods拣货接口然后获取到计算后的材积重，收费重并更新到tableData中。
2. 点击弹窗确认按钮调用savePickingGoodsData接口。保存的时候需要做下数据映射。

# 更新产品数与箱数计算逻辑

## 相关文件

- src/views/Waybill/FirstLeg/components/SkuTable.vue

1. 产品数计算逻辑：使用tableData中的 箱数 乘 单箱总数量 相加
2. 箱数计算逻辑： 使用tableData中的箱数相加
