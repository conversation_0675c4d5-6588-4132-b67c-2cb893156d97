{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://127.0.0.1:9001", "description": "Generated server url"}], "paths": {"/upload": {"post": {"tags": ["文件上传"], "operationId": "upload", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/tools/config/pbe/e": {"post": {"tags": ["获取加密串工具"], "summary": "PBE加密", "operationId": "encrypt", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/tools/config/pbe/d": {"post": {"tags": ["获取加密串工具"], "summary": "PBE加密", "operationId": "decrypt", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/updateUser": {"post": {"tags": ["用户管理"], "summary": "更新系统用户", "operationId": "updateUser", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/saveSysUser": {"post": {"tags": ["用户管理"], "summary": "新增系统用户", "operationId": "saveSysUser", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/resetPassword": {"post": {"tags": ["用户管理"], "summary": "重置密码", "operationId": "resetPassword", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/getUserList": {"post": {"tags": ["用户管理"], "summary": "分页获取系统用户列表", "operationId": "getUserList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/getUserById": {"post": {"tags": ["用户管理"], "summary": "根据id获取系统用户", "operationId": "getUserById", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/enableUser": {"post": {"tags": ["用户管理"], "summary": "启用系统用户", "operationId": "enableUser", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/disableUser": {"post": {"tags": ["用户管理"], "summary": "禁用系统用户", "operationId": "disableUser", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sysUser/deleteUser": {"post": {"tags": ["用户管理"], "summary": "删除系统用户", "operationId": "deleteUser", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/updateSellerInfo": {"post": {"tags": ["卖家管理"], "summary": "更新卖家信息", "operationId": "updateSellerInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/sellerRecharge": {"post": {"tags": ["卖家管理"], "summary": "卖家充值", "operationId": "sellerRecharge", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeRecordModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/saveSellerInfo": {"post": {"tags": ["卖家管理"], "summary": "新增卖家信息", "operationId": "saveSellerInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/findSellerList": {"post": {"tags": ["卖家管理"], "summary": "分页获取卖家信息列表", "operationId": "findSellerList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/distributionWarehouse": {"post": {"tags": ["卖家管理"], "summary": "分配仓库", "operationId": "distributionWarehouse", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/changeSellerStatus": {"post": {"tags": ["卖家管理"], "summary": "通过、驳回审核、冻结、启用", "operationId": "changeSellerStatus", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerBankAccount/updateSellerBankAccountInfo": {"post": {"tags": ["资金账户管理"], "summary": "更增商家账户信息", "operationId": "updateSellerBankAccountInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerBankAccount/saveSellerBankAccountInfo": {"post": {"tags": ["资金账户管理"], "summary": "新增商家账户信息", "operationId": "saveSellerBankAccountInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerBankAccount/findSellerBankAccountList": {"post": {"tags": ["资金账户管理"], "summary": "分页获取商家账户列表", "operationId": "findSellerBankAccountList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerBankAccount/changeStatus": {"post": {"tags": ["资金账户管理"], "summary": "修改状态", "operationId": "changeStatus", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/updateRoleInfo": {"post": {"tags": ["角色管理"], "summary": "更新角色", "operationId": "updateRoleInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/saveRoleInfo": {"post": {"tags": ["角色管理"], "summary": "新增角色", "operationId": "saveRoleInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/saveOrUpdateRoleMenu": {"post": {"tags": ["角色管理"], "summary": "保存或更新角色菜单配置", "operationId": "saveOrUpdateRoleMenu", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleMenuModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/getRoleList": {"post": {"tags": ["角色管理"], "summary": "分页获取角色列表", "operationId": "getRoleList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/receivingAccount/updateReceivingAccount": {"post": {"tags": ["财务管理-收款账户管理"], "summary": "更新收款账户", "operationId": "updateReceivingAccount", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/receivingAccount/saveReceivingAccount": {"post": {"tags": ["财务管理-收款账户管理"], "summary": "保存收款账户", "operationId": "saveReceivingAccount", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/receivingAccount/getReceivingAccountList": {"post": {"tags": ["财务管理-收款账户管理"], "summary": "获取收款账户列表", "operationId": "getReceivingAccountList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultIPageReceivingAccountVo"}}}}}}}, "/receivingAccount/deleteReceivingAccount": {"post": {"tags": ["财务管理-收款账户管理"], "summary": "删除收款账户", "operationId": "deleteReceivingAccount", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productSpecificationsBasic/update": {"post": {"tags": ["产品列表-产品规格信息"], "summary": "修改", "operationId": "update", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecificationsBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productSpecificationsBasic/save": {"post": {"tags": ["产品列表-产品规格信息"], "summary": "新增", "operationId": "insert", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecificationsBasicSaveModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productSpecificationsBasic/getList": {"post": {"tags": ["产品列表-产品规格信息"], "summary": "分页获取产品规格信息列表", "operationId": "selectAll", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecificationsBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productReleaseType/update": {"post": {"tags": ["产品列表-发布类型"], "summary": "修改", "operationId": "update_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTypeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productReleaseType/save": {"post": {"tags": ["产品列表-发布类型"], "summary": "新增", "operationId": "insert_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTypeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productReleaseType/getList": {"post": {"tags": ["产品列表-发布类型"], "summary": "分页获取产品列表列表", "operationId": "selectAll_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTypeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productDistributionBasic/update": {"post": {"tags": ["产品分销信息-产品分销信息"], "summary": "修改", "operationId": "update_2", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDistributionBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productDistributionBasic/save": {"post": {"tags": ["产品分销信息-产品分销信息"], "summary": "新增", "operationId": "insert_2", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDistributionBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productDistributionBasic/getList": {"post": {"tags": ["产品分销信息-产品分销信息"], "summary": "分页获取产品分销信息列表", "operationId": "selectAll_2", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDistributionBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/updateProductCategoryInfo": {"post": {"tags": ["产品品类"], "summary": "更增产品分类", "operationId": "updateProductCategoryInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/saveProductCategoryInfo": {"post": {"tags": ["产品品类"], "summary": "新增产品分类", "operationId": "saveProductCategoryInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/getProductCategoryList": {"post": {"tags": ["产品品类"], "summary": "获取产品分类列表", "operationId": "getProductCategoryList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/getProductCategoryById": {"post": {"tags": ["产品品类"], "summary": "根据id获取产品分类", "operationId": "getProductCategoryById", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/deleteProductCategory": {"post": {"tags": ["产品品类"], "summary": "删除产品分类", "operationId": "deleteProductCategory", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productCategory/allocationAttribute": {"post": {"tags": ["产品品类"], "summary": "分配属性", "operationId": "allocationAttribute", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/update": {"post": {"tags": ["产品信息"], "summary": "修改", "operationId": "update_3", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/save": {"post": {"tags": ["产品信息"], "summary": "新增", "operationId": "insert_3", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/getProductList": {"post": {"tags": ["产品信息"], "summary": "分页查询产品列表", "operationId": "selectProductAll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecificationsBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/getList": {"post": {"tags": ["产品信息"], "summary": "分页获取产品信息", "operationId": "selectAll_3", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductBasicModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/updateProductAttributeInfo": {"post": {"tags": ["产品属性"], "summary": "更新产品属性", "operationId": "updateProductAttributeInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductAttributeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/saveProductAttributeInfo": {"post": {"tags": ["产品属性"], "summary": "新增产品属性", "operationId": "saveProductAttributeInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductAttributeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/getProductAttributeList": {"post": {"tags": ["产品属性"], "summary": "获取产品属性列表", "operationId": "getProductAttributeList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductAttributeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/getProductAttributeById": {"post": {"tags": ["产品属性"], "summary": "根据id获取产品属性", "operationId": "getProductAttributeById", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductAttributeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/deleteProductAttribute": {"post": {"tags": ["产品属性"], "summary": "删除产品属性", "operationId": "deleteProductAttribute", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductAttributeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/updateOrgInfo": {"post": {"tags": ["机构管理"], "summary": "更新机构", "operationId": "updateOrgInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrgModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/saveOrgInfo": {"post": {"tags": ["机构管理"], "summary": "新增机构", "operationId": "saveOrgInfo", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrgModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/logout": {"post": {"tags": ["系统注销服务"], "summary": "系统注销", "operationId": "logout", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVesselSetting/updateFirstVesselSetting": {"post": {"tags": ["头程服务设置"], "summary": "更新头程服务设置", "operationId": "updateFirstVesselSetting", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselSettingModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVesselSetting/saveFirstVesselSetting": {"post": {"tags": ["头程服务设置"], "summary": "保存头程服务设置", "operationId": "saveFirstVessel", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselSettingModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVesselSetting/getFirstVesselSettingList": {"post": {"tags": ["头程服务设置"], "summary": "查询头程服务设置", "operationId": "getFirstVesselSettingList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselSettingModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultIPageFirstVesselSettingVo"}}}}}}}, "/firstVesselSetting/deleteFirstVesselSetting": {"post": {"tags": ["头程服务设置"], "summary": "删除头程服务设置", "operationId": "deleteFirstVesselSetting", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselSettingModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/updateFirstVessel": {"post": {"tags": ["头程运单管理"], "summary": "更新运单", "operationId": "updateFirstVessel", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/saveFirstVessel": {"post": {"tags": ["头程运单管理"], "summary": "新增运单", "operationId": "saveFirstVessel_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/pickingGoods": {"post": {"tags": ["头程运单管理"], "summary": "拣货", "operationId": "pickingGoods", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PickingGoodsModel"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/getThridBoxList": {"post": {"tags": ["头程运单管理"], "summary": "获取第三方箱信息", "operationId": "getThridBoxList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListBoxVo"}}}}}}}, "/firstVessel/getSystemBoxList": {"post": {"tags": ["头程运单管理"], "summary": "获取系统箱信息", "operationId": "getSystemBoxList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListBoxVo"}}}}}}}, "/firstVessel/getFirstVesselThirdTrajectoryList": {"post": {"tags": ["头程运单管理"], "summary": "获取头程运单第三方轨迹列表", "operationId": "getFirstVesselThirdTrajectoryList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListFirstVesselTrajectoryVo"}}}}}}}, "/firstVessel/getFirstVesselSystemTrajectoryList": {"post": {"tags": ["头程运单管理"], "summary": "获取头程运单系统轨迹列表", "operationId": "getFirstVesselSystemTrajectoryList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListFirstVesselTrajectoryVo"}}}}}}}, "/firstVessel/getFirstVesselList": {"post": {"tags": ["头程运单管理"], "summary": "分页查询头程运单列表", "operationId": "getFirstVesselList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultIPageFirstVesselVo"}}}}}}}, "/firstVessel/getFirstVesselById": {"post": {"tags": ["头程运单管理"], "summary": "根据id获取头程运单信息", "operationId": "getFirstVesselById", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultFirstVesselVo"}}}}}}}, "/firstVessel/batchUpdateServiceProviderMeasureData": {"post": {"tags": ["头程运单管理"], "summary": "批量更新服务商测量数据", "operationId": "batchUpdateServiceProviderMeasureData", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchUpdateFirstVessel": {"post": {"tags": ["头程运单管理"], "summary": "批量更新头程信息", "operationId": "batchUpdateFirstVessel", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchUpdateFirstVesselAndUseServiceProviderMeasureData": {"post": {"tags": ["头程运单管理"], "summary": "批量更新头程信息并使用服务商测量数据", "operationId": "batchUpdateFirstVesselAndUseServiceProviderMeasureData", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchUpdateAndUseServiceProviderTrack": {"post": {"tags": ["头程运单管理"], "summary": "批量更新并使用服务商轨迹", "operationId": "batchUpdateAndUseServiceProviderTrack", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchUpdateAndUseServiceProviderExpressMainOrderNumber": {"post": {"tags": ["头程运单管理"], "summary": "批量更新服务商主单号并使用服务商测量数据", "operationId": "batchUpdateAndUseServiceProviderExpressMainOrderNumber", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchRealTimeSyncServiceProviderNewTrack": {"post": {"tags": ["头程运单管理"], "summary": "批量实时同步服务商新轨迹", "operationId": "batchRealTimeSyncServiceProviderNewTrack", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchPushFirstVessel": {"post": {"tags": ["头程运单管理"], "summary": "批量推送运单", "operationId": "batchPushFirstVessel", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchPushFirstVesselAgain": {"post": {"tags": ["头程运单管理"], "summary": "批量推送运单", "operationId": "batchPushFirstVesselAgain", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchImportTrack": {"post": {"tags": ["头程运单管理"], "summary": "批量导入轨迹", "operationId": "batchImportTrack", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchEditExpressMainOrderNumber": {"post": {"tags": ["头程运单管理"], "summary": "批量编辑快递主单号", "operationId": "batchEditExpressMainOrderNumber", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchEditCost": {"post": {"tags": ["头程运单管理"], "summary": "批量编辑费用(新增、删除费用)", "operationId": "batchEditCost", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchDeleteInternalRemarks": {"post": {"tags": ["头程运单管理"], "summary": "批量删除内部备注", "operationId": "batchDeleteInternalRemarks", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchDeleteCustomerRemarks": {"post": {"tags": ["头程运单管理"], "summary": "批量删除客户备注", "operationId": "batchDeleteCustomerRemarks", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchCancelFirstVessel": {"post": {"tags": ["头程运单管理"], "summary": "批量取消运单", "operationId": "batchCancelFirstVessel", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchAdjustFirstVesselStatus": {"post": {"tags": ["头程运单管理"], "summary": "批量调整运单状态(转运中、已签收、取件、取消运单)", "operationId": "batchAdjustFirstVesselStatus", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchAddTrack": {"post": {"tags": ["头程运单管理"], "summary": "批量添加轨迹", "operationId": "batchAddTrack", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVessel/batchAddRemarks": {"post": {"tags": ["头程运单管理"], "summary": "批量添加备注(客户备注、内部备注)", "operationId": "batchAddRemarks", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirstVesselModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/update": {"post": {"tags": ["结算单管理管理"], "summary": "修改", "operationId": "update_4", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FinanceSettdocManageModel"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/save": {"post": {"tags": ["结算单管理管理"], "summary": "新增", "operationId": "insert_4", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FinanceSettdocManageModel"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/getList": {"post": {"tags": ["结算单管理管理"], "summary": "分页获取结算单管理管理列表", "operationId": "selectAll_4", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceSettdocManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/update": {"post": {"tags": ["银行账号管理"], "summary": "修改", "operationId": "update_5", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/save": {"post": {"tags": ["银行账号管理"], "summary": "新增", "operationId": "insert_5", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/getList": {"post": {"tags": ["银行账号管理"], "summary": "分页获取银行账号管理列表", "operationId": "selectAll_5", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceBankAccountModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/findAll": {"post": {"tags": ["银行账号管理"], "summary": "获取银行账号列表", "operationId": "findAll", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/enable": {"post": {"tags": ["银行账号管理"], "summary": "启用", "operationId": "enable", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/disable": {"post": {"tags": ["银行账号管理"], "summary": "禁用", "operationId": "disable", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/update": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "修改", "operationId": "update_6", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceAccountRechargeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/save": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "新增", "operationId": "insert_6", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceAccountRechargeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/getList": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "分页获取充值账户管理列表", "operationId": "selectAll_6", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceAccountRechargeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/examine": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "审核", "operationId": "examine", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinanceAccountRechargeModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/enable": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "启用", "operationId": "enable_1", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/disable": {"post": {"tags": ["财务管理-充值账户管理"], "summary": "禁用", "operationId": "disable_1", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/dict/saveDict": {"post": {"tags": ["字典管理"], "summary": "新增字典", "operationId": "saveOrgInfo_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/dict/saveDictItem": {"post": {"tags": ["字典管理"], "summary": "新增字典详情", "operationId": "saveDictItem", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictItemModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/currencyManage/updateCurrencySetting": {"post": {"tags": ["财务管理-币种管理"], "summary": "修改币种设置", "operationId": "updateCurrencySetting", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/currencyManage/getCurrencyList": {"post": {"tags": ["财务管理-币种管理"], "summary": "获取币种汇率列表", "operationId": "getCurrencyList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultIPageCurrencyVo"}}}}}}}, "/currencyManage/getCurrencyHistoryRecordList": {"post": {"tags": ["财务管理-币种管理"], "summary": "获取币种汇率历史记录列表", "operationId": "getCurrencyHistoryRecordList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultIPageCurrencyHistoryRecordVo"}}}}}}}, "/currencyManage/enableOrDisableCurrency": {"post": {"tags": ["财务管理-币种管理"], "summary": "启用或禁用币种", "operationId": "enableOrDisableCurrency", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/currencyManage/defaultRegularUpdateStrategy": {"post": {"tags": ["财务管理-币种管理"], "summary": "默认币种汇率定时更新策略", "operationId": "defaultRegularUpdateStrategy", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobInfo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/currencyManage/currencyRegularUpdateStrategy": {"post": {"tags": ["财务管理-币种管理"], "summary": "币种配置", "operationId": "currencyRegularUpdateStrategy", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobInfo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/update": {"post": {"tags": ["仓库管理"], "summary": "修改仓库", "operationId": "update_7", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseWarehouseManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/save": {"post": {"tags": ["仓库管理"], "summary": "新增仓库", "operationId": "insert_7", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseWarehouseManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/getList": {"post": {"tags": ["仓库管理"], "summary": "分页获取仓库管理列表", "operationId": "selectAll_7", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseWarehouseManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/findAll": {"post": {"tags": ["仓库管理"], "summary": "获取全部仓库管理列表", "operationId": "findAll_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseWarehouseManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/enable": {"post": {"tags": ["仓库管理"], "summary": "启用", "operationId": "enable_2", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/disable": {"post": {"tags": ["仓库管理"], "summary": "禁用", "operationId": "disable_2", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/update": {"post": {"tags": ["服务商管理"], "summary": "修改", "operationId": "update_8", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseServiceProviderManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/save": {"post": {"tags": ["服务商管理"], "summary": "新增", "operationId": "insert_8", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseServiceProviderManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/getList": {"post": {"tags": ["服务商管理"], "summary": "分页获取服务商管理列表", "operationId": "selectAll_8", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseServiceProviderManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/enable": {"post": {"tags": ["服务商管理"], "summary": "启用", "operationId": "enable_3", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/disable": {"post": {"tags": ["服务商管理"], "summary": "禁用", "operationId": "disable_3", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/update": {"post": {"tags": ["地点管理"], "summary": "修改", "operationId": "update_9", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseLocationManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/save": {"post": {"tags": ["地点管理"], "summary": "新增", "operationId": "insert_9", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseLocationManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/getList": {"post": {"tags": ["地点管理"], "summary": "分页获取地点管理列表", "operationId": "selectAll_9", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseLocationManageModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/enable": {"post": {"tags": ["地点管理"], "summary": "启用", "operationId": "enable_4", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/disable": {"post": {"tags": ["地点管理"], "summary": "禁用", "operationId": "disable_4", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/base/login": {"post": {"tags": ["系统登陆服务"], "summary": "账号、口令认证", "operationId": "base_login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseLoginModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAuthenticationResponse"}}}}}}}, "/sellerManagement/resetSellerPassword": {"get": {"tags": ["卖家管理"], "summary": "重置卖家密码", "operationId": "resetSellerPassword", "parameters": [{"name": "sellerId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/sellerManagement/findSellerAll": {"get": {"tags": ["卖家管理"], "summary": "获取卖家列表", "operationId": "findSellerAll", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListSellerVo"}}}}}}}, "/role/getOrgInfo": {"get": {"tags": ["角色管理"], "summary": "根据角色id获取角色信息", "operationId": "getOrgInfo", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/getMenuTree": {"get": {"tags": ["角色管理"], "summary": "获取菜单树", "operationId": "getMenuTree", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/getMenuIdList": {"get": {"tags": ["角色管理"], "summary": "根据角色id获取角色已配置菜单id", "operationId": "getMenuIdList", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/role/deleteOrgInfo": {"get": {"tags": ["角色管理"], "summary": "删除角色", "operationId": "deleteOrgInfo", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productSpecificationsBasic/getById": {"get": {"tags": ["产品列表-产品规格信息"], "summary": "根据id获取信息", "operationId": "selectOne", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productSpecificationsBasic/delById": {"get": {"tags": ["产品列表-产品规格信息"], "summary": "删除", "operationId": "delete", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productReleaseType/getById": {"get": {"tags": ["产品列表-发布类型"], "summary": "根据id获取信息", "operationId": "selectOne_1", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productReleaseType/delById": {"get": {"tags": ["产品列表-发布类型"], "summary": "删除", "operationId": "delete_1", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productDistributionBasic/getById": {"get": {"tags": ["产品分销信息-产品分销信息"], "summary": "根据id获取信息", "operationId": "selectOne_2", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productDistributionBasic/delById": {"get": {"tags": ["产品分销信息-产品分销信息"], "summary": "删除", "operationId": "delete_2", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/reject": {"get": {"tags": ["产品信息"], "summary": "拒绝审核", "operationId": "reject", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/getById": {"get": {"tags": ["产品信息"], "summary": "根据id获取信息", "operationId": "selectOne_3", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/examine": {"get": {"tags": ["产品信息"], "summary": "通过审核", "operationId": "examine_1", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productBasic/delById": {"get": {"tags": ["产品信息"], "summary": "删除", "operationId": "delete_3", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/productAttribute/getProductAttributeListByProductId": {"get": {"tags": ["产品属性"], "summary": "根据产品id获取产品属性列表", "operationId": "getProductAttributeListByProductId", "parameters": [{"name": "productId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/getOrgTree": {"get": {"tags": ["机构管理"], "summary": "获取机构树", "operationId": "getOrg<PERSON>ree", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/getOrgRoleTree": {"get": {"tags": ["机构管理"], "summary": "获取机构角色树", "operationId": "getOrgRoleTree", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/getOrgInfo": {"get": {"tags": ["机构管理"], "summary": "根据机构id获取机构信息", "operationId": "getOrgInfo_1", "parameters": [{"name": "orgId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/org/deleteOrgInfo": {"get": {"tags": ["机构管理"], "summary": "删除机构", "operationId": "deleteOrgInfo_1", "parameters": [{"name": "orgId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/firstVesselSetting/getFirstVesselSettingById": {"get": {"tags": ["头程服务设置"], "summary": "根据id查询头程服务设置", "operationId": "getFirstVesselSettingList_1", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultFirstVesselSettingVo"}}}}}}}, "/firstVesselSetting/getAllFirstVesselSettingList": {"get": {"tags": ["头程服务设置"], "summary": "查询所有头程服务设置", "operationId": "getAllFirstVesselSettingList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListFirstVesselSettingVo"}}}}}}}, "/financeSettdocManage/getByCustomerId": {"get": {"tags": ["结算单管理管理"], "summary": "根据客户id获取信息", "operationId": "selectOne_4", "parameters": [{"name": "customerId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/generate": {"get": {"tags": ["结算单管理管理"], "summary": "结算单生成", "operationId": "generate", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/delById": {"get": {"tags": ["结算单管理管理"], "summary": "删除", "operationId": "delete_4", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeSettdocManage/confirm": {"get": {"tags": ["结算单管理管理"], "summary": "结算单确认", "operationId": "confirm", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/getById": {"get": {"tags": ["银行账号管理"], "summary": "根据角色id获取信息", "operationId": "selectOne_5", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeBankAccount/delById": {"get": {"tags": ["银行账号管理"], "summary": "删除", "operationId": "delete_5", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/getById": {"get": {"tags": ["财务管理-充值账户管理"], "summary": "根据角色id获取信息", "operationId": "selectOne_6", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/financeAccountRecharge/delById": {"get": {"tags": ["财务管理-充值账户管理"], "summary": "删除", "operationId": "delete_6", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/dict/getDictItemList": {"get": {"tags": ["字典管理"], "summary": "根据字典code获取字典详情", "operationId": "getDictItemList", "parameters": [{"name": "dictCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/currencyManage/getCurrencyDropDownList": {"get": {"tags": ["财务管理-币种管理"], "summary": "获取下拉列表", "operationId": "getCurrencyDropDownList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCurrencyVo"}}}}}}}, "/baseWarehouseManage/getById": {"get": {"tags": ["仓库管理"], "summary": "根据角色id获取仓库信息", "operationId": "selectOne_7", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseWarehouseManage/delById": {"get": {"tags": ["仓库管理"], "summary": "删除仓库", "operationId": "delete_7", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/getById": {"get": {"tags": ["服务商管理"], "summary": "根据角色id获取信息", "operationId": "selectOne_8", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseServiceProviderManage/delById": {"get": {"tags": ["服务商管理"], "summary": "删除", "operationId": "delete_8", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/getById": {"get": {"tags": ["地点管理"], "summary": "根据角色id获取信息", "operationId": "selectOne_9", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}, "/baseLocationManage/delById": {"get": {"tags": ["地点管理"], "summary": "删除", "operationId": "delete_9", "parameters": [{"name": "idList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}}}}}, "components": {"schemas": {"ResultObject": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "object", "description": "返回数据对象"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "SysUserModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "用户id", "refType": null}, "orgId": {"type": "string", "description": "机构id", "refType": null}, "position": {"type": "string", "description": "职位", "refType": null}, "roleId": {"type": "string", "description": "角色id", "refType": null}, "userName": {"type": "string", "description": "用户名", "refType": null}, "nickName": {"type": "string", "description": "昵称", "refType": null}, "phone": {"type": "string", "description": "手机号", "refType": null}, "email": {"type": "string", "description": "邮箱", "refType": null}, "status": {"type": "string", "description": "状态 0 禁用 1 启用", "refType": null}, "type": {"type": "string", "refType": null}}}, "SellerModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "companyName": {"type": "string", "description": "公司名称", "refType": null}, "companyCode": {"type": "string", "description": "公司编码", "refType": null}, "companyAbbr": {"type": "string", "description": "公司简称", "refType": null}, "creditCode": {"type": "string", "description": "统一信用代码", "refType": null}, "legalPersonId": {"type": "string", "description": "法人证件号", "refType": null}, "legalPersonName": {"type": "string", "description": "法人代表名称", "refType": null}, "businessLicense": {"type": "string", "description": "营业执照", "refType": null}, "legalPersonIdCard": {"type": "string", "description": "法人身份证", "refType": null}, "companyPhone": {"type": "string", "description": "公司座机", "refType": null}, "email": {"type": "string", "description": "邮箱", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "detailedAddressOne": {"type": "string", "description": "详细地址1", "refType": null}, "detailedAddressTwo": {"type": "string", "description": "详细地址2", "refType": null}, "registerPlaceId": {"type": "string", "description": "注册地", "refType": null}, "contactPlaceId": {"type": "string", "description": "联系地", "refType": null}, "contactsName": {"type": "string", "description": "联系人姓名", "refType": null}, "contactsPhone": {"type": "string", "description": "联系人电话", "refType": null}, "contactsIdCard": {"type": "string", "description": "联系人证件号", "refType": null}, "contactsPosition": {"type": "string", "description": "联系人职位", "refType": null}, "status": {"type": "string", "description": "状态 0 禁用 1 启用 2 冻结", "refType": null}, "minAmount": {"type": "number", "description": "最小金额", "refType": null}, "maxAmount": {"type": "number", "description": "最大金额", "refType": null}, "currency": {"type": "string", "description": "账户币种", "refType": null}, "warehouse": {"type": "array", "description": "仓库", "items": {"type": "string", "description": "仓库"}, "refType": "string"}}}, "RechargeRecordModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "refType": null}, "sellerId": {"type": "string", "description": "卖家", "refType": null}, "rechargeType": {"type": "string", "description": "充值方式", "refType": null}, "bankCard": {"type": "string", "description": "收款银行卡", "refType": null}, "bankCardName": {"type": "string", "description": "付款银行卡名称", "refType": null}, "rechargeAmount": {"type": "string", "description": "充值金额", "refType": null}, "rechargeAccount": {"type": "string", "description": "充值账户", "refType": null}, "bankName": {"type": "string", "description": "收款银行名称", "refType": null}, "paymentSerialNumber": {"type": "string", "description": "付款流水号", "refType": null}, "remark": {"type": "string", "description": "备注", "refType": null}, "rechargeVoucher": {"type": "string", "description": "充值凭证", "refType": null}}}, "SellerBankAccountModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "seller": {"type": "string", "description": "商家", "refType": null}, "businessType": {"type": "string", "description": "业务类型 头程、海外仓", "refType": null}, "currency": {"type": "string", "description": "账户币种", "refType": null}, "quota": {"type": "number", "description": "额度", "refType": null}, "status": {"type": "string", "description": "状态", "refType": null}, "settlementMode": {"type": "string", "description": "结算周期", "refType": null}, "billGenerationDay": {"type": "string", "description": "结算日", "refType": null}, "ids": {"type": "array", "description": "ids", "items": {"type": "string", "description": "ids"}, "refType": "string"}}}, "RoleModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "roleName": {"type": "string", "description": "角色名称", "refType": null}, "orgId": {"type": "string", "description": "机构id", "refType": null}, "sort": {"type": "integer", "description": "排序", "format": "int32", "refType": null}, "status": {"type": "string", "description": "状态", "refType": null}}}, "RoleMenuModel": {"type": "object", "properties": {"roleId": {"type": "string", "description": "角色id", "refType": null}, "menuIds": {"type": "array", "description": "菜单id", "items": {"type": "string", "description": "菜单id"}, "refType": "string"}}}, "ReceivingAccountModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "主键", "refType": null}, "paymentMethod": {"type": "string", "description": "付款方式", "refType": null}, "currency": {"type": "string", "description": "收款币种", "refType": null}, "accountName": {"type": "string", "description": "账户名", "refType": null}, "accountHolder": {"type": "string", "description": "户名", "refType": null}, "accountNumber": {"type": "string", "description": "账号", "refType": null}, "bankName": {"type": "string", "description": "开户银行", "refType": null}, "jointBank": {"type": "string", "description": "联行号", "refType": null}, "paymentCode": {"type": "string", "description": "收款码", "refType": null}}}, "IPageReceivingAccountVo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64", "refType": null}, "current": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int64", "deprecated": true, "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/ReceivingAccountVo"}, "refType": "ReceivingAccountVo"}}, "description": "返回数据对象"}, "ReceivingAccountVo": {"type": "object", "properties": {"id": {"type": "string", "description": "主键", "refType": null}, "paymentMethod": {"type": "string", "description": "付款方式", "refType": null}, "paymentMethodName": {"type": "string", "description": "付款方式名称", "refType": null}, "currency": {"type": "string", "description": "收款币种", "refType": null}, "currencyName": {"type": "string", "description": "币种名称", "refType": null}, "accountName": {"type": "string", "description": "账户名", "refType": null}, "accountHolder": {"type": "string", "description": "户名", "refType": null}, "accountNumber": {"type": "string", "description": "账号", "refType": null}, "bankName": {"type": "string", "description": "开户银行", "refType": null}, "jointBank": {"type": "string", "description": "联行号", "refType": null}, "paymentCode": {"type": "string", "description": "收款码", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}}}, "ResultIPageReceivingAccountVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/IPageReceivingAccountVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ProductSpecificationsBasicModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人/备货商-查询条件", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "productAttr": {"type": "string", "description": "规格属性用逗号,分割（1颜色，2尺寸，3重量，4材质，5风格，6型号，7商品数量，8容量）", "refType": null}, "productAttrOne": {"type": "string", "description": "选择的属性1", "refType": null}, "productAttrTwo": {"type": "string", "description": "选择的属性2", "refType": null}, "productSkuCode": {"type": "string", "description": "商品编码sku-查询条件", "refType": null}, "productSkuUrl": {"type": "string", "description": "sku主图", "refType": null}, "productLength": {"type": "string", "description": "长度", "refType": null}, "productWidth": {"type": "string", "description": "宽度", "refType": null}, "productHeight": {"type": "string", "description": "高度", "refType": null}, "productWeight": {"type": "string", "description": "重量", "refType": null}, "productSupplyPrice": {"type": "string", "description": "供应价（美元）", "refType": null}, "productReleaseTypeId": {"type": "string", "description": "产品发布类型id", "refType": null}, "productBasicId": {"type": "string", "description": "产品信息id", "refType": null}, "productDistributionId": {"type": "string", "description": "产品分销id", "refType": null}, "productAttrAll": {"type": "string", "description": "选择的属性总", "refType": null}, "productAttrOneAll": {"type": "string", "description": "选择的属性1总", "refType": null}, "productAttrTwoAll": {"type": "string", "description": "选择的属性1总", "refType": null}, "startTimes": {"type": "string", "description": "开始时间-查询条件", "format": "date-time", "refType": null}, "endTimes": {"type": "string", "description": "结束时间-查询条件", "format": "date-time", "refType": null}}}, "ProductSpecificationsBasicSaveModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "productSpeBasic": {"type": "array", "description": "分销入参", "items": {"$ref": "#/components/schemas/ProductSpecificationsBasicModel"}, "refType": "ProductSpecificationsBasicModel"}, "productAttrAll": {"type": "string", "refType": null}, "productAttrOneAll": {"type": "string", "refType": null}, "productAttrTwoAll": {"type": "string", "refType": null}}}, "ProductReleaseTypeModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "type": {"type": "string", "description": "类型（1仅备货至海外仓（一件代发、中转等），2备货到海外仓且加入分销平台，3仅加入分销平台（第三方仓发货））", "refType": null}, "warehouseAddress": {"type": "string", "description": "类型为3仅加入分销平台才有的仓库地址", "refType": null}, "category": {"type": "string", "description": "商品类别", "refType": null}}}, "ProductDistributionBasicModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "productHostGraphUrl": {"type": "string", "description": "主图url，逗号,分割", "refType": null}, "productDimensionUrl": {"type": "string", "description": "尺寸图url，逗号,分割", "refType": null}, "productCnTitle": {"type": "string", "description": "商品中文标题", "refType": null}, "productEnTitle": {"type": "string", "description": "商品英文标题", "refType": null}, "productCategoryId": {"type": "string", "description": "对应发布类型的商品类别-》产品品类id", "refType": null}, "productDetails": {"type": "string", "description": "详情图文，存储前端代码、图片url方便回显", "refType": null}, "productReleaseTypeId": {"type": "string", "description": "产品发布类型id", "refType": null}, "productBasicId": {"type": "string", "description": "产品信息id", "refType": null}}}, "ProductCategoryModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "parentId": {"type": "string", "description": "父级id", "refType": null}, "name": {"type": "string", "description": "产品名称", "refType": null}, "description": {"type": "string", "description": "描述", "refType": null}, "status": {"type": "string", "description": "产品状态", "refType": null}, "attributes": {"type": "array", "description": "属性", "items": {"type": "string", "description": "属性"}, "refType": "string"}, "level": {"type": "integer", "description": "层级", "format": "int32", "refType": null}}}, "ProductBasicModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "productCode": {"type": "string", "description": "商品集编码", "refType": null}, "productName": {"type": "string", "description": "商品名称", "refType": null}, "productCnDeclare": {"type": "string", "description": "中文申报品名", "refType": null}, "productEnDeclare": {"type": "string", "description": "英文申报品名", "refType": null}, "productBrandName": {"type": "string", "description": "品牌名", "refType": null}, "productModel": {"type": "string", "description": "型号", "refType": null}, "productCnMaterial": {"type": "string", "description": "中文材质", "refType": null}, "productEnMaterial": {"type": "string", "description": "英文材质", "refType": null}, "productCnPurpose": {"type": "string", "description": "中文用途", "refType": null}, "productEnPurpose": {"type": "string", "description": "英文用途", "refType": null}, "productPrice": {"type": "string", "description": "申报单价（USD）", "refType": null}, "customsCode": {"type": "string", "description": "海关编码", "refType": null}, "goodsAttr": {"type": "string", "description": "货物属性（1普货）", "refType": null}, "goodsType": {"type": "string", "description": "货物类型（1包裹）", "refType": null}, "isElectricity": {"type": "string", "description": "是否带电（1无磁无电）", "refType": null}, "productReviewUrl": {"type": "string", "description": "商品审核链接", "refType": null}, "productBarCode": {"type": "string", "description": "自定义条码", "refType": null}, "productReleaseTypeId": {"type": "string", "description": "产品发布类型id", "refType": null}}}, "ProductAttributeModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "name": {"type": "string", "description": "属性名称", "refType": null}, "value": {"type": "string", "description": "属性值", "refType": null}, "inputType": {"type": "string", "description": "输入类型", "refType": null}, "isAddValue": {"type": "boolean", "description": "是否可添加属性值", "refType": null}, "limitValue": {"type": "string", "description": "限制值", "refType": null}, "description": {"type": "string", "description": "描述", "refType": null}}}, "OrgModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "refType": null}, "orgCode": {"type": "string", "description": "机构编码", "refType": null}, "orgName": {"type": "string", "description": "机构名称", "refType": null}, "parentId": {"type": "string", "description": "父级id", "refType": null}, "chargePerson": {"type": "string", "description": "负责人", "refType": null}, "telephone": {"type": "string", "description": "电话", "refType": null}, "mobilePhone": {"type": "string", "description": "手机", "refType": null}, "quota": {"type": "integer", "description": "限额", "format": "int32", "refType": null}, "address": {"type": "string", "description": "地址", "refType": null}, "managementMode": {"type": "string", "description": "管理方式", "refType": null}, "postcode": {"type": "string", "description": "邮编", "refType": null}}}, "FirstVesselSettingModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "transportServiceName": {"type": "string", "description": "运输服务名称", "refType": null}, "transportServiceCode": {"type": "string", "description": "运输服务代码", "refType": null}, "chargeMode": {"type": "string", "description": "计费方式", "refType": null}, "weightMode": {"type": "string", "description": "计重方式", "refType": null}, "roundMode": {"type": "string", "description": "进位方式", "refType": null}, "bubbleCoefficient": {"type": "number", "description": "计泡系数", "refType": null}, "chargeWeightMode": {"type": "string", "description": "收费重方式", "refType": null}, "ticketWeightPrecision": {"type": "number", "description": "票计重精度", "refType": null}, "boxWeightPrecision": {"type": "number", "description": "箱计重精度", "refType": null}, "sizePrecision": {"type": "number", "description": "尺寸精度", "refType": null}, "minBoxRealWeight": {"type": "number", "description": "最低箱实重/体积", "refType": null}, "minBoxMaterialWeight": {"type": "number", "description": "最低箱材重", "refType": null}, "minBoxChargeWeight": {"type": "number", "description": "最低箱收费重", "refType": null}, "minTicketChargeWeight": {"type": "number", "description": "最低票收费重", "refType": null}, "arrivalCountry": {"type": "string", "description": "到达国家", "refType": null}, "customer": {"type": "string", "description": "客户", "refType": null}, "destinationType": {"type": "string", "description": "目的地类型", "refType": null}, "ids": {"type": "array", "description": "id集合", "items": {"type": "string", "description": "id集合"}, "refType": "string"}, "status": {"type": "string", "description": "状态", "refType": null}}}, "FirstVesselSettingVo": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "refType": null}, "transportServiceName": {"type": "string", "description": "运输服务名称", "refType": null}, "transportServiceCode": {"type": "string", "description": "运输服务代码", "refType": null}, "chargeMode": {"type": "string", "description": "计费方式", "refType": null}, "chargeModeName": {"type": "string", "refType": null}, "weightMode": {"type": "string", "description": "计重方式", "refType": null}, "weightModeName": {"type": "string", "refType": null}, "roundMode": {"type": "string", "description": "进位方式", "refType": null}, "bubbleCoefficient": {"type": "string", "description": "计泡系数", "refType": null}, "chargeWeightMode": {"type": "string", "description": "收费重方式", "refType": null}, "ticketWeightPrecision": {"type": "number", "description": "票计重精度", "refType": null}, "boxWeightPrecision": {"type": "number", "description": "箱计重精度", "refType": null}, "sizePrecision": {"type": "number", "description": "尺寸精度", "refType": null}, "minBoxRealWeight": {"type": "number", "description": "最低箱实重/体积", "refType": null}, "minBoxMaterialWeight": {"type": "number", "description": "最低箱材重", "refType": null}, "minBoxChargeWeight": {"type": "number", "description": "最低箱收费重", "refType": null}, "minTicketChargeWeight": {"type": "number", "description": "最低票收费重", "refType": null}, "arrivalCountry": {"type": "string", "description": "到达国家", "refType": null}, "status": {"type": "string", "description": "状态", "refType": null}, "statusName": {"type": "string", "description": "状态名称", "refType": null}, "destinationType": {"type": "string", "description": "目的地类型", "refType": null}, "destinationTypeName": {"type": "string", "refType": null}, "customer": {"type": "string", "description": "客户", "refType": null}}}, "IPageFirstVesselSettingVo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64", "refType": null}, "current": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int64", "deprecated": true, "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/FirstVesselSettingVo"}, "refType": "FirstVesselSettingVo"}}, "description": "返回数据对象"}, "ResultIPageFirstVesselSettingVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/IPageFirstVesselSettingVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "BoxModel": {"type": "object", "properties": {"boxNum": {"type": "integer", "description": "箱子数量", "format": "int32", "refType": null}, "boxBarcode": {"type": "string", "description": "自定义箱条码", "refType": null}, "boxBarcodeRule": {"type": "string", "description": "箱条码规则", "refType": null}, "systemBoxLength": {"type": "string", "description": "箱子长", "refType": null}, "systemBoxWidth": {"type": "string", "description": "箱子宽", "refType": null}, "systemBoxHeight": {"type": "string", "description": "箱子高", "refType": null}, "systemBoxWeight": {"type": "string", "description": "箱子重量", "refType": null}, "commodity": {"type": "array", "description": "商品", "items": {"$ref": "#/components/schemas/CommodityModel"}, "refType": "CommodityModel"}}, "description": "箱子信息"}, "CommodityModel": {"type": "object", "properties": {"commodityId": {"type": "string", "description": "商品id", "refType": null}, "commodityCount": {"type": "integer", "description": "商品数量", "format": "int32", "refType": null}}, "description": "商品"}, "FirstVesselModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "id", "refType": null}, "customer": {"type": "string", "description": "客户", "refType": null}, "customerWaybillNumber": {"type": "string", "description": "客户运单号", "refType": null}, "deliveryWarehouse": {"type": "string", "description": "交货仓库", "refType": null}, "destinationType": {"type": "string", "description": "目的地类型", "refType": null}, "shippingService": {"type": "string", "description": "运输服务", "refType": null}, "destination": {"type": "string", "description": "目的地", "refType": null}, "declarationMethod": {"type": "string", "description": "报关方式", "refType": null}, "taxationMethod": {"type": "string", "description": "交税方式", "refType": null}, "vatNo": {"type": "string", "description": "VAT号", "refType": null}, "vatAddress": {"type": "string", "description": "VAT地址", "refType": null}, "eoriNo": {"type": "string", "description": "EORI号", "refType": null}, "eoriAddress": {"type": "string", "description": "EORI地址", "refType": null}, "storageType": {"type": "string", "description": "入库类型", "refType": null}, "containerType": {"type": "string", "description": "货柜类型", "refType": null}, "purchaseInsurance": {"type": "string", "description": "购买保险", "refType": null}, "customerRemarks": {"type": "string", "description": "客户备注", "refType": null}, "internalRemarks": {"type": "string", "description": "内部备注", "refType": null}, "boxInfo": {"type": "array", "description": "箱子信息", "items": {"$ref": "#/components/schemas/BoxModel"}, "refType": "BoxModel"}, "onlyVessel": {"type": "boolean", "description": "是否只创建头程", "refType": null}, "ids": {"type": "array", "description": "id列表", "items": {"type": "string", "description": "id列表"}, "refType": "string"}, "startTime": {"type": "string", "description": "开始时间", "format": "date-time", "refType": null}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "refType": null}, "recipient": {"type": "string", "description": "收件人", "refType": null}, "addressOne": {"type": "string", "description": "地址一", "refType": null}, "addressTwo": {"type": "string", "description": "地址二", "refType": null}, "city": {"type": "string", "description": "城市", "refType": null}, "state": {"type": "string", "description": "州", "refType": null}, "zipCode": {"type": "string", "description": "邮编", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "phone": {"type": "string", "description": "电话", "refType": null}, "email": {"type": "string", "description": "邮箱", "refType": null}}}, "PickingGoodsModel": {"type": "object", "properties": {"id": {"type": "string", "refType": null}, "length": {"type": "number", "description": "长", "refType": null}, "width": {"type": "number", "description": "宽", "refType": null}, "height": {"type": "number", "description": "高", "refType": null}, "weight": {"type": "number", "description": "重量", "refType": null}, "carrier": {"type": "string", "description": "承运商", "refType": null}, "waybillNumber": {"type": "string", "description": "单号", "refType": null}, "shippingService": {"type": "string", "description": "运输服务", "refType": null}, "maxActualWeight": {"type": "number", "refType": null}, "maxWeightProduct": {"type": "number", "refType": null}, "maxMaterialWeight": {"type": "number", "refType": null}, "maxVolume": {"type": "number", "refType": null}, "heavyCharges": {"type": "number", "refType": null}, "totalHeavyCharges": {"type": "number", "refType": null}}}, "BoxVo": {"type": "object", "properties": {"boxNum": {"type": "integer", "description": "箱子数量", "format": "int32", "refType": null}, "boxBarCode": {"type": "string", "description": "自定义箱条码", "refType": null}, "boxBarCodeRule": {"type": "string", "description": "箱条码规则", "refType": null}, "systemBoxLength": {"type": "string", "description": "箱子长-系统/客户", "refType": null}, "systemBoxWidth": {"type": "string", "description": "箱子宽-系统/客户", "refType": null}, "systemBoxHeight": {"type": "string", "description": "箱子高-系统/客户", "refType": null}, "systemBoxWeight": {"type": "string", "description": "箱子重量-系统/客户", "refType": null}, "pickBoxLength": {"type": "number", "description": "箱子长-拣货", "refType": null}, "pickBoxWidth": {"type": "number", "description": "箱子宽-拣货", "refType": null}, "pickBoxHeight": {"type": "number", "description": "箱子高-拣货", "refType": null}, "pickBoxWeight": {"type": "number", "description": "箱子重量-拣货", "refType": null}, "thirdBoxLength": {"type": "number", "description": "箱子长-第三方", "refType": null}, "thirdBoxWidth": {"type": "number", "description": "箱子宽-第三方", "refType": null}, "thirdBoxHeight": {"type": "number", "description": "箱子高-第三方", "refType": null}, "thirdBoxWeight": {"type": "number", "description": "箱子重量-第三方", "refType": null}, "commodity": {"type": "array", "description": "商品", "items": {"$ref": "#/components/schemas/CommodityVo"}, "refType": "CommodityVo"}}, "description": "返回数据对象"}, "CommodityVo": {"type": "object", "properties": {"commodityId": {"type": "string", "description": "商品id", "refType": null}, "commodityCount": {"type": "integer", "description": "商品数量", "format": "int32", "refType": null}}, "description": "商品"}, "ResultListBoxVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/BoxVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "FirstVesselTrajectoryVo": {"type": "object", "properties": {"id": {"type": "string", "refType": null}, "vesselId": {"type": "string", "refType": null}, "trackMessage": {"type": "string", "refType": null}, "createTime": {"type": "string", "format": "date-time", "refType": null}}, "description": "返回数据对象"}, "ResultListFirstVesselTrajectoryVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/FirstVesselTrajectoryVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "FirstVesselVo": {"type": "object", "properties": {"id": {"type": "string", "refType": null}, "customer": {"type": "string", "refType": null}, "systemWaybillNumber": {"type": "string", "refType": null}, "customerWaybillNumber": {"type": "string", "refType": null}, "deliveryWarehouse": {"type": "string", "refType": null}, "destinationType": {"type": "string", "refType": null}, "shippingService": {"type": "string", "refType": null}, "destination": {"type": "string", "refType": null}, "declarationMethod": {"type": "string", "refType": null}, "taxationMethod": {"type": "string", "refType": null}, "storageType": {"type": "string", "refType": null}, "purchaseInsurance": {"type": "string", "refType": null}, "customerRemarks": {"type": "string", "refType": null}, "internalRemarks": {"type": "string", "refType": null}, "createUser": {"type": "string", "refType": null}, "updateUser": {"type": "string", "refType": null}, "shipmentId": {"type": "string", "refType": null}, "serviceProvider": {"type": "string", "refType": null}, "sysBoxNum": {"type": "integer", "format": "int32", "refType": null}, "actualBoxNum": {"type": "integer", "format": "int32", "refType": null}, "actualWeight": {"type": "number", "refType": null}, "volumeWeight": {"type": "number", "refType": null}, "dimensional": {"type": "number", "refType": null}, "changeableWeight": {"type": "number", "refType": null}, "cost": {"type": "number", "refType": null}, "track": {"type": "string", "refType": null}, "supplier": {"type": "string", "refType": null}, "trackingNumber": {"type": "string", "refType": null}, "country": {"type": "string", "refType": null}, "commodityName": {"type": "string", "refType": null}, "commodityAttribute": {"type": "string", "refType": null}, "createTime": {"type": "string", "format": "date-time", "refType": null}, "statusTime": {"type": "string", "format": "date-time", "refType": null}, "boxInfo": {"type": "array", "description": "箱子信息", "items": {"$ref": "#/components/schemas/BoxVo"}, "refType": "BoxVo"}}}, "IPageFirstVesselVo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64", "refType": null}, "current": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int64", "deprecated": true, "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/FirstVesselVo"}, "refType": "FirstVesselVo"}}, "description": "返回数据对象"}, "ResultIPageFirstVesselVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/IPageFirstVesselVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultFirstVesselVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/FirstVesselVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "FinanceSettdocManageModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "string", "description": "状态（confirm 待确认、confirmed 已确认、waitpay待付款、paid已付款、debt欠款）", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "relatedNumber": {"type": "string", "description": "关联订单", "refType": null}, "customer": {"type": "array", "description": "客户名称", "items": {"type": "string", "description": "客户名称"}, "refType": "string"}, "customerId": {"type": "array", "description": "客户id", "items": {"type": "string", "description": "客户id"}, "refType": "string"}, "transportService": {"type": "string", "description": "运输服务", "refType": null}, "transportServiceId": {"type": "string", "description": "运输服务id", "refType": null}, "documentNumber": {"type": "string", "description": "结算单据号", "refType": null}, "costType": {"type": "string", "description": "费用类型名称", "refType": null}, "costTypeId": {"type": "string", "description": "费用类型id", "refType": null}, "costUnit": {"type": "string", "description": "费用单位", "refType": null}, "costUnitId": {"type": "string", "description": "费用单位id", "refType": null}, "unitCost": {"type": "string", "description": "单位费用", "refType": null}, "billCharge": {"type": "string", "description": "账单收费重", "refType": null}, "amount": {"type": "string", "description": "金额", "refType": null}, "pendingPaymentAmount": {"type": "string", "description": "待付款金额", "refType": null}, "currency": {"type": "string", "description": "币种", "refType": null}, "costDesc": {"type": "string", "description": "费用描述", "refType": null}, "remarks": {"type": "string", "description": "内部备注", "refType": null}, "paymentStatus": {"type": "string", "description": "出账状态（noDisbursement未出账、disbursement已出账）", "refType": null}, "confirmTime": {"type": "string", "description": "确认时间", "format": "date-time", "refType": null}, "settlementTime": {"type": "string", "description": "结算时间", "format": "date-time", "refType": null}, "disbursementTime": {"type": "string", "description": "出账时间", "format": "date-time", "refType": null}, "paymentTime": {"type": "string", "description": "付款时间", "format": "date-time", "refType": null}, "customerEntity": {"type": "string", "refType": null}, "customerIdEntity": {"type": "string", "refType": null}, "type": {"type": "string", "description": "1头程、2海外仓、3分销商仓库", "refType": null}, "warehouse": {"type": "string", "description": "仓库", "refType": null}, "warehouseId": {"type": "string", "description": "仓库id", "refType": null}, "freezeAmount": {"type": "string", "description": "冻结金额", "refType": null}, "adAmount": {"type": "string", "description": "实扣金额", "refType": null}}}, "FinanceBankAccountModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "subsidiaryName": {"type": "string", "description": "子公司名称", "refType": null}, "currency": {"type": "integer", "description": "币种id", "format": "int32", "refType": null}, "currencyName": {"type": "string", "description": "币种名称", "refType": null}, "bankName": {"type": "string", "description": "开户银行", "refType": null}, "bankNumber": {"type": "string", "description": "银行账号", "refType": null}, "accountName": {"type": "string", "description": "开户名", "refType": null}, "bankAddress": {"type": "string", "description": "开户行地址", "refType": null}, "keywords": {"type": "string", "description": "查询条件-关键字", "refType": null}}}, "FinanceAccountRechargeModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "customerCode": {"type": "string", "description": "客户编码", "refType": null}, "rechargeAccount": {"type": "string", "description": "充值账户", "refType": null}, "rechargeAccountId": {"type": "integer", "description": "充值账户id", "format": "int32", "refType": null}, "rechargeMethod": {"type": "string", "description": "充值方式（1现金，2银行转账）", "refType": null}, "receBank": {"type": "string", "description": "收款银行卡", "refType": null}, "receBankName": {"type": "string", "description": "收款银行名称", "refType": null}, "payBankName": {"type": "string", "description": "付款银行名称", "refType": null}, "payNumber": {"type": "string", "description": "付款流水号", "refType": null}, "rechargeAmount": {"type": "string", "description": "充值金额", "refType": null}, "remarks": {"type": "string", "description": "备注", "refType": null}, "voucherUrl": {"type": "string", "description": "凭证图片路径", "refType": null}, "examineStatus": {"type": "integer", "description": "审核状态（0待审核，1通过，2不通过）", "format": "int32", "refType": null}, "msg": {"type": "string", "description": "审核原因", "refType": null}, "applyStartTime": {"type": "string", "description": "申请开始时间", "format": "date-time", "refType": null}, "applyEndTime": {"type": "string", "description": "申请结束时间", "format": "date-time", "refType": null}, "examineStartTime": {"type": "string", "description": "审核结束时间", "format": "date-time", "refType": null}, "examineEndTime": {"type": "string", "description": "审核结束时间", "format": "date-time", "refType": null}}}, "DictModel": {"type": "object", "properties": {"id": {"type": "string", "refType": null}, "dictName": {"type": "string", "refType": null}, "dictCode": {"type": "string", "refType": null}, "description": {"type": "string", "refType": null}, "createUser": {"type": "string", "refType": null}, "createTime": {"type": "string", "format": "date-time", "refType": null}, "updateTime": {"type": "string", "format": "date-time", "refType": null}}}, "DictItemModel": {"type": "object", "properties": {"id": {"type": "string", "refType": null}, "dictId": {"type": "string", "refType": null}, "itemText": {"type": "string", "refType": null}, "itemValue": {"type": "string", "refType": null}, "description": {"type": "string", "refType": null}, "sortOrder": {"type": "integer", "format": "int32", "refType": null}, "createUser": {"type": "string", "refType": null}, "createTime": {"type": "string", "format": "date-time", "refType": null}}}, "AdjustmentMethodModel": {"type": "object", "properties": {"name": {"type": "string", "refType": null}, "value": {"type": "number", "refType": null}}, "description": "调整方式"}, "CurrencyModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "主键", "refType": null}, "currencyName": {"type": "string", "description": "币种名称", "refType": null}, "currencyCode": {"type": "string", "description": "币种编码", "refType": null}, "baseCurrencyName": {"type": "string", "description": "基准币种名称", "refType": null}, "baseCurrencyCode": {"type": "string", "description": "基准币种编码", "refType": null}, "officialExchangeRate": {"type": "number", "description": "官方汇率", "refType": null}, "customExchangeRate": {"type": "number", "description": "自定义汇率", "refType": null}, "exchangeRateUpdateTime": {"type": "string", "description": "汇率更新时间", "format": "date-time", "refType": null}, "status": {"type": "integer", "description": "状态 禁用：0 启用：1", "format": "int32", "refType": null}, "customExchangeValue": {"type": "number", "description": "自定义值", "refType": null}, "adjustmentMethods": {"type": "array", "description": "调整方式", "items": {"$ref": "#/components/schemas/AdjustmentMethodModel"}, "refType": "AdjustmentMethodModel"}, "ids": {"type": "array", "description": "id集合", "items": {"type": "string", "description": "id集合"}, "refType": "string"}, "startTime": {"type": "string", "description": "开始时间", "format": "date-time", "refType": null}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "refType": null}, "currencyCodes": {"type": "array", "description": "币种编码集合", "items": {"type": "string", "description": "币种编码集合"}, "refType": "string"}}}, "AdjustmentMethodVo": {"type": "object", "properties": {"name": {"type": "string", "refType": null}, "value": {"type": "string", "refType": null}}, "description": "调整方式"}, "CurrencyVo": {"type": "object", "properties": {"id": {"type": "string", "description": "主键", "refType": null}, "currencyName": {"type": "string", "description": "币种名称", "refType": null}, "currencyCode": {"type": "string", "description": "币种编码", "refType": null}, "baseCurrencyName": {"type": "string", "description": "基准币种名称", "refType": null}, "baseCurrencyCode": {"type": "string", "description": "基准币种编码", "refType": null}, "officialExchangeRate": {"type": "number", "description": "官方汇率", "refType": null}, "customExchangeRate": {"type": "number", "description": "自定义汇率", "refType": null}, "exchangeRateUpdateTime": {"type": "string", "description": "汇率更新时间", "format": "date-time", "refType": null}, "status": {"type": "integer", "description": "状态 禁用：0 启用：1", "format": "int32", "refType": null}, "statusName": {"type": "string", "description": "状态 禁用：0 启用：1", "refType": null}, "customExchangeValue": {"type": "number", "description": "自定义值", "refType": null}, "adjustmentMethods": {"type": "array", "description": "调整方式", "items": {"$ref": "#/components/schemas/AdjustmentMethodVo"}, "refType": "AdjustmentMethodVo"}}}, "IPageCurrencyVo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64", "refType": null}, "current": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int64", "deprecated": true, "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyVo"}, "refType": "CurrencyVo"}}, "description": "返回数据对象"}, "ResultIPageCurrencyVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/IPageCurrencyVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "CurrencyHistoryRecordVo": {"type": "object", "properties": {"createTime": {"type": "string", "description": "操作时间", "format": "date-time", "refType": null}, "ip": {"type": "string", "description": "ip地址", "refType": null}, "systemName": {"type": "string", "description": "操作系统名称", "refType": null}, "browserName": {"type": "string", "description": "浏览器名称", "refType": null}, "currencyName": {"type": "string", "description": "币种名称", "refType": null}, "currencyCode": {"type": "string", "description": "币种编码", "refType": null}, "baseCurrencyName": {"type": "string", "description": "基准币种名称", "refType": null}, "baseCurrencyCode": {"type": "string", "description": "基准币种编码", "refType": null}, "operator": {"type": "string", "description": "操作人", "refType": null}, "beforeAdjustmentMethods": {"type": "array", "description": "调整前", "items": {"$ref": "#/components/schemas/AdjustmentMethodVo"}, "refType": "AdjustmentMethodVo"}, "afterAdjustmentMethods": {"type": "array", "description": "调整后", "items": {"$ref": "#/components/schemas/AdjustmentMethodVo"}, "refType": "AdjustmentMethodVo"}, "officialExchangeRate": {"type": "number", "description": "官方汇率", "refType": null}, "customExchangeValue": {"type": "number", "description": "自定义值", "refType": null}}}, "IPageCurrencyHistoryRecordVo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64", "refType": null}, "current": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int64", "deprecated": true, "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyHistoryRecordVo"}, "refType": "CurrencyHistoryRecordVo"}}, "description": "返回数据对象"}, "ResultIPageCurrencyHistoryRecordVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/IPageCurrencyHistoryRecordVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "JobInfo": {"type": "object", "properties": {"jobName": {"type": "string", "refType": null}, "cron": {"type": "string", "refType": null}}}, "BaseWarehouseManageModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}, "warehouseName": {"type": "string", "description": "仓库名称", "refType": null}, "warehouseCode": {"type": "string", "description": "仓库代码", "refType": null}, "warehouseType": {"type": "string", "description": "仓库类型", "refType": null}, "contacts": {"type": "string", "description": "联系人", "refType": null}, "contactsPhone": {"type": "string", "description": "联系人电话", "refType": null}, "postalCode": {"type": "string", "description": "邮政编码", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "countryCode": {"type": "string", "description": "国家代码", "refType": null}, "province": {"type": "string", "description": "省/州", "refType": null}, "provinceCode": {"type": "string", "description": "省/州代码", "refType": null}, "city": {"type": "string", "description": "市", "refType": null}, "cityCode": {"type": "string", "description": "市代码", "refType": null}, "detailedAddressOne": {"type": "string", "description": "详细地址1", "refType": null}, "detailedAddressTwo": {"type": "string", "description": "详细地址2", "refType": null}, "belongEnterprise": {"type": "string", "description": "仓库服务商", "refType": null}, "labelSource": {"type": "string", "description": "标签来源", "refType": null}, "warehousePartners": {"type": "string", "description": "仓库合作商", "refType": null}}}, "BaseServiceProviderManageModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "serviceProviderName": {"type": "string", "description": "服务商名称", "refType": null}, "serviceProviderAbbr": {"type": "string", "description": "服务商简称", "refType": null}, "serviceProviderCode": {"type": "string", "description": "服务商代码", "refType": null}, "serviceProviderType": {"type": "string", "description": "服务商类型", "refType": null}, "contacts": {"type": "string", "description": "联系人", "refType": null}, "contactsPhone": {"type": "string", "description": "联系人电话", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "countryCode": {"type": "string", "description": "国家代码", "refType": null}, "province": {"type": "string", "description": "省/州", "refType": null}, "provinceCode": {"type": "string", "description": "省/州代码", "refType": null}, "city": {"type": "string", "description": "市", "refType": null}, "cityCode": {"type": "string", "description": "市代码", "refType": null}, "detailedAddressOne": {"type": "string", "description": "详细地址1", "refType": null}, "detailedAddressTwo": {"type": "string", "description": "详细地址2", "refType": null}, "postalCode": {"type": "string", "description": "邮政编码", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}}}, "BaseLocationManageModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "refType": null}, "locationName": {"type": "string", "description": "地点名称", "refType": null}, "locationAbbr": {"type": "string", "description": "地点简称", "refType": null}, "locationAttr": {"type": "string", "description": "地点归属", "refType": null}, "locationType": {"type": "string", "description": "地点类型", "refType": null}, "locationTypeName": {"type": "string", "description": "地点类型名称", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "countryCode": {"type": "string", "description": "国家代码", "refType": null}, "province": {"type": "string", "description": "省/州", "refType": null}, "provinceCode": {"type": "string", "description": "省/州代码", "refType": null}, "city": {"type": "string", "description": "市", "refType": null}, "cityCode": {"type": "string", "description": "市代码", "refType": null}, "detailedAddressOne": {"type": "string", "description": "详细地址1", "refType": null}, "detailedAddressTwo": {"type": "string", "description": "详细地址2", "refType": null}, "postalCode": {"type": "string", "description": "邮政编码", "refType": null}, "contacts": {"type": "string", "description": "联系人", "refType": null}, "contactsPhone": {"type": "string", "description": "联系人电话", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "createBy": {"type": "string", "description": "创建人", "refType": null}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time", "refType": null}, "updateBy": {"type": "string", "description": "修改人", "refType": null}, "status": {"type": "integer", "description": "状态（1启用，0停用）", "format": "int32", "refType": null}, "delStatus": {"type": "integer", "description": "删除状态（1正常，0删除）", "format": "int32", "refType": null}}}, "BaseLoginModel": {"type": "object", "properties": {"username": {"type": "string", "description": "登陆账号", "example": "admin", "refType": null}, "password": {"type": "string", "description": "登陆口令", "example": "Passw0rd@!", "refType": null}, "captcha": {"type": "string", "description": "验证码", "example": "2587", "refType": null}, "checkKey": {"type": "string", "description": "验证码key", "example": "123", "refType": null}, "type": {"type": "string", "description": "认证类型", "example": "base", "default": "base", "refType": null}}}, "AuthenticationResponse": {"type": "object", "properties": {"appId": {"type": "string", "description": "登陆账号标识", "example": "voc", "refType": null}, "type": {"type": "string", "description": "登陆账号标识", "example": "base", "refType": null}, "username": {"type": "string", "description": "登陆账号", "example": "admin", "refType": null}, "userid": {"type": "string", "description": "登陆账号标识", "example": "1", "refType": null}, "access_token": {"type": "string", "description": "登陆账号TOKEN", "example": "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMSIsImlkZW50aXR5X3R5cGUiOiJiYXNlIiwiYXBwX2lkIjoiaW5zaWdodHMiLCJ1c2VybmFtZSI6IklDeTMvdUhmMGJUMDdoUVFYaUFPd1hSUEY2cC9nbEJWd0NTWno2MUlhSC9LNUIvdDRzc29jeEI2dEpoUWhRZEYiLCJzdWIiOiIxIiwiaWF0IjoxNzA5NTQwNjc4LCJleHAiOjE3MTIxMzI2Nzh9.zMidSM2L7wD4NFWqwwjJ5XdjUT5jo7GwqeccSsYAt2c", "refType": null}}, "description": "返回数据对象"}, "ResultAuthenticationResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/AuthenticationResponse"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListSellerVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/SellerVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "SellerVo": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "refType": null}, "companyName": {"type": "string", "description": "公司名称", "refType": null}, "companyCode": {"type": "string", "description": "公司编码", "refType": null}, "companyAbbr": {"type": "string", "description": "公司简称", "refType": null}, "creditCode": {"type": "string", "description": "统一信用代码", "refType": null}, "legalPersonId": {"type": "string", "description": "法人证件号", "refType": null}, "legalPersonName": {"type": "string", "description": "法人代表名称", "refType": null}, "businessLicense": {"type": "string", "description": "营业执照", "refType": null}, "legalPersonIdCard": {"type": "string", "description": "法人身份证", "refType": null}, "companyPhone": {"type": "string", "description": "公司座机", "refType": null}, "email": {"type": "string", "description": "邮箱", "refType": null}, "country": {"type": "string", "description": "国家", "refType": null}, "detailedAddressOne": {"type": "string", "description": "详细地址1", "refType": null}, "detailedAddressTwo": {"type": "string", "description": "详细地址2", "refType": null}, "registerPlaceId": {"type": "string", "description": "注册地", "refType": null}, "contactPlaceId": {"type": "string", "description": "联系地", "refType": null}, "contactsName": {"type": "string", "description": "联系人姓名", "refType": null}, "contactsPhone": {"type": "string", "description": "联系人电话", "refType": null}, "contactsIdCard": {"type": "string", "description": "联系人证件号", "refType": null}, "contactsPosition": {"type": "string", "description": "联系人职位", "refType": null}, "status": {"type": "string", "description": "状态 0 禁用 1 启用", "refType": null}, "statusText": {"type": "string", "description": "状态 0 禁用 1 启用", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "loginTime": {"type": "string", "description": "登录时间", "format": "date-time", "refType": null}, "leg": {"type": "array", "description": "头程金额", "items": {"type": "string", "description": "头程金额"}, "refType": "string"}, "overseasPosition": {"type": "array", "description": "海外仓金额", "items": {"type": "string", "description": "海外仓金额"}, "refType": "string"}}, "description": "返回数据对象"}, "ResultFirstVesselSettingVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/FirstVesselSettingVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListFirstVesselSettingVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/FirstVesselSettingVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListCurrencyVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/CurrencyVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}}}, "x-openapi": {"x-setting": {"customCode": 200, "language": "zh-CN", "enableSwaggerModels": true, "swaggerModelName": "实体类列表", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "", "enableDynamicParameter": true, "enableDebug": true, "enableFooter": true, "enableFooterCustom": false, "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "enableGroup": true, "enableResponseCode": true}, "x-markdownFiles": []}}