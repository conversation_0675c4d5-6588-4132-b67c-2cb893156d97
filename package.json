{"name": "v3ep", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build": "vue-tsc -b && vite build --mode prod", "preview": "vite preview", "lint:fix": "eslint src --fix", "lint": "eslint src"}, "dependencies": {"axios": "^1.8.2", "crypto-es": "^2.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.6", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "vue": "^3.5.13", "vue-i18n": "^11.1.2", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@iconify/json": "^2.2.319", "@intlify/unplugin-vue-i18n": "^6.0.5", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/qs": "^6.9.18", "@unocss/preset-icons": "66.1.0-beta.6", "@unocss/preset-rem-to-px": "66.1.0-beta.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "prettier": "^3.5.3", "sass": "^1.85.1", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "unocss": "66.1.0-beta.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}