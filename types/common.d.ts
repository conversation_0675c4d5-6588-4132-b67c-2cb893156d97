/**
 * @description: 卖家(客户)
 * @return {*}
 */
declare interface CompanyInfo {
  id?: string
  companyName?: string
  companyCode?: string
  companyAbbr?: string
  creditCode?: string
  legalPersonId?: string
  legalPersonName?: string
  businessLicense?: string
  legalPersonIdCard?: string
  companyPhone?: string
  email?: string
  country?: string
  detailedAddressOne?: string
  detailedAddressTwo?: string
  registerPlaceId?: string
  contactPlaceId?: string
  contactsName?: string
  contactsPhone?: string
  contactsIdCard?: string
  contactsPosition?: string
  status?: string
  statusText?: string
  createTime?: string
  loginTime?: string
  leg?: any[]
  overseasPosition?: any[]
}

declare interface BaseOption {
  code: string
  name: string
}
