import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginVue from "eslint-plugin-vue";
import { defineConfig } from "eslint/config";
import eslintConfigPrettier from 'eslint-config-prettier'

import { readFile } from 'node:fs/promises'
/**
 * 由于安装了autoimport 插件，所以，需要引入.eslintrc-auto-import.json 来完善eslint以免不必要的报错
 * 如果没有使用autoimport ，就不需要引入了
 * @description: 
 * @return {*}
 */
const autoImportFile = new URL('./.eslintrc-auto-import.json', import.meta.url)
const autoImportGlobals = JSON.parse(await readFile(autoImportFile, 'utf8'))


export default defineConfig([
  {
    files: ["**/*.{js,mjs,cjs,ts,vue}"],
    plugins: { js },
    extends: ["js/recommended"]
  },
  {
    files: ["**/*.{js,mjs,cjs,ts,vue}"],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportGlobals.globals,
        // vant中未声明的函数需要在这里声明
        showToast: 'readonly',
      }
    }
  },
  js.configs.recommended,
  tseslint.configs.recommended,
  pluginVue.configs["flat/essential"],
  eslintConfigPrettier,
  {
    files: ["**/*.vue"],
    languageOptions: {
      parserOptions: {
        parser: tseslint.parser
      }
    }
  },
  {
    rules: {
      // eslint（https://eslint.bootcss.com/docs/rules/）
      // 'no-var': 'off', // 要求使用 let 或 const 而不是 var
      // 'no-multiple-empty-lines': ['off', { max: 1 }], // 不允许多个空行
      // 'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      // 'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      // 'no-unexpected-multiline': 'off', // 禁止空余的多行
      // 'no-useless-escape': 'off', // 禁止不必要的转义字符
      // 'prefer-const': 'off', // 关闭没有使用const的报错

      // typeScript (https://typescript-eslint.io/rules)
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      // '@typescript-eslint/no-unused-vars': 'off', // 禁止定义未使用的变量
      // '@typescript-eslint/no-multiple-empty-lines': 'off', // 禁止定义未使用的变量
      // '@typescript-eslint/no-var': 'off', // 禁止定义未使用的变量
      // '@typescript-eslint/prefer-ts-expect-error': 'error', // 禁止使用 @ts-ignore
      // '@typescript-eslint/prefer-const': 'off', // 关闭没有使用const的报错
      // '@typescript-eslint/no-explicit-any': 'off', // 禁止使用 any 类型
      // '@typescript-eslint/no-non-null-assertion': 'off',
      // '@typescript-eslint/no-namespace': 'off', // 禁止使用自定义 TypeScript 模块和命名空间。
      // '@typescript-eslint/semi': 'off',

      // eslint-plugin-vue (https://eslint.vuejs.org/rules/)
      'vue/multi-word-component-names': 'off', // 要求组件名称始终为 “-” 链接的单词
      // 'vue/script-setup-uses-vars': 'off', // 防止<script setup>使用的变量<template>被标记为未使用
      // 'vue/no-mutating-props': 'off', // 不允许组件 prop的改变
      // 'vue/attribute-hyphenation': 'off', // 对模板中的自定义组件强制执行属性命名样式
    },
  },
  {
    files: ["**/*.vue", "**/*.ts"],
    rules: {
      "no-undef": "off"
    }
  }
]);