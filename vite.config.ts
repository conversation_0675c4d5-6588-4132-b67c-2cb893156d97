import { ConfigEnv, loadEnv, UserConfig } from 'vite'
// import path from 'path'
import vue from '@vitejs/plugin-vue'
import VueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import UnoCSS from 'unocss/vite'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { resolve } from 'path'

// https://vite.dev/config/
const root = process.cwd()

export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv(
      process.argv[3] === '--mode' ? process.argv[4] : process.argv[3],
      root
    )
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: env.VITE_BASE_PATH,
    plugins: [
      vue(),
      VueJsx(),
      AutoImport({
        // 自动导入vue相关函数 vue-router pinia相关函数
        imports: ['vue', 'vue-router'],
        resolvers: [ElementPlusResolver()],
        dts: 'types/auto-imports.d.ts'
      }),
      // 自动导入组件
      Components({
        resolvers: [ElementPlusResolver()],
        dts: 'types/components.d.ts'
        // 指定自动导入组件位置，默认是src/components
        // dirs: ['src/components'],
        // 解决命名冲突
        // directoryAsNamespace: true,
      }),
      UnoCSS(),
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        include: [resolve(__dirname, 'src/locales/**')]
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@views': resolve(__dirname, './src/views')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 全局引入scss变量
          additionalData: `@use "@/styles/variables.module.scss" as *;`
          // javascriptEnabled: true
        }
      }
    },
    esbuild: {
      pure: env.VITE_DROP_CONSOLE === 'true' ? ['console.log'] : undefined,
      drop: env.VITE_DROP_DEBUGGER === 'true' ? ['debugger'] : undefined
    },
    build: {
      // 打包文件超过1M 警告提示
      chunkSizeWarningLimit: 1000,
      target: 'es2015',
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: env.VITE_SOURCEMAP === 'true',
      rollupOptions: {
        // 拆包
        output: {
          manualChunks: {
            'vue-chunks': ['vue', 'vue-router', 'pinia', 'vue-i18n'],
            'element-plus': ['element-plus']
          }
        }
      },
      cssCodeSplit: !(env.VITE_USE_CSS_SPLIT === 'false'),
      cssTarget: ['chrome31']
    },
    server: {
      port: 5175,
      proxy: {
        // 选项写法
        '/api': {
          target: 'http://**************:80/',
          changeOrigin: true
          // rewrite: path => path.replace(/^\/api/, '/api')
        }
      },
      hmr: {
        overlay: false
      },
      host: '0.0.0.0'
    },
    // 预加载
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'element-plus/es/locale/lang/zh-cn',
        'element-plus/es/locale/lang/en',
        '@iconify/iconify',
        'axios',
        'dayjs'
      ]
    }
  }
}

// export default defineConfig()
