# Vue 3 + TypeScript + Vite

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about the recommended Project Setup and IDE Support in the [Vue Docs TypeScript Guide](https://vuejs.org/guide/typescript/overview.html#project-setup).

### element-plue 结合iconify使用图标

```vue
<h2>el-button+图标:</h2>
<el-button type="primary">
  <el-icon> <i-ep-edit /> </el-icon> 新增11
</el-button>
<el-button type="primary" icon="i-ep:credit-card"> 新增1122221 </el-button>
<el-button type="primary" icon="i-ep:monitor"> 新增 </el-button>
<el-button type="primary" icon="i-ep-credit-card"> 新增 </el-button>
<el-button type="primary" icon="i-ep-edit"> 新增 </el-button>
<el-button type="primary" icon="i-ri:home-2-line"> 新增 </el-button>
<div class="i-ri:4k-fill"></div>
<div class="i-ri:home-2-line"></div>
```
