---
type: "always_apply"
---

# Vue3 项目开发规范

## 强制规则

1. **优先使用项目内资源**: 优先使用本项目的 hooks、组件、CSS 类
2. **逻辑提取**: 所有 JS/TS 逻辑不允许写在行内，全部提取成函数
3. **类型安全**: 避免使用 `any` 类型，必须定义明确的类型

## 技术栈

本项目基于以下技术栈开发：

- **框架**: Vue 3.5.13 + TypeScript 5.7.2
- **构建工具**: Vite 6.2.0
- **UI 组件库**: Element Plus 2.9.6
- **状态管理**: Pinia 3.0.1
- **路由**: Vue Router 4.5.0
- **HTTP 请求**: Axios 1.8.2
- **国际化**: Vue I18n 11.1.2
- **样式处理**: SCSS + UnoCSS
- **代码规范**: ESLint + Prettier

## 项目结构

```
src/
├── api/            # API 接口定义（按业务模块组织）
├── assets/         # 静态资源（图片、图标等）
├── axios/          # Axios 配置和拦截器
├── components/     # 公共组件
├── constants/      # 常量定义
├── hooks/          # 自定义 Hooks
├── layout/         # 布局组件
├── locales/        # 国际化语言包
├── plugins/        # 插件配置
├── router/         # 路由配置
├── store/          # Pinia 状态管理
├── styles/         # 全局样式
├── utils/          # 工具函数
├── views/          # 页面视图（按业务模块组织）
├── App.vue         # 根组件
├── main.ts         # 应用入口
└── permission.ts   # 权限控制
```

## 组件开发规范

### 组件命名与结构

1. **公共组件**: 使用 PascalCase，放置在 `src/components` 目录
2. **业务组件**: 放置在对应业务模块的 `components` 目录下
3. **组件目录结构**:
   ```
   ComponentName/
   ├── index.vue      # 组件入口文件
   ├── components/    # 子组件（可选）
   └── hooks/         # 组件相关 hooks（可选）
   ```

### Vue 组件结构

使用 `<script setup>` 语法，组件内部按以下顺序组织：

```vue
<script setup lang="ts">
// 1. 导入语句
import { ... } from '...'

// 2. 组件选项
defineOptions({
  name: 'ComponentName'
})

// 3. Props 和 Emits 定义
const props = defineProps<{...}>()
const emit = defineEmits(['...'])

// 4. 响应式数据
const data = ref(...)
const computedValue = computed(() => ...)

// 5. 生命周期钩子
onMounted(() => {
  // ...
})

// 6. 方法定义
const handleEvent = () => {
  // ...
}

// 7. 暴露给父组件的属性/方法
defineExpose({
  // ...
})
</script>

<template>
  <!-- 模板内容 -->
</template>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

## 样式规范

1. **SCSS**: 组件样式使用 SCSS 预处理器
2. **作用域**: 默认使用 `scoped` 属性限制样式作用域
3. **UnoCSS**: 优先使用 UnoCSS 原子化类（如 `flex`, `items-center`, `mb-20`）
4. **全局样式**: 放置在 `src/styles` 目录

## 自动导入配置

项目已配置自动导入，无需手动导入以下内容：

- **Vue API**: `ref`, `reactive`, `computed`, `watch` 等
- **Vue Router**: `useRouter`, `useRoute` 等
- **Element Plus**: 所有组件自动导入
- **全局类型**: 在 `types/global.d.ts` 中定义的类型

## 路径别名

- `@`: 指向 `src` 目录
- `@components`: 指向 `src/components` 目录
- `@views`: 指向 `src/views` 目录

## 开发命令

- **开发环境**: `pnpm dev`
- **生产构建**: `pnpm build`
- **代码检查**: `pnpm lint`
- **代码修复**: `pnpm lint:fix`

## Git 提交规范

使用约定式提交规范 (Conventional Commits)：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码样式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建系统变更
- `ci`: CI配置变更
- `chore`: 其他变更

### 示例

```
feat(用户模块): 添加用户登录功能

- 实现了用户名密码登录
- 添加了记住密码功能

Closes #123
```
