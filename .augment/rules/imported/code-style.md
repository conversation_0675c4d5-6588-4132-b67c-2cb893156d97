---
type: 'always_apply'
---

# 代码风格规范

## 文件命名

1. **Vue 组件文件**: 使用 PascalCase (如 `UserProfile.vue`)
2. **工具/Hook 文件**: 使用 camelCase (如 `useUserInfo.ts`)
3. **类型定义文件**: 使用 camelCase (如 `userTypes.ts`)
4. **常量文件**: 使用 kebab-case (如 `user-constants.ts`)

## TypeScript 规范

1. **类型定义**: 优先使用 interface 而非 type
2. **函数参数与返回值**: 必须定义明确类型
3. **避免使用 any**: 如必须使用，添加注释说明原因
4. **类型导出**: 统一放在 `types/global.d.ts` 或对应模块的类型文件中

### 类型定义示例

```ts
// 推荐：使用 interface
interface UserInfo {
  id: string
  name: string
  email: string
}

// 函数类型定义
const getUserInfo = async (id: string): Promise<UserInfo> => {
  // 实现逻辑
}

// 组件 Props 类型
interface ComponentProps {
  title: string
  visible?: boolean
  onClose?: () => void
}
```

## 样式规范

1. **SCSS**: 组件样式使用 SCSS 预处理器
2. **作用域**: 默认使用 `scoped` 属性限制样式作用域
3. **UnoCSS 优先**: 优先使用 UnoCSS 原子化类
4. **全局样式**: 放置在 `src/styles` 目录

### UnoCSS 使用示例

```vue
<template>
  <!-- 推荐：使用 UnoCSS 原子化类 -->
  <div class="flex items-center justify-between p-20 mb-16">
    <span class="text-16 font-bold color-#333">标题</span>
    <el-button class="ml-auto">操作</el-button>
  </div>
</template>

<style lang="scss" scoped>
/* 仅在 UnoCSS 无法满足时使用自定义样式 */
.custom-style {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}
</style>
```

## API 请求规范

1. **API 定义**: 统一放置在 `src/api` 目录下，按业务模块组织
2. **返回值类型**: 定义清晰的请求返回值类型
3. **错误处理**: 统一使用 try/catch 处理异常

### API 定义示例

```ts
// src/api/user.ts
export interface UserListParams {
  page: number
  size: number
  keyword?: string
}

export const getUserList = async (
  params: UserListParams
): Promise<BasePage<UserInfo>> => {
  try {
    const { result } = await request.get('/api/user/list', { params })
    return result
  } catch (error) {
    console.error('获取用户列表失败:', error)
    throw error
  }
}
```

## 注释规范

1. **方法注释**: 复杂方法必须添加注释说明功能、参数和返回值
2. **类型注释**: 复杂类型定义添加注释说明各字段含义
3. **TODO/FIXME**: 使用 TODO/FIXME 标记需要改进的代码

### 注释示例

```ts
/**
 * 处理用户数据转换
 * @param rawData 原始用户数据
 * @param options 转换选项
 * @returns 转换后的用户信息
 */
const transformUserData = (
  rawData: any,
  options: TransformOptions
): UserInfo => {
  // TODO: 优化数据转换逻辑
  // FIXME: 处理空值情况
  return {
    id: rawData.id,
    name: rawData.userName || '未知用户',
    email: rawData.email
  }
}
```
