---
type: 'agent_requested'
---

# 组件开发补充规范

## 组件引用方式

### 公共组件引用

```ts
// 推荐：使用路径别名
import ComponentName from '@/components/ComponentName'
import ComponentName from '@/components/ComponentName/index.vue'

// 自动导入的组件无需手动导入（Element Plus 组件）
// <el-button> 等组件会自动导入
```

### 业务组件引用

```ts
// 相对路径引用同模块组件
import LocalComponent from './components/LocalComponent.vue'

// 跨模块引用
import OtherComponent from '@/views/OtherModule/components/OtherComponent.vue'
```

## 组件迁移规范

当需要将业务组件提升为公共组件时：

1. 在 `src/components` 目录下创建以组件名命名的目录
2. 将组件文件重命名为 `index.vue` 并移动到新目录
3. 更新所有引用该组件的文件，修改导入路径
4. 确保组件的通用性，移除业务特定逻辑

### 迁移示例

```ts
// 迁移前：业务组件
import AddProduct from './components/AddProduct.vue'

// 迁移后：公共组件
import AddProduct from '@/components/AddProduct'
```

## 组件复用原则

1. **优先使用现有公共组件**: 避免重复开发相似功能的组件
2. **组件职责单一**: 每个组件只负责一个明确的功能
3. **Props 设计合理**: 提供必要的配置选项，保持 API 简洁
4. **事件命名规范**: 使用动词形式，如 `change`、`click`、`submit`
