---
description: 
globs: 
alwaysApply: true
---
# Git 工作流规范

## 分支管理

1. **主分支**:
   - `main`: 主分支，用于生产环境
   - `dev`: 开发分支，用于测试环境

2. **功能分支**:
   - 命名规范: `feature/功能名称`
   - 从 `dev` 分支创建
   - 完成后合并回 `dev` 分支

3. **修复分支**:
   - 命名规范: `hotfix/bug描述`
   - 紧急修复从 `main` 分支创建
   - 非紧急修复从 `dev` 分支创建

## 提交规范

使用约定式提交规范 (Conventional Commits)，提交信息格式如下:

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码样式调整，不影响功能
- `refactor`: 代码重构，不新增功能也不修复bug
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建系统或外部依赖变更
- `ci`: CI配置变更
- `chore`: 其他不修改src或测试文件的变更
- `revert`: 回退之前的提交

### 示例

```
feat(用户模块): 添加用户登录功能

- 实现了用户名密码登录
- 添加了记住密码功能

Closes #123
```

## 代码审查 (Code Review)

1. **提交前自查**:
   - 确保代码风格符合规范
   - 运行 `pnpm lint` 检查代码问题
   - 确保所有测试通过

2. **Pull Request 流程**:
   - 创建功能分支并完成开发
   - 提交 Pull Request 到目标分支
   - 指定至少一名审查者
   - 通过审查后合并到目标分支

3. **Code Review 要点**:
   - 代码逻辑是否正确
   - 是否符合代码规范
   - 是否有潜在的性能问题
   - 是否有足够的测试覆盖
