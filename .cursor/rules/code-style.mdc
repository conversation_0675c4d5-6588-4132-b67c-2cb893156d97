---
description: 
globs: 
alwaysApply: true
---
# 代码风格规范

## 文件命名

1. **Vue 组件文件**: 使用 PascalCase (如 `UserProfile.vue`)
2. **工具/Hook 文件**: 使用 camelCase (如 `useUserInfo.ts`)
3. **类型定义文件**: 使用 camelCase (如 `userTypes.ts`)
4. **常量文件**: 使用 kebab-case (如 `user-constants.ts`)

## TypeScript 规范

1. **类型定义**: 尽量使用 interface 而非 type
2. **函数参数与返回值**: 必须定义类型
3. **避免使用 any**: 如必须使用，添加注释说明原因
4. **类型导出**: 统一放在对应模块的 types.ts 文件中

## Vue 组件规范

使用 `<script setup>` 语法，组件内部结构按以下顺序组织：

```vue
<script setup lang="ts">
// 1. 导入语句
import { ... } from '...'

// 2. 组件选项
defineOptions({
  name: 'ComponentName'
})

// 3. Props 和 Emits 定义
const props = defineProps<{...}>()
const emit = defineEmits(['...'])

// 4. 响应式数据
const data = ref(...)
const computedValue = computed(() => ...)

// 5. 生命周期钩子
onMounted(() => {
  // ...
})

// 6. 方法定义
const handleEvent = () => {
  // ...
}

// 7. 暴露给父组件的属性/方法
defineExpose({
  // ...
})
</script>
```

## 样式规范

1. **使用 SCSS**: 组件样式使用 SCSS 预处理器
2. **作用域**: 默认使用 `scoped` 属性限制样式作用域
3. **全局样式**: 放置在 [src/styles](mdc:src/styles) 目录
4. **原子化类**: 使用 UnoCSS 提供的原子化类

## API 请求规范

1. **API 定义**: 统一放置在 [src/api](mdc:src/api) 目录下
2. **按模块组织**: 每个业务模块创建独立的 API 文件
3. **返回值类型**: 定义清晰的请求返回值类型
4. **错误处理**: 使用 try/catch 或 Promise.catch 处理异常

## 注释规范

1. **方法注释**: 复杂方法必须添加注释说明功能、参数和返回值
2. **类型注释**: 复杂类型定义添加注释说明各字段含义
3. **TODO/FIXME**: 使用 TODO/FIXME 标记需要改进的代码
4. **版权信息**: 重要文件顶部添加版权和作者信息
