---
description: 
globs: *.vue,*.ts,*.tsx
alwaysApply: false
---
# 组件开发规范

## 组件命名

1. 公共组件应放置在 [src/components](mdc:src/components) 目录下
2. 每个组件应创建独立目录，使用 `index.vue` 作为入口文件
3. 组件名称应使用 PascalCase 命名法

## 组件引用

从公共组件库引用组件时，使用以下格式：

```ts
import ComponentName from '@/components/ComponentName'
// 或者明确指定入口文件
import ComponentName from '@/components/ComponentName/index.vue'
```

## 组件结构

Vue组件应遵循以下结构：

```vue
<script setup lang="ts">
// 导入语句
import { ... } from '...'

// 组件选项
defineOptions({
  name: 'ComponentName'
})

// Props和Emits定义
const props = defineProps<{...}>()
const emit = defineEmits(['...'])

// 响应式数据和方法
const data = ref(...)
const methods = () => {...}
</script>

<template>
  <!-- 模板内容 -->
</template>

<style lang="scss" scoped>
/* 样式内容 */
</style>
