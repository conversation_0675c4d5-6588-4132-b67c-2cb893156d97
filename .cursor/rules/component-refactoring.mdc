---
description: 
globs: *.vue,*.ts,*.tsx
alwaysApply: false
---
# 组件重构规范

## 组件迁移

当需要将业务组件提升为公共组件时，遵循以下步骤：

1. 在 [src/components](mdc:src/components) 目录下创建以组件名命名的目录
2. 将组件文件重命名为 `index.vue` 并移动到新目录
3. 更新所有引用该组件的文件，修改导入路径

例如，将 `src/views/Waybill/FirstLeg/components/AddProduct.vue` 迁移到公共组件：

```ts
// 旧的引用方式
import AddProduct from './AddProduct.vue'

// 新的引用方式
import AddProduct from '@/components/AddProduct/index.vue'
```

## 组件复用

已经迁移到公共组件库的组件应当从所有业务模块中引用，避免在业务模块中重复实现相同功能的组件。

最近迁移的组件：
- [AddProduct](mdc:src/components/AddProduct/index.vue): 商品选择添加组件
